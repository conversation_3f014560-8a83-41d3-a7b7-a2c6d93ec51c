import {
  AdminOrder,
  AdminOrderLineItem,
  AdminOrderPreview,
} from "@medusajs/framework/types";
import { Button, Heading, Input, toast } from "@medusajs/ui";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { RouteFocusModal } from "../../../../components/modals/route-focus-modal";
import {
  StackedFocusModal,
  useStackedModal,
} from "../../../../components/modals";
import { useAddItemsToQuote, useUpdateQuoteItem, useUpdateAddedQuoteItem } from "../../../../hooks/api/quotes";
import { ManageItem } from "./manage-item";
import { ManageItemsTable } from "./manage-items-table";

type ManageItemsSectionProps = {
  order: AdminOrder;
  preview: AdminOrderPreview;
};

let addedVariants: string[] = [];

export const ManageItemsSection = ({
  order,
  preview,
}: ManageItemsSectionProps) => {
  const { t } = useTranslation();
  /**
   * STATE
   */
  const { setIsOpen } = useStackedModal();
  const [filterTerm, setFilterTerm] = useState("");

  /*
   * MUTATIONS
   */
  const { mutateAsync: addItems, isPending } = useAddItemsToQuote(preview.id);
  const { mutateAsync: updateQuoteItem } = useUpdateQuoteItem(preview.id);
  const { mutateAsync: updateAddedQuoteItem } = useUpdateAddedQuoteItem(preview.id);

  /**
   * HANDLERS
   */
  const handleAddItems = async () => {
    try {
      if (addedVariants.length === 0) {
        toast.error(t("general.error"));
        return;
      }

      await addItems({
        items: addedVariants.map((variantId) => ({
          variant_id: variantId,
          quantity: 1,
        })),
      });

      // 清空选择
      addedVariants = [];
      setIsOpen("inbound-items", false);

      // 延迟toast以避免渲染警告
      setTimeout(() => {
        toast.success(t("general.success"));
      }, 0);
    } catch (e) {
      console.error('Error adding items:', e);
      setTimeout(() => {
        let errorMessage = e.message || "Failed to add items";

        // 检查是否是库存位置错误
        if (errorMessage.includes("is not associated with any stock location")) {
          errorMessage = t("inventory.reservation.errors.noAvaliableQuantity", "库存位置没有可用数量。");
        }

        toast.error(t("general.error"), {
          description: errorMessage,
        });
      }, 0);
    }
  };

  /**
   * COMPUTED
   */
  const filteredItems = useMemo(() => {
    return preview.items.filter(
      (i) =>
        i.title.toLowerCase().includes(filterTerm) ||
        i.product_title?.toLowerCase().includes(filterTerm)
    ) as AdminOrderLineItem[];
  }, [preview, filterTerm]);

  const originalItemsMap = useMemo(() => {
    return new Map(order.items.map((item) => [item.id, item]));
  }, [order, filterTerm]);

  return (
    <div>
      <div className="mb-3 mt-8 flex items-center justify-between">
        <Heading level="h2">{t("fields.items")}</Heading>

        <div className="flex gap-2">
          <Input
            value={filterTerm}
            onChange={(e) => setFilterTerm(e.target.value)}
            placeholder={t("fields.search")}
            autoComplete="off"
            type="search"
          />

          <StackedFocusModal id="inbound-items">
            <StackedFocusModal.Trigger asChild>
              <Button variant="secondary" size="small">
                {t("actions.addItems")}
              </Button>
            </StackedFocusModal.Trigger>

            <StackedFocusModal.Content>
              <StackedFocusModal.Header />

              <ManageItemsTable
                currencyCode={order.currency_code}
                onSelectionChange={(finalSelection) => {
                  addedVariants = finalSelection;
                }}
              />

              <StackedFocusModal.Footer>
                <div className="flex w-full items-center justify-end gap-x-4">
                  <div className="flex items-center justify-end gap-x-2">
                    <StackedFocusModal.Close asChild>
                      <Button variant="secondary" size="small">
                        {t("actions.cancel")}
                      </Button>
                    </StackedFocusModal.Close>
                    <Button
                      key="submit-button"
                      type="button"
                      variant="primary"
                      size="small"
                      onClick={handleAddItems}
                      isLoading={isPending}
                    >
                      {t("actions.save")}
                    </Button>
                  </div>
                </div>
              </StackedFocusModal.Footer>
            </StackedFocusModal.Content>
          </StackedFocusModal>
        </div>
      </div>

      {filteredItems.map((item) => {
        const originalItem = originalItemsMap.get(item.id);
        return (
          <ManageItem
            key={item.id}
            item={item}
            currencyCode={order.currency_code}
            onQuantityChange={async (itemId, quantity) => {
              try {
                if (!itemId) {
                  setTimeout(() => {
                    toast.error(t("general.error", "Error"), {
                      description: "Item ID is missing",
                    });
                  }, 0);
                  return;
                }

                const isAddedItem = !!item.actions?.find((a) => a.action === "ITEM_ADD");

                if (isAddedItem) {
                  const addAction = item.actions?.find((a) => a.action === "ITEM_ADD");
                  if (addAction) {
                    await updateAddedQuoteItem({
                      actionId: addAction.id,
                      quantity: quantity,
                      unit_price: item.unit_price, // 包含当前单价
                    });
                  }
                } else {
                  await updateQuoteItem({
                    itemId: itemId,
                    quantity: quantity,
                    unit_price: item.unit_price, // 包含当前单价
                  });
                }
                // 延迟toast以避免渲染警告
                setTimeout(() => {
                  toast.success(t("general.success", "Updated successfully"));
                }, 0);
              } catch (error) {
                console.error('Error updating quantity:', error);
                // 延迟toast以避免渲染警告
                setTimeout(() => {
                  toast.error(t("general.error", "Error"), {
                    description: error.message,
                  });
                }, 0);
              }
            }}
            onUnitPriceChange={async (itemId, unitPrice) => {
              try {
                if (!itemId) {
                  setTimeout(() => {
                    toast.error(t("general.error", "Error"), {
                      description: "Item ID is missing",
                    });
                  }, 0);
                  return;
                }

                const isAddedItem = !!item.actions?.find((a) => a.action === "ITEM_ADD");

                if (isAddedItem) {
                  const addAction = item.actions?.find((a) => a.action === "ITEM_ADD");
                  if (addAction) {
                    await updateAddedQuoteItem({
                      actionId: addAction.id,
                      quantity: item.quantity, // 包含当前数量
                      unit_price: unitPrice,
                    });
                  }
                } else {
                  await updateQuoteItem({
                    itemId: itemId,
                    quantity: item.quantity, // 包含当前数量
                    unit_price: unitPrice,
                  });
                }
                // 延迟toast以避免渲染警告
                setTimeout(() => {
                  toast.success(t("general.success", "Updated successfully"));
                }, 0);
              } catch (error) {
                console.error('Error updating unit price:', error);
                // 延迟toast以避免渲染警告
                setTimeout(() => {
                  toast.error(t("general.error", "Error"), {
                    description: error.message,
                  });
                }, 0);
              }
            }}
            onAddItem={(item) => {
              // TODO: Implement add item logic
              console.log('Add item:', item);
            }}
            onRemoveItem={(itemId) => {
              // TODO: Implement remove item logic
              console.log('Remove item:', itemId);
            }}
            onUndoItem={(itemId) => {
              // TODO: Implement undo item logic
              console.log('Undo item:', itemId);
            }}
            onDuplicate={(item) => {
              // TODO: Implement duplicate item logic
              console.log('Duplicate item:', item);
            }}
            isAdded={!!item.actions?.find((a) => a.action === "ITEM_ADD")}
            isModified={!!item.actions?.find((a) => a.action === "ITEM_UPDATE")}
            isRemoved={false} // TODO: Implement proper logic for removed items
          />
        );
      })}

      {filterTerm && !filteredItems.length && (
        <div
          style={{
            background:
              "repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)",
          }}
          className="bg-ui-bg-field mt-4 block h-[56px] w-full rounded-lg border border-dashed"
        />
      )}
    </div>
  );
};