{"$schema": "./$schema.json", "general": {"ascending": "<PERSON><PERSON>", "descending": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "start": "Başla", "end": "Bitiş", "open": "Aç", "close": "Ka<PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON>", "range": "Aralık", "search": "Ara", "of": "<PERSON><PERSON><PERSON><PERSON>", "results": "sonuçlar", "pages": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON>", "is": "olduğunda", "timeline": "Zaman Çizelgesi", "success": "Başarılı", "warning": "Uyarı", "tip": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "select": "Seç", "selected": "Seçildi", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Devre Dışı", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktif", "revoked": "İptal Edildi", "new": "<PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "added": "Eklendi", "removed": "Kaldırıldı", "admin": "Yönetici", "store": "Mağaza", "details": "Detaylar", "items_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "countSelected": "{{count}} se<PERSON><PERSON><PERSON>", "countOfTotalSelected": "{{total}} arasından {{count}} seçildi", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} daha fazla", "areYouSure": "Emin misiniz?", "noRecordsFound": "<PERSON><PERSON>t bulunamadı", "typeToConfirm": "{val} yazın ve onaylayın:", "noResultsTitle": "<PERSON><PERSON><PERSON>", "noResultsMessage": "Filtreleri veya arama so<PERSON><PERSON><PERSON> den<PERSON>in", "noSearchResults": "<PERSON><PERSON> sonucu yok", "noSearchResultsFor": "<0>'{{query}}'</0> için sonuç yok", "noRecordsTitle": "Kayıt Yok", "noRecordsMessage": "Gösterilecek kayıt yok", "unsavedChangesTitle": "Bu formdan çıkmak istediğinizden emin misiniz?", "unsavedChangesDescription": "Kaydedilmemiş değişiklikleriniz kaybolacak.", "includesTaxTooltip": "<PERSON><PERSON> sü<PERSON><PERSON>i fiyatlar vergiler dahil fiyatlardır.", "excludesTaxTooltip": "<PERSON><PERSON> sütundaki fiyatlar vergiler hariç fiyatlardır.", "noMoreData": "<PERSON>ha fazla veri yok"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} anah<PERSON>", "numberOfKeys_other": "{{count}} anah<PERSON>", "drawer": {"header_one": "JSON <0>· {{count}} anahtar</0>", "header_other": "JSON <0>· {{count}} anahtar</0>", "description": "<PERSON><PERSON> nes<PERSON>in JSON verilerini g<PERSON>."}}, "metadata": {"header": "<PERSON><PERSON>", "numberOfKeys_one": "{{count}} anah<PERSON>", "numberOfKeys_other": "{{count}} anah<PERSON>", "edit": {"header": "Meta Verileri Düzenle", "description": "Bu nesnenin meta verilerini düzenleyin.", "successToast": "<PERSON>a veriler ba<PERSON><PERSON><PERSON><PERSON>.", "actions": {"insertRowAbove": "<PERSON><PERSON><PERSON><PERSON>", "insertRowBelow": "<PERSON><PERSON><PERSON>ı<PERSON>", "deleteRow": "Satırı sil"}, "labels": {"key": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, "complexRow": {"label": "Bazı satırlar devre dışı", "description": "<PERSON><PERSON> <PERSON><PERSON>, düzenlenemeyen dizi veya nesneler gibi ilkel olmayan meta veriler içeriyor. Devre dışı satırları düzenlemek için API'yi kullanın.", "tooltip": "<PERSON><PERSON>, <PERSON><PERSON> veri içerdiği iç<PERSON> de<PERSON> dışıdı<PERSON>."}}}, "validation": {"mustBeInt": "<PERSON><PERSON><PERSON> tam sayı olmalıdır.", "mustBePositive": "<PERSON><PERSON><PERSON> pozitif bir sayı olmalıdır."}, "actions": {"save": "<PERSON><PERSON>", "saveAsDraft": "Taslak olarak kaydet", "copy": "Kopyala", "copied": "Kopyalandı", "duplicate": "Çoğalt", "publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "Oluştur", "delete": "Sil", "remove": "Kaldır", "revoke": "İptal Et", "cancel": "İptal", "forceConfirm": "<PERSON><PERSON><PERSON>", "continueEdit": "Düzenlemeye Devam Et", "enable": "Etkinleştir", "disable": "Devre Dışı Bırak", "undo": "<PERSON><PERSON>", "complete": "<PERSON><PERSON><PERSON>", "viewDetails": "Detayları Görüntüle", "back": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "showMore": "<PERSON><PERSON>", "continue": "<PERSON><PERSON>", "continueWithEmail": "E-posta ile <PERSON>", "idCopiedToClipboard": "ID panoya kopyalandı", "addReason": "<PERSON><PERSON><PERSON>", "addNote": "Not Ekle", "reset": "Sıfırla", "confirm": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "select": "Seç", "browse": "<PERSON><PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "hide": "<PERSON><PERSON><PERSON>", "export": "Dışa Aktar", "import": "İçe Aktar", "cannotUndo": "Bu işlem geri alınamaz"}, "operators": {"in": "İçinde"}, "app": {"search": {"label": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, müşteriler ve daha fazlasını arayın.", "allAreas": "<PERSON><PERSON><PERSON>", "navigation": "Navigasyon", "openResult": "<PERSON><PERSON><PERSON>", "showMore": "<PERSON><PERSON>", "placeholder": "Bir şeye atlayın veya bir şey bulun...", "noResultsTitle": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "noResultsMessage": "Aramanıza uyan bir şey b<PERSON>madık.", "emptySearchTitle": "Aramak i<PERSON> ya<PERSON>ın", "emptySearchMessage": "Keşfetmek için bir anahtar kelime veya ifade girin.", "loadMore": "{{count}} <PERSON><PERSON> y<PERSON><PERSON>", "groups": {"all": "<PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Müşteri Grupları", "product": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productVariant": "<PERSON><PERSON><PERSON><PERSON>", "inventory": "<PERSON><PERSON><PERSON>", "reservation": "Rezervasyonlar", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "promotion": "Promosyonlar", "campaign": "Kampanyalar", "priceList": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "region": "<PERSON><PERSON><PERSON><PERSON>", "taxRegion": "<PERSON><PERSON><PERSON>", "returnReason": "<PERSON><PERSON>", "salesChannel": "Satış Kanalları", "productType": "<PERSON><PERSON><PERSON><PERSON>", "productTag": "<PERSON><PERSON><PERSON><PERSON>", "location": "Konumlar", "shippingProfile": "<PERSON><PERSON>", "publishableApiKey": "Yayınlanabilir API Anahtarları", "secretApiKey": "Gizli API Anahtarları", "command": "<PERSON><PERSON><PERSON><PERSON>", "navigation": "Navigasyon"}}, "keyboardShortcuts": {"pageShortcut": "Sayfaya Git", "settingShortcut": "<PERSON><PERSON><PERSON>", "commandShortcut": "<PERSON><PERSON><PERSON><PERSON>", "then": "sonra", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToProducts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCollections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCategories": "<PERSON><PERSON><PERSON>", "goToCustomers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCustomerGroups": "Müşteri Grupları", "goToInventory": "<PERSON><PERSON><PERSON>", "goToReservations": "Rezervasyonlar", "goToPriceLists": "<PERSON><PERSON><PERSON>", "goToPromotions": "Promosyonlar", "goToCampaigns": "Kampanyalar"}, "settings": {"goToSettings": "<PERSON><PERSON><PERSON>", "goToStore": "Mağaza", "goToUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToRegions": "<PERSON><PERSON><PERSON><PERSON>", "goToTaxRegions": "<PERSON><PERSON><PERSON>", "goToSalesChannels": "Satış Kanalları", "goToProductTypes": "<PERSON><PERSON><PERSON><PERSON>", "goToLocations": "Konumlar", "goToPublishableApiKeys": "Yayınlanabilir API Anahtarları", "goToSecretApiKeys": "Gizli API Anahtarları", "goToWorkflows": "İş Akışları", "goToProfile": "Profil", "goToReturnReasons": "<PERSON><PERSON>"}}, "menus": {"user": {"documentation": "Dokümantasyon", "changelog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcuts": "K<PERSON><PERSON>ollar", "profileSettings": "<PERSON><PERSON>", "theme": {"label": "<PERSON><PERSON>", "dark": "Karanlık", "light": "Aydınlık", "system": "Sistem"}}, "store": {"label": "Mağaza", "storeSettings": "Mağaza Ayarları"}, "actions": {"logout": "Çıkış Yap"}}, "nav": {"accessibility": {"title": "Navigasyon", "description": "Gösterge paneli için navigasyon menüsü."}, "common": {"extensions": "<PERSON><PERSON><PERSON><PERSON>"}, "main": {"store": "Mağaza", "storeSettings": "Mağaza Ayarları"}, "settings": {"header": "<PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON>", "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myAccount": "He<PERSON>b<PERSON>m"}}}, "dataGrid": {"columns": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "Görüntülenen sütunların değiştirilmesi devre dışı bırakılmıştır."}, "shortcuts": {"label": "K<PERSON><PERSON>ollar", "commands": {"undo": "<PERSON><PERSON>", "redo": "<PERSON><PERSON>", "copy": "Kopyala", "paste": "Yapıştır", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "clear": "<PERSON><PERSON><PERSON>", "moveUp": "Yukarı Taşı", "moveDown": "Aşağı Taşı", "moveLeft": "Sola Taşı", "moveRight": "Sağa <PERSON>şı", "moveTop": "En Üste Taşı", "moveBottom": "En Alta Taşı", "selectDown": "Aşağı Seç", "selectUp": "Yukarı Seç", "selectColumnDown": "Sütunu Aşağı Seç", "selectColumnUp": "Sütunu Yukarı Seç", "focusToolbar": "<PERSON>ç <PERSON>uğuna Odaklan", "focusCancel": "Odak İptal"}}, "errors": {"fixError": "Hatasını Düzelt", "count_one": "{{count}} hata", "count_other": "{{count}} hata"}}, "filters": {"date": {"today": "<PERSON><PERSON><PERSON><PERSON>", "lastSevenDays": "Son 7 gün", "lastThirtyDays": "Son 30 gün", "lastNinetyDays": "Son 90 gün", "lastTwelveMonths": "Son 12 ay", "custom": "<PERSON><PERSON>", "from": "Başlangıç", "to": "Bitiş", "starting": "Başlangıç", "ending": "Bitiş"}, "compare": {"lessThan": "Küçüktür", "greaterThan": "Büyüktür", "exact": "Tam", "range": "Aralık", "lessThanLabel": "{{value}}'<PERSON>", "greaterThanLabel": "{{value}}'<PERSON> <PERSON><PERSON>", "andLabel": "ve"}, "radio": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "true": "Do<PERSON><PERSON>", "false": "Yanlış"}, "addFilter": "<PERSON><PERSON><PERSON>", "sortLabel": "Sıralama", "filterLabel": "Filtre", "searchLabel": "<PERSON><PERSON>", "sorting": {"alphabeticallyAsc": "A'dan <PERSON>'ye", "alphabeticallyDesc": "<PERSON>'den A'ya", "dateAsc": "<PERSON><PERSON><PERSON> es<PERSON>", "dateDesc": "Eskiye yeniden"}}, "errorBoundary": {"badRequestTitle": "400 - Hatalı İstek", "badRequestMessage": "<PERSON><PERSON><PERSON>, hatalı bir sözdizimi nedeniyle isteği anlayamadı.", "notFoundTitle": "404 - <PERSON><PERSON> ad<PERSON><PERSON> bir sayfa yok", "notFoundMessage": "URL'yi kontrol edin ve tekrar deneyin veya arama ç<PERSON>ğunu kullanarak aradığınız şeyi bulun.", "internalServerErrorTitle": "500 - <PERSON><PERSON><PERSON>", "internalServerErrorMessage": "<PERSON><PERSON><PERSON> beklenmeyen bir hata oluş<PERSON>. Lütfen daha sonra tekrar deneyin.", "defaultTitle": "<PERSON>ir hata o<PERSON>", "defaultMessage": "<PERSON>u <PERSON>fa oluşturulurken beklenmeyen bir hata meydana geldi.", "noMatchMessage": "Aradığınız sayfa mevcut değil.", "backToDashboard": "Gösterge Paneline Dön"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "shippingAddress": {"header": "Teslimat Adresi", "editHeader": "Teslimat Adresini Düzenle", "editLabel": "Teslimat adresi", "label": "Teslimat adresi"}, "billingAddress": {"header": "<PERSON><PERSON>", "editHeader": "Fatura Adresini <PERSON>", "editLabel": "<PERSON><PERSON> ad<PERSON>i", "label": "<PERSON><PERSON> ad<PERSON>i", "sameAsShipping": "Teslimat adresi ile aynı"}, "contactHeading": "İletişim", "locationHeading": "<PERSON><PERSON>"}, "email": {"editHeader": "E-postayı Düzenle", "editLabel": "E-posta", "label": "E-posta"}, "transferOwnership": {"header": "Sahipliği Aktar", "label": "Sahipliği aktar", "details": {"order": "Sipariş detayları", "draft": "Taslak detayları"}, "currentOwner": {"label": "Mevcut sahip", "hint": "<PERSON><PERSON><PERSON><PERSON><PERSON> mevcut sahibi."}, "newOwner": {"label": "<PERSON><PERSON>", "hint": "Siparişin aktarılacağı yeni sahip."}, "validation": {"mustBeDifferent": "<PERSON><PERSON> sahip, mevcut sahipten farklı olmalıdır.", "required": "<PERSON>ni sa<PERSON> zorunludur."}}, "sales_channels": {"availableIn": "<0>{{x}}</0> satış kanalından <1>{{y}}</1> tanesinde mevcut"}, "products": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Satışa başlamak için ilk ürününüzü oluşturun."}, "edit": {"header": "Ürünü <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>ını düzenleyin.", "successToast": "<PERSON><PERSON><PERSON><PERSON> {{title}} b<PERSON><PERSON><PERSON><PERSON><PERSON> gü<PERSON>."}, "create": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> bir ür<PERSON><PERSON>.", "header": "<PERSON><PERSON>", "tabs": {"details": "Detaylar", "organize": "Organize Et", "variants": "<PERSON><PERSON><PERSON><PERSON>", "inventory": "<PERSON><PERSON><PERSON>"}, "errors": {"variants": "Lütfen en az bir varyant seçin.", "options": "Lütfen en az bir seçenek oluşturun.", "uniqueSku": "SKU benzersiz olmalıdır."}, "inventory": {"heading": "<PERSON><PERSON><PERSON>", "label": "Varyantın envanter kitine envanter öğeleri ekleyin.", "itemPlaceholder": "<PERSON><PERSON><PERSON>", "quantityPlaceholder": "<PERSON> i<PERSON>in kaç tane gerektiğini belirtin."}, "variants": {"header": "<PERSON><PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON><PERSON>, bu bir varyantlı ürün", "subHeadingDescription": "İş<PERSON>lenmediğ<PERSON>e, sizin için var<PERSON>lan bir varyant oluşturacağız", "optionTitle": {"placeholder": "<PERSON><PERSON>"}, "optionValues": {"placeholder": "Küçük, Orta, Büyük"}, "productVariants": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON>, varyantların mağazanızdaki sırasını etkileyecektir.", "alert": "Varyantlar oluşturmak için seçenekler ekleyin.", "tip": "İşaretlenmeyen varyantlar oluşturulmayacaktır. Ürün seçeneklerinizdeki varyasyonları bu listeye uyacak şekilde oluşturup düzenleyebilirsiniz."}, "productOptions": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>, <PERSON><PERSON><PERSON><PERSON>, boy<PERSON> vb."}}, "successToast": "<PERSON><PERSON><PERSON><PERSON> {{title}} başarıyla oluşturuldu."}, "export": {"header": "Ürün Listesini Dışa Aktar", "description": "Ürün listesini bir CSV dosyasına dışa aktarın.", "success": {"title": "Dışa aktarma işleminiz işleniyor", "description": "Veri dışa aktarımı birkaç dakika sürebilir. İşlem tamamlandığında size bildireceğiz."}, "filters": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bu görünümü ayarlamak için tablo görünümünde filtreleri uygulayın"}, "columns": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Belirli ihtiyaçları karşılamak için dışa aktarılan verileri özelleştirin"}}, "import": {"header": "Ürün Listesini İçe Aktar", "uploadLabel": "Ürünleri İçe Aktar", "uploadHint": "Bir CSV dosyasını sürükleyip bırakın veya yüklemek için tıklayın", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, önceden tanımlanmış bir formatta bir CSV dosyası sağlayarak içe aktarın", "template": {"title": "Listenizi nasıl düzenleyeceğinizden emin de<PERSON> misiniz?", "description": "Doğru formatı takip ettiğinizden emin olmak için aşağıdaki şablonu indirin."}, "upload": {"title": "Bir CSV dosyası yükleyin", "description": "İçe aktarma yoluyla ürün ekleyebilir veya güncelleyebilirsiniz. Mevcut ürünleri güncellemek için mevcut tutamaç ve ID'yi, mevcut varyantları güncellemek için mevcut ID'yi kullanmanız gerekmektedir. Ürünleri içe aktarmadan önce sizden onay istenecektir.", "preprocessing": "<PERSON><PERSON>...", "productsToCreate": "Ürünler oluşturulacak", "productsToUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON> güncellenecek"}, "success": {"title": "İçe aktarma işleminiz işleniyor", "description": "Veri içe aktarma bir süre alabilir. İşlem tamamlandığında size bildireceğiz."}}, "deleteWarning": "<PERSON><PERSON><PERSON><PERSON> {{title}}'i silmek üzeresiniz. Bu işlem geri alınamaz.", "variants": {"header": "<PERSON><PERSON><PERSON><PERSON>", "empty": {"heading": "Vary<PERSON> yok", "description": "Görüntülenecek varyant yok."}, "filtered": {"heading": "<PERSON><PERSON>ç yok", "description": "Varyantlar mevcut filtrelerle eşleşmiyor."}}, "attributes": "Öznitelikler", "editAttributes": "Öznitelikleri Düzenle", "editOptions": "Seçenekle<PERSON>", "editPrices": "Fiyatları düzenle", "media": {"label": "<PERSON><PERSON><PERSON>", "editHint": "Ürünü mağazanızda sergilemek için medya ekleyin.", "makeThumbnail": "Küçük resim yap", "uploadImagesLabel": "<PERSON><PERSON><PERSON><PERSON>", "uploadImagesHint": "Resimleri buraya sürükleyip bırakın veya yüklemek için tıklayın.", "invalidFileType": "'{{name}}' desteklenmeyen bir dosya türüdür. Desteklenen dosya türleri: {{types}}.", "failedToUpload": "Eklenen medya yüklenemedi. Lütfen tekrar deneyin.", "deleteWarning_one": "{{count}} resmi silmek üzeresiniz. Bu işlem geri alınamaz.", "deleteWarning_other": "{{count}} resmi silmek üzeresiniz. Bu işlem geri alınamaz.", "deleteWarningWithThumbnail_one": "Küçük resim dahil {{count}} resmi silmek üzeresiniz. Bu işlem geri alınamaz.", "deleteWarningWithThumbnail_other": "Küçük resim dahil {{count}} resmi silmek üzeresiniz. Bu işlem geri alınamaz.", "thumbnailTooltip": "Küçük resim", "galleryLabel": "<PERSON><PERSON>", "downloadImageLabel": "Mevcut resmi indir", "deleteImageLabel": "Mevcut resmi sil", "emptyState": {"header": "<PERSON><PERSON><PERSON>z medya yok", "description": "Ürünü mağazanızda sergilemek için medya ekleyin.", "action": "<PERSON><PERSON><PERSON>"}, "successToast": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>."}, "discountableHint": "İşaretlenmediğ<PERSON>e, bu <PERSON><PERSON><PERSON><PERSON> indirim uygulanmayacaktır.", "noSalesChannels": "Hiçbir satış kanalında mevcut değil", "variantCount_one": "{{count}} varyant", "variantCount_other": "{{count}} varyant", "deleteVariantWarning": "Varyant {{title}}'i silmek üzeresiniz. Bu işlem geri alınamaz.", "productStatus": {"draft": "Taslak", "published": "Yayınlandı", "proposed": "Önerildi", "rejected": "Reddedildi"}, "fields": {"title": {"label": "Başlık", "hint": "Ürününüz için kısa ve net bir başlık verin.<0/> Arama motorları için önerilen uzunluk 50-60 karakterdir.", "placeholder": "Kışlık ceket"}, "subtitle": {"label": "Alt Başlık", "placeholder": "Sıcak ve rahat"}, "handle": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON>, ürünü mağazanızda referans almak için kullanılır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ür<PERSON><PERSON> başlığından oluşturulacaktır.", "placeholder": "kışlık-ceket"}, "description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>", "hint": "Ürününüz için kısa ve net bir açıklama verin.<0/> Arama motorları için önerilen uzunluk 120-160 karakterdir.", "placeholder": "Sıcak ve rahat bir ceket"}, "discountable": {"label": "İndirim Uygulanabilir", "hint": "İşaretlenmediğ<PERSON>e, bu <PERSON><PERSON><PERSON><PERSON> indirim uygulanmaz."}, "type": {"label": "<PERSON><PERSON><PERSON>"}, "collection": {"label": "Koleksiyon"}, "categories": {"label": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sales_channels": {"label": "Satış kanalları", "hint": "Bu ürün yalnızca varsayılan satış kanalında mevcut olacaktır."}, "countryOrigin": {"label": "<PERSON><PERSON><PERSON>"}, "material": {"label": "Malzeme"}, "width": {"label": "Genişlik"}, "length": {"label": "Uzunluk"}, "height": {"label": "Yükseklik"}, "weight": {"label": "Ağırlık"}, "options": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, boyutunu vb. tan<PERSON><PERSON><PERSON><PERSON> için kull<PERSON>lı<PERSON>.", "add": "Seçenek ekle", "optionTitle": "Seçenek başlığı", "optionTitlePlaceholder": "Renk", "variations": "Varyasyonlar (virgülle ayrılmış)", "variantionsPlaceholder": "K<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>vi, Yeşil"}, "variants": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "İşaretlenmeyen varyantlar oluşturulmayacaktır. Bu sıralama varyantların ön uçtaki sıralamasını etkiler."}, "mid_code": {"label": "Mid kodu"}, "hs_code": {"label": "HS kodu"}, "shipping_profile": {"label": "<PERSON><PERSON> profili", "hint": "Ürünü bir kargo profili ile ilişkilendirin"}}, "variant": {"edit": {"header": "Varyantı Düzenle", "success": "Ürün <PERSON>ı başarıyla düzenlendi"}, "create": {"header": "<PERSON><PERSON>ant detayları"}, "deleteWarning": "Bu varyantı silmek istediğinizden emin misiniz?", "pricesPagination": "1 - {{current}} / {{total}} fiyatlar", "tableItemAvailable": "{{availableCount}} mevcut", "tableItem_one": "{{availableCount}} ö<PERSON>esi {{locationCount}} konumda mevcut", "tableItem_other": "{{availableCount}} ö<PERSON>esi {{locationCount}} konumda mevcut", "inventory": {"notManaged": "Yönetilmiyor", "manageItems": "<PERSON><PERSON><PERSON>", "notManagedDesc": "Bu varyant için envanter yönetilmiyor. Envanteri izlemek için 'Envanteri Yönet' özelliğini açın.", "manageKit": "<PERSON><PERSON><PERSON>", "navigateToItem": "Envanter öğesine git", "actions": {"inventoryItems": "Envanter öğesine git", "inventoryKit": "<PERSON><PERSON><PERSON>"}, "inventoryKit": "<PERSON><PERSON><PERSON>", "inventoryKitHint": "Bu varyant birkaç envanter öğesinden mi oluşuyor?", "validation": {"itemId": "Lütfen envanter öğesini seçin.", "quantity": "Miktar zorunludur. Lütfen pozitif bir sayı girin."}, "header": "Stok & Envanter", "editItemDetails": "<PERSON>ğe de<PERSON>ylarını düzenle", "manageInventoryLabel": "<PERSON><PERSON><PERSON>", "manageInventoryHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, siparişler ve iadeler oluşturulduğunda envanter miktarını sizin için değiştireceğiz.", "allowBackordersLabel": "<PERSON><PERSON> izin ver", "allowBackordersHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, müşteriler mevcut miktar olmasa bile varyantı satın alabilir.", "toast": {"levelsBatch": "Envanter seviyeleri güncellendi.", "update": "<PERSON><PERSON><PERSON> başar<PERSON><PERSON> güncellendi.", "updateLevel": "Envanter seviyesi başarıyla güncellendi.", "itemsManageSuccess": "<PERSON><PERSON><PERSON> ö<PERSON> başarıyla güncellendi."}}}, "options": {"header": "Seçenekler", "edit": {"header": "Seçeneği Düzenle", "successToast": "Seçenek {{title}} başar<PERSON><PERSON> güncellendi."}, "create": {"header": "Seçenek Oluştur", "successToast": "Seçenek {{title}} başarıyla oluşturuldu."}, "deleteWarning": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>eneğini silmek üzeresiniz: {{title}}. Bu işlem geri alınamaz."}, "organization": {"header": "Organize Et", "edit": {"header": "Organizasyonu <PERSON>", "toasts": {"success": "{{title}}'in organizasyonu başarıyla gü<PERSON>di."}}}, "toasts": {"delete": {"success": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "{{title}} b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "error": {"header": "<PERSON><PERSON><PERSON><PERSON>"}}}, "stock": {"heading": "Ürün stok seviyelerini ve konumlarını yönetin", "description": "Tüm ürün <PERSON>larının stok seviyelerini güncelleyin.", "loading": "Bu biraz zaman alabilir...", "tooltips": {"alreadyManaged": "<PERSON>u envanter <PERSON> zaten {{title}} altında düzenlenebilir.", "alreadyManagedWithSku": "<PERSON>u envanter <PERSON> zaten {{title}} altında düzenlenebilir ({{sku}})."}}, "shippingProfile": {"header": "Kargo yapılandırması", "edit": {"header": "Kargo yapılandırması", "toasts": {"success": "<PERSON>rgo yapılandırması başarıyla güncellendi."}}, "create": {"errors": {"required": "Kargo yapılandırması gereklidir"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Ürünleri koleksiyonlar halinde organize edin.", "createCollection": "Koleksiyon Oluştur", "createCollectionHint": "Ürünlerinizi organize etmek için yeni bir koleksiyon oluşturun.", "createSuccess": "Koleksiyon başarıyla oluşturuldu.", "editCollection": "<PERSON><PERSON>ks<PERSON><PERSON><PERSON>", "handleTooltip": "<PERSON><PERSON><PERSON>, koleksiyonu mağazanızda referans almak için kullanılır. Belirtilmezse, koleksiyon başlığından oluşturulacaktır.", "deleteWarning": "Koleksiyon {{title}}'i silmek üzeresiniz. Bu işlem geri alınamaz.", "removeSingleProductWarning": "Ürünü koleksiyondan kaldırmak üzeresiniz: {{title}}. Bu işlem geri alınamaz.", "removeProductsWarning_one": "{{count}} ürünü koleksiyondan kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "removeProductsWarning_other": "{{count}} ürünü koleksiyondan kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "products": {"list": {"noRecordsMessage": "Koleksiyonda ürün yok."}, "add": {"successToast_one": "Ürün kole<PERSON>iyona başarıyla eklendi.", "successToast_other": "Ürünler koleksiyona başarıyla eklendi."}, "remove": {"successToast_one": "Ürün koleksiyondan başarıyla kaldırıldı.", "successToast_other": "Ürünler koleksiyondan başarıyla kaldırıldı."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Ürünleri kategoriler halinde organize edin ve bu kategorilerin sıralama ve hiyerarşisini yönetin.", "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Ürünlerinizi organize etmek için yeni bir kategori oluşturun.", "tabs": {"details": "Detaylar", "organize": "Sıralamayı Düzenle"}, "successToast": "<PERSON><PERSON><PERSON> {{name}} başarıyla oluşturuldu."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kategoriyi düzenleyerek detaylarını güncelleyin.", "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "delete": {"confirmation": "Kate<PERSON>i {{name}}'i silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON><PERSON> {{name}} b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "products": {"add": {"disabledTooltip": "<PERSON><PERSON><PERSON><PERSON> zaten bu kategoride.", "successToast_one": "{{count}} ü<PERSON><PERSON><PERSON> ka<PERSON>gor<PERSON>ye eklendi.", "successToast_other": "{{count}} ü<PERSON><PERSON><PERSON> ka<PERSON>gor<PERSON>ye eklendi."}, "remove": {"confirmation_one": "{{count}} ürünü kategoriden kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "confirmation_other": "{{count}} ürünü kategoriden kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "successToast_one": "{{count}} ü<PERSON><PERSON><PERSON> kate<PERSON> kaldırıldı.", "successToast_other": "{{count}} ü<PERSON><PERSON><PERSON> kate<PERSON> kaldırıldı."}, "list": {"noRecordsMessage": "Kategoride ürün yok."}}, "organize": {"header": "Organize Et", "action": "Sıralamayı Düzenle"}, "fields": {"visibility": {"label": "Görünürlük", "internal": "<PERSON><PERSON><PERSON>", "public": "<PERSON><PERSON>"}, "status": {"label": "Durum", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>"}, "path": {"label": "Yol", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tam yolunu g<PERSON>."}, "children": {"label": "<PERSON>"}, "new": {"label": "<PERSON><PERSON>"}}}, "inventory": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>er öğelerinizi yönetin", "reserved": "<PERSON><PERSON><PERSON>", "available": "Mevcut", "locationLevels": "Konumlar", "associatedVariants": "İlişkili varyantlar", "manageLocations": "Konumları yönet", "deleteWarning": "Bir envanter öğesini silmek üzeresiniz. Bu işlem geri alınamaz.", "editItemDetails": "<PERSON>ğe de<PERSON>ylarını düzenle", "create": {"title": "<PERSON><PERSON><PERSON>", "details": "Detaylar", "availability": "Mevcutluk", "locations": "Konumlar", "attributes": "Öznitelikler", "requiresShipping": "<PERSON><PERSON>", "requiresShippingHint": "Envanter öğesi kargo gerektiriyor mu?", "successToast": "<PERSON><PERSON><PERSON> ö<PERSON> başarıyla oluşturuldu."}, "reservation": {"header": "{{itemName}} i<PERSON><PERSON>", "editItemDetails": "Rezervasyonu <PERSON>", "lineItemId": "Satır öğesi ID", "orderID": "Sipariş ID", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "location": "<PERSON><PERSON>", "inStockAtLocation": "Bu konumda mevcut", "availableAtLocation": "Bu konumda mevcut", "reservedAtLocation": "<PERSON>u konumda rezerve edildi", "reservedAmount": "Rezerve edilecek miktar", "create": "Rezervasyon Oluştur", "itemToReserve": "Rezerve edilecek öğe", "quantityPlaceholder": "Ne kadar rezerve etmek istiyorsunuz?", "descriptionPlaceholder": "Bu rezervasyon türü nedir?", "successToast": "Rezervasyon başarıyla oluşturuldu.", "updateSuccessToast": "Rezervasyon başarıyla güncellendi.", "deleteSuccessToast": "Rezervasyon baş<PERSON><PERSON><PERSON>.", "errors": {"noAvaliableQuantity": "Stok konumunda mevcut miktar yok.", "quantityOutOfRange": "Minimum miktar 1 ve maksimum miktar {{max}}"}}, "toast": {"updateLocations": "Konumlar başarıyla güncellendi.", "updateLevel": "Envanter seviyesi başarıyla güncellendi.", "updateItem": "<PERSON><PERSON><PERSON> başar<PERSON><PERSON> güncellendi."}, "adjustInventory": {"errors": {"stockedQuantity": "<PERSON>ok miktarı, rezerve edilen miktar olan {{quantity}}'den daha az olarak güncellenemez."}}, "stock": {"title": "Stok Yönetimi", "description": "Ürün stok seviyelerini ve konumlarını yönetin", "action": "Stok seviyelerini düzenle", "placeholder": "<PERSON><PERSON><PERSON>", "disablePrompt_one": "{{count}} konum seviyesini devre dışı bırakmak üzeresiniz. Bu işlem geri alınamaz.", "disablePrompt_other": "{{count}} konum seviyesini devre dışı bırakmak üzeresiniz. Bu işlem geri alınamaz.", "disabledToggleTooltip": "Devre dışı bırakılamaz: devre dışı bırakmadan önce gelen ve/veya rezerve edilmiş miktarı temizleyin.", "successToast": "Stok seviyeleri başarıyla güncellendi."}}, "giftCards": {"domain": "Hediye Ka<PERSON>ları", "editGiftCard": "Hediye Kartını Düzenle", "createGiftCard": "Hediye Kartı Oluştur", "createGiftCardHint": "Mağazanızda ödeme yöntemi olarak kullanılabilecek bir hediye kartı manuel olarak oluşturun.", "selectRegionFirst": "<PERSON><PERSON> bir bö<PERSON>", "deleteGiftCardWarning": "{{code}} hediye kartını silmek üzeresiniz. Bu işlem geri alınamaz.", "balanceHigherThanValue": "Bakiye, orijinal miktardan yüksek olamaz.", "balanceLowerThanZero": "Bakiye negatif <PERSON>.", "expiryDateHint": "<PERSON>lk<PERSON>rin hediye kartı son kullanma tarihleriyle ilgili farklı yasaları vardır. Son kullanma tarihini belirlemeden önce yerel düzenlemeleri kontrol ettiğinizden emin olun.", "regionHint": "Hediye kartının b<PERSON><PERSON><PERSON><PERSON>ştirmek, para birimini de değiştirecek ve bu durum parasal değerini etkileyebilir.", "enabledHint": "Hediye kartının etkin veya devre dışı olup olmadığını belirtin.", "balance": "Bakiye", "currentBalance": "<PERSON><PERSON><PERSON><PERSON>", "initialBalance": "Başlangıç bakiyesi", "personalMessage": "<PERSON><PERSON><PERSON><PERSON>", "recipient": "Alıcı"}, "customers": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Müşterileriniz burada görünecek."}, "create": {"header": "Müşteri Oluştur", "hint": "Yeni bir müşteri oluşturun ve detaylarını yönetin.", "successToast": "{{email}} müşterisi başarıyla oluşturuldu."}, "groups": {"label": "Müşteri grupları", "remove": "\"{{name}}\" müşteri grubundan müşteriyi kaldırmak istediğinizden emin misiniz?", "removeMany": "Müşteriyi aşağıdaki müşteri gruplarından kaldırmak istediğinizden emin misiniz: {{groups}}?", "alreadyAddedTooltip": "Müşteri zaten bu müşteri grubunda.", "list": {"noRecordsMessage": "<PERSON>u müşteri herhangi bir gruba ait değil."}, "add": {"success": "Müşteri eklendi: {{groups}}.", "list": {"noRecordsMessage": "Lütfen önce bir müşteri grubu oluşturun."}}, "removed": {"success": "Müşteri çıkarıldı: {{groups}}.", "list": {"noRecordsMessage": "Lütfen önce bir müşteri grubu oluşturun."}}}, "edit": {"header": "Müşteriyi Düzenle", "emailDisabledTooltip": "Kayıtlı müşteriler için e-posta adresi değiştirilemez.", "successToast": "{{email}} müşterisi başarıyla güncellendi."}, "delete": {"title": "Müşteriyi Sil", "description": "{{email}} müşterisini silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "{{email}} müşterisi başarı<PERSON> si<PERSON>."}, "fields": {"guest": "<PERSON><PERSON><PERSON><PERSON>", "registered": "<PERSON><PERSON><PERSON><PERSON>", "groups": "Gruplar"}, "registered": "<PERSON><PERSON><PERSON><PERSON>", "guest": "<PERSON><PERSON><PERSON><PERSON>", "hasAccount": "<PERSON><PERSON><PERSON><PERSON> var"}, "customerGroups": {"domain": "Müşteri Grupları", "subtitle": "Müşterileri gruplara ayırın. Gruplar farklı promosyonlara ve fiyatlara sahip olabilir.", "create": {"header": "Müşteri Grubu Oluştur", "hint": "Müşterilerinizi segmente etmek için yeni bir müşteri grubu oluşturun.", "successToast": "{{name}} müşteri grubu başarıyla oluşturuldu."}, "edit": {"header": "Müşteri Grubunu <PERSON>", "successToast": "{{name}} müşteri grubu başarıyla güncellendi."}, "delete": {"title": "Müşteri Grubunu Sil", "description": "{{name}} müşteri grubunu silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "{{name}} müşteri grubu başar<PERSON><PERSON> silind<PERSON>."}, "customers": {"alreadyAddedTooltip": "Müşteri zaten gruba eklenmiş.", "add": {"successToast_one": "Müşteri gruba başarıyla eklendi.", "successToast_other": "Müşteriler gruba başarıyla eklendi.", "list": {"noRecordsMessage": "Önce bir müşteri oluşturun."}}, "remove": {"title_one": "Müşteriyi kaldır", "title_other": "Müşterileri kaldır", "description_one": "Müşteri grubundan {{count}} müşteriyi kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "description_other": "Müşteri grubundan {{count}} müşteriyi kaldırmak üzeresiniz. Bu işlem geri alınamaz."}, "list": {"noRecordsMessage": "Bu grupta müşteri yok."}}, "list": {"filtered": {"heading": "<PERSON><PERSON>ç yok", "description": "Hiçbir müşteri grubu mevcut filtre kriterlerine u<PERSON>."}, "empty": {"heading": "Müşteri grubu bulunamadı", "description": "Bir kullanıcı davet edil<PERSON>, burada görünecektir."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "claim": "<PERSON><PERSON>", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "return": "İade", "cancelWarning": "{{id}} numaralı siparişi iptal etmek üzeresiniz. Bu işlem geri alınamaz.", "onDateFromSalesChannel": "{{date}} tarihinde {{salesChannel}} üzerinden", "list": {"noRecordsMessage": "Siparişleriniz burada görünecektir."}, "summary": {"requestReturn": "<PERSON><PERSON> talebi", "allocateItems": "Ürünleri tahsis et", "editOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editOrderContinue": "Sipariş düzenlemeye devam et", "inventoryKit": "{{count}}x envanter öğesinden oluşur", "itemTotal": "<PERSON><PERSON><PERSON><PERSON>", "shippingTotal": "Kargo <PERSON>", "discountTotal": "İndirim <PERSON>", "taxTotalIncl": "<PERSON><PERSON><PERSON> (dahil)", "itemSubtotal": "<PERSON><PERSON>ün <PERSON>", "shippingSubtotal": "Ka<PERSON>", "discountSubtotal": "İndirim Ara Toplamı", "taxTotal": "<PERSON><PERSON><PERSON>"}, "transfer": {"title": "Mülkiyeti Devret", "requestSuccess": "Sipar<PERSON>ş devri talebi şu kişiye gönderildi: {{email}}.", "currentOwner": "Mevcut <PERSON>", "newOwner": "<PERSON><PERSON>", "currentOwnerDescription": "<PERSON><PERSON> sipariş<PERSON> şu anda ilişkili olan <PERSON>üşteri.", "newOwnerDescription": "Bu siparişin devredileceği müşteri."}, "payment": {"title": "Ö<PERSON>mel<PERSON>", "isReadyToBeCaptured": "Ödeme <0/> alınmaya hazı<PERSON>.", "totalPaidByCustomer": "Müşteri tarafından ödenen toplam", "capture": "Ödemeyi al", "capture_short": "Al", "refund": "İade", "markAsPaid": "Ödendi olarak işaretle", "statusLabel": "<PERSON><PERSON><PERSON>", "statusTitle": "<PERSON><PERSON><PERSON>", "status": {"notPaid": "Ödenmedi", "authorized": "Yetkilendirildi", "partiallyAuthorized": "<PERSON><PERSON><PERSON><PERSON>", "awaiting": "Bekleniyor", "captured": "Alındı", "partiallyRefunded": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> edildi", "partiallyCaptured": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "refunded": "<PERSON>ade edildi", "canceled": "İptal edildi", "requiresAction": "<PERSON><PERSON><PERSON> g<PERSON>"}, "capturePayment": "{{amount}} tutarında ödeme alınacak.", "capturePaymentSuccess": "{{amount}} tutarındaki ödeme başarıyla alındı", "markAsPaidPayment": "{{amount}} tutarındaki ödeme ödenmiş olarak işaretlenecek.", "markAsPaidPaymentSuccess": "{{amount}} tutarındaki ödeme başarıyla ödenmiş olarak işaretlendi", "createRefund": "İade Oluştur", "refundPaymentSuccess": "{{amount}} tutarındaki iade başarılı", "createRefundWrongQuantity": "Miktar 1 ile {{number}} arasında bir sayı olmalıdır", "refundAmount": "{{ amount }} tutarını iade et", "paymentLink": "{{ amount }} i<PERSON><PERSON> b<PERSON>ğlantısını kopyala", "selectPaymentToRefund": "İade edilecek ödemeyi seçin"}, "edits": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmText": "Bir Sipariş Düzenlemeyi onaylamak üzeresiniz. Bu işlem geri alınamaz.", "cancel": "Düzenlemeyi İptal Et", "currentItems": "<PERSON><PERSON><PERSON>", "currentItemsDescription": "<PERSON><PERSON><PERSON>n miktarını ayarla veya çıkar.", "addItemsDescription": "Siparişe yeni ürünler ekleyebilirsiniz.", "addItems": "Ürünleri ekle", "amountPaid": "<PERSON>denen tutar", "newTotal": "<PERSON><PERSON>", "differenceDue": "Fark ödenecek", "create": "Sipariş<PERSON>", "currentTotal": "Mevcut toplam", "noteHint": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>in da<PERSON>i bir not e<PERSON>in", "cancelSuccessToast": "Sipariş düzenlemesi iptal edildi", "createSuccessToast": "Sipariş düzen<PERSON>e talebi oluşturuldu", "activeChangeError": "Siparişte (iade, talep, değiş<PERSON> vb.) aktif bir değişiklik zaten var. Lütfen siparişi düzenlemeden önce bu değişikliği tamamlayın veya iptal edin.", "panel": {"title": "Sipariş düzenleme talep edildi", "titlePending": "Sipariş düzenleme bekliyor"}, "toast": {"canceledSuccessfully": "Sipariş düzenlemesi iptal edildi", "confirmedSuccessfully": "Sipariş düzenlemesi onaylandı"}, "validation": {"quantityLowerThanFulfillment": "<PERSON><PERSON><PERSON>, tama<PERSON><PERSON>n miktardan daha az veya eşit olamaz"}}, "returns": {"create": "İade Oluştur", "confirm": "<PERSON><PERSON><PERSON>", "confirmText": "Bir iade işlemini onaylamak üzeresiniz. Bu işlem geri alınamaz.", "inbound": "<PERSON><PERSON><PERSON>", "outbound": "Giden", "sendNotification": "<PERSON><PERSON><PERSON><PERSON>", "sendNotificationHint": "Müşteriyi iade hakkında bilgilendir.", "returnTotal": "İade toplamı", "inboundTotal": "<PERSON><PERSON><PERSON>", "refundAmount": "İade tutarı", "outstandingAmount": "<PERSON><PERSON> tutar", "reason": "Sebep", "reasonHint": "Müşterinin neden ürünleri iade etmek istediğini seçin.", "note": "Not", "noInventoryLevel": "Stok seviyesi yok", "noInventoryLevelDesc": "Seçilen konum için seçilen ürünlerin stok seviyesi yoktur. İade talep edilebilir ancak seçilen konum için bir stok seviyesi oluşturulana kadar alınamaz.", "noteHint": "Belirtmek istediğiniz bir şey varsa serbestçe yazabilirsiniz.", "location": "<PERSON><PERSON>", "locationHint": "Ürünleri hangi konuma iade etmek istediğinizi seçin.", "inboundShipping": "İade teslimatı", "inboundShippingHint": "Hangi yöntemi kullanmak istediğinizi seçin.", "returnableQuantityLabel": "İade edilebilir miktar", "refundableAmountLabel": "İade edilebilir tutar", "returnRequestedInfo": "{{requestedItemsCount}}x ürün i<PERSON>in iade talebi", "returnReceivedInfo": "{{requestedItemsCount}}x ürün iade alındı", "itemReceived": "Ürünler alındı", "returnRequested": "İade talep edildi", "damagedItemReceived": "Hasarlı ürünler alındı", "damagedItemsReturned": "{{quantity}}x hasarlı ürün iade edildi", "activeChangeError": "Bu siparişte aktif bir değişiklik işlemi devam ediyor. Lütfen değişikliği tamamlayın veya iptal edin.", "cancel": {"title": "İadeyi İptal Et", "description": "İade talebini iptal etmek istediğinizden emin misiniz?"}, "placeholders": {"noReturnShippingOptions": {"title": "İade teslimat seçenekleri bulunamadı", "hint": "Seçilen konum için bir iade teslimat seçeneği oluşturulmadı. <LinkComponent>Konum & teslimat</LinkComponent> bölümünde bir tane oluşturabilirsiniz."}, "outboundShippingOptions": {"title": "Giden teslimat seçenekleri bulunamadı", "hint": "Seçilen konum için bir giden teslimat seçeneği oluşturulmadı. <LinkComponent>Konum & teslimat</LinkComponent> bölümünde bir tane oluşturabilirsiniz."}}, "receive": {"action": "Ürünleri al", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "<PERSON><PERSON>m ürünleri stokla", "itemsLabel": "Alınan ü<PERSON>ü<PERSON>ler", "title": "#{{returnId}} i<PERSON><PERSON> al", "sendNotificationHint": "Alınan iade hakkında müşteriyi bilgilendirin.", "inventoryWarning": "Lütfen yukarıdaki girişlerinize göre stok seviyelerinin otomatik olarak ayarlanacağını unutmayın.", "writeOffInputLabel": "<PERSON><PERSON> ü<PERSON>ün hasarlı?", "toast": {"success": "İade başarıyla alındı.", "errorLargeValue": "<PERSON><PERSON><PERSON>, talep edilen ürün miktarından fazla.", "errorNegativeValue": "<PERSON><PERSON><PERSON> negatif <PERSON>.", "errorLargeDamagedValue": "Hasarlı ürün miktarı + hasarsız alınan ürün miktarı toplam ürün miktarını aşıyor. Lütfen hasarsız ürün miktarını azaltın."}}, "toast": {"canceledSuccessfully": "İade başarıyla iptal edildi", "confirmedSuccessfully": "İade başarıyla onaylandı"}, "panel": {"title": "İade başlatıldı", "description": "Tamamlanması gereken açık bir iade talebi var"}}, "claims": {"create": "Talep Oluştur", "confirm": "<PERSON><PERSON>", "confirmText": "Bir Talebi onaylamak üzeresiniz. Bu işlem geri alınamaz.", "manage": "<PERSON><PERSON>", "outbound": "Giden", "outboundItemAdded": "{{itemsCount}}x talep yoluyla e<PERSON>ndi", "outboundTotal": "Giden toplam", "outboundShipping": "Giden teslimat", "outboundShippingHint": "Hangi yöntemi kullanmak istediğinizi seçin.", "refundAmount": "<PERSON><PERSON><PERSON>", "activeChangeError": "Bu siparişte aktif bir değişiklik işlemi var. Lütfen önceki değişikliği tamamlayın veya iptal edin.", "actions": {"cancelClaim": {"successToast": "Talep başarıyla iptal edildi."}}, "cancel": {"title": "Talebi İptal Et", "description": "Talebi iptal etmek istediğinizden emin misiniz?"}, "tooltips": {"onlyReturnShippingOptions": "Bu liste yalnızca iade teslimat seçeneklerinden oluşacaktır."}, "toast": {"canceledSuccessfully": "Talep başarıyla iptal edildi", "confirmedSuccessfully": "Talep başarıyla onaylandı"}, "panel": {"title": "Talep başlatıldı", "description": "Tamamlanması gereken açık bir talep talebi var"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmText": "Bir Değişimi onaylamak üzeresiniz. Bu işlem geri alınamaz.", "outbound": "Giden", "outboundItemAdded": "{{itemsCount}}x de<PERSON><PERSON><PERSON><PERSON> yo<PERSON>", "outboundTotal": "Giden toplam", "outboundShipping": "Giden teslimat", "outboundShippingHint": "Hangi yöntemi kullanmak istediğinizi seçin.", "refundAmount": "<PERSON><PERSON><PERSON>", "activeChangeError": "Bu siparişte aktif bir değişiklik işlemi var. Lütfen önceki değişikliği tamamlayın veya iptal edin.", "actions": {"cancelExchange": {"successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarıyla iptal edildi."}}, "cancel": {"title": "Değişimi İptal Et", "description": "Değişimi iptal etmek istediğinizden emin misiniz?"}, "tooltips": {"onlyReturnShippingOptions": "Bu liste yalnızca iade teslimat seçeneklerinden oluşacaktır."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarıyla iptal edildi", "confirmedSuccessfully": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> onaylandı"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>ld<PERSON>", "description": "Tamamlanması gereken açık bir değişim tale<PERSON> var"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON>", "notAllocatedLabel": "<PERSON><PERSON><PERSON>"}, "allocateItems": {"action": "Ürünleri tahsis et", "title": "Sipariş ürünlerini tahsis et", "locationDescription": "Hangi konumdan tahsis etmek istediğinizi seçin.", "itemsToAllocate": "<PERSON><PERSON><PERSON> ed<PERSON><PERSON>", "itemsToAllocateDesc": "Tahsis etmek istediğiniz ürün sayısını seçin", "search": "Ürünleri ara", "consistsOf": "{{num}}x envanter öğesinden oluşur", "requires": "Her bir varyant için {{num}} gerektirir", "toast": {"created": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarıyla tahs<PERSON> edildi"}, "error": {"quantityNotAllocated": "Tahs<PERSON> edil<PERSON> var."}}, "shipment": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trackingNumber": "Takip numa<PERSON>ı", "addTracking": "Takip numarası ekle", "sendNotification": "<PERSON><PERSON><PERSON><PERSON>", "sendNotificationHint": "Müşteriye bu gönderim hakkında bilgi ver.", "toastCreated": "<PERSON><PERSON><PERSON><PERSON> başar<PERSON>yla oluşturuldu."}, "fulfillment": {"cancelWarning": "Bir sipariş tamamlamayı iptal etmek üzeresiniz. Bu işlem geri alınamaz.", "markAsDeliveredWarning": "Tamamlamayı teslim edilmiş olarak işaretlemek üzeresiniz. Bu işlem geri alınamaz.", "unfulfilledItems": "Tamamlanmamış Ürünler", "statusLabel": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "statusTitle": "<PERSON><PERSON><PERSON><PERSON>", "fulfillItems": "Ürünleri tama<PERSON>la", "awaitingFulfillmentBadge": "Tamamlanmayı bekliyor", "requiresShipping": "Teslimat gerekli", "number": "Tam<PERSON><PERSON><PERSON> #{{number}}", "itemsToFulfill": "Tamamlanacak ürünler", "create": "Tam<PERSON>lama <PERSON>", "available": "Mevcut", "inStock": "Stok<PERSON>", "markAsShipped": "G<PERSON><PERSON><PERSON><PERSON> o<PERSON>ak işaretle", "markAsDelivered": "<PERSON><PERSON><PERSON> edil<PERSON>", "itemsToFulfillDesc": "Tamamlanacak ürünleri ve miktarları seçin", "locationDescription": "Ürünleri hangi konumdan tamamlamak istediğinizi se<PERSON>.", "sendNotificationHint": "Oluş<PERSON><PERSON>n tamamlama hakkında müşterilere bilgi verin.", "methodDescription": "Müşterinin seçtiği teslimat yönteminden farklı bir yöntem seçin", "error": {"wrongQuantity": "Tamamlanacak sadece bir ürün mevcut", "wrongQuantity_other": "Miktar 1 ile {{number}} arasında olmalıdır", "noItems": "Tamamlanacak ürün yok.", "noShippingOption": "<PERSON><PERSON>ği gereklidir", "noLocation": "Konum gereklidir"}, "status": {"notFulfilled": "Tamamlanmadı", "partiallyFulfilled": "<PERSON><PERSON><PERSON><PERSON>", "fulfilled": "Tamamlandı", "partiallyShipped": "<PERSON><PERSON><PERSON><PERSON>", "shipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON><PERSON> edil<PERSON>", "partiallyDelivered": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> ed<PERSON>", "partiallyReturned": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> edildi", "returned": "<PERSON>ade edildi", "canceled": "İptal edildi", "requiresAction": "<PERSON><PERSON><PERSON>"}, "toast": {"created": "Tam<PERSON><PERSON>a başarıyla oluşturuldu", "canceled": "<PERSON><PERSON><PERSON>a başarıyla iptal edildi", "fulfillmentShipped": "Gönderilmiş bir tamamlamayı iptal edemezsiniz", "fulfillmentDelivered": "Tam<PERSON><PERSON>a başarıyla teslim edildi olarak işaretlendi"}, "trackingLabel": "Takip", "shippingFromLabel": "<PERSON><PERSON><PERSON><PERSON>", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "differentOptionSelected": "Seçilen kargo seçeneği müşterinin seçtiğinden farklı.", "disabledItemTooltip": "Seçtiğiniz kargo seçeneği bu ürünün tama<PERSON>lanmasına izin vermiyor"}, "refund": {"title": "İade Oluştur", "sendNotificationHint": "Oluşturulan iade hakkında müşterilere bilgi verin.", "systemPayment": "Sistem ödemesi", "systemPaymentDesc": "Ödemelerinizden biri bir sistem ödemesidir. Bu tür ödemeler için işlemler Medusa tarafından yönetilmez.", "error": {"amountToLarge": "Orijinal sipariş tutarından daha fazlasını iade edemezsiniz.", "amountNegative": "İade tutarı pozitif bir sayı olmalıdır.", "reasonRequired": "Lütfen bir iade sebebi se<PERSON>."}}, "customer": {"contactLabel": "İletişim", "editEmail": "E-posta düzenle", "transferOwnership": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editBillingAddress": "<PERSON>ura adresini d<PERSON>", "editShippingAddress": "Teslimat adresini düzenle"}, "activity": {"header": "Aktivite", "showMoreActivities_one": "{{count}} aktiv<PERSON><PERSON>", "showMoreActivities_other": "{{count}} aktiv<PERSON><PERSON>", "comment": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON>ir yorum bırak", "addButtonText": "<PERSON><PERSON> e<PERSON>", "deleteButtonText": "<PERSON><PERSON><PERSON> sil"}, "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "events": {"common": {"toReturn": "İade edilecek", "toSend": "G<PERSON><PERSON><PERSON><PERSON>k"}, "placed": {"title": "Sipariş verildi", "fromSalesChannel": "{{salesChannel}} üzerinden"}, "canceled": {"title": "Sipariş iptal edildi"}, "payment": {"awaiting": "<PERSON><PERSON><PERSON>", "captured": "<PERSON><PERSON><PERSON>", "canceled": "Ödeme iptal edildi", "refunded": "<PERSON>deme i<PERSON> edildi"}, "fulfillment": {"created": "Ürünler tamamlandı", "canceled": "<PERSON><PERSON><PERSON><PERSON> iptal edildi", "shipped": "Ürünler gönderildi", "delivered": "Ürünler teslim edildi", "items_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "return": {"created": "İade #{{returnId}} talep edildi", "canceled": "İade #{{returnId}} iptal edildi", "received": "İade #{{returnId}} alındı", "items_one": "{{count}} ü<PERSON><PERSON><PERSON> i<PERSON> edildi", "items_other": "{{count}} ü<PERSON><PERSON><PERSON> i<PERSON> edildi"}, "note": {"comment": "<PERSON><PERSON>", "byLine": "{{author}} ta<PERSON><PERSON><PERSON><PERSON>n"}, "claim": {"created": "Talep #{{claimId}} talep edildi", "canceled": "Talep #{{claimId}} iptal edildi", "itemsInbound": "{{count}} <PERSON><PERSON><PERSON><PERSON> i<PERSON> ed<PERSON>", "itemsOutbound": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "exchange": {"created": "<PERSON><PERSON><PERSON>şim #{{exchangeId}} talep edildi", "canceled": "Değişim #{{exchangeId}} iptal edildi", "itemsInbound": "{{count}} <PERSON><PERSON><PERSON><PERSON> i<PERSON> ed<PERSON>", "itemsOutbound": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "edit": {"requested": "<PERSON><PERSON><PERSON><PERSON> düzenlemesi #{{editId}} talep edildi", "confirmed": "Si<PERSON><PERSON>ş düzenlemesi #{{editId}} onaylandı"}, "transfer": {"requested": "Sipariş devri #{{transferId}} talep edildi", "confirmed": "Sipariş devri #{{transferId}} onaylandı", "declined": "Sipariş transferi #{{transferId}} reddedildi"}, "update_order": {"shipping_address": "Teslimat adresi g<PERSON>di", "billing_address": "Fatura adresi <PERSON>", "email": "E-posta g<PERSON>"}}}, "fields": {"displayId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refundableAmount": "İade edilebilir tutar", "returnableQuantity": "İade edilebilir miktar"}, "orderCanceled": "Sipariş başarıyla iptal edildi", "status": {"not_paid": "Ödenmedi", "pending": "Beklemede", "completed": "Tamamlandı", "draft": "Taslak", "archived": "Arşivlendi", "canceled": "İptal edildi", "requires_action": "İşlem gerekiyor"}, "edit": {"email": {"title": "E-postayı düzenle", "requestSuccess": "Sipariş e-postası {{email}} olarak güncellendi."}, "shippingAddress": {"title": "Teslimat adresini düzenle", "requestSuccess": "Sipariş teslimat adresi güncellendi."}, "billingAddress": {"title": "<PERSON>ura adresini d<PERSON>", "requestSuccess": "Sipariş fatura adresi g<PERSON>."}}}, "draftOrders": {"domain": "Taslak Siparişler", "deleteWarning": "Taslak sipariş {{id}} silinmek üzere. Bu işlem geri alınamaz.", "paymentLinkLabel": "Ödeme bağlantısı", "cartIdLabel": "Sepet ID", "markAsPaid": {"label": "Ödenmiş olarak işaretle", "warningTitle": "Ödenmiş Olarak İşaretle", "warningDescription": "Taslak siparişi ödenmiş olarak işaretlemek üzeresiniz. Bu işlem geri alınamaz ve daha sonra ödeme toplamak mümkün olmayacaktır."}, "status": {"open": "Açık", "completed": "Tamamlandı"}, "create": {"createDraftOrder": "Taslak Sipariş Oluştur", "createDraftOrderHint": "Bir sipariş yerleştirilmeden önce detaylarını yönetmek için yeni bir taslak sipariş oluşturun.", "chooseRegionHint": "<PERSON><PERSON><PERSON><PERSON>", "existingItemsLabel": "<PERSON><PERSON><PERSON>", "existingItemsHint": "Taslak siparişe mevcut ürünleri ekleyin.", "customItemsLabel": "<PERSON><PERSON>", "customItemsHint": "Taslak siparişe özel ürünler ekleyin.", "addExistingItemsAction": "Mevcut ürünleri ekle", "addCustomItemAction": "<PERSON><PERSON>ü<PERSON>", "noCustomItemsAddedLabel": "Henüz özel ürün eklenmedi", "noExistingItemsAddedLabel": "<PERSON><PERSON><PERSON>z mevcut ürün e<PERSON>i", "chooseRegionTooltip": "<PERSON><PERSON> bir bö<PERSON>", "useExistingCustomerLabel": "Mevcut müş<PERSON>iyi kullan", "addShippingMethodsAction": "teslimat yöntemlerini ekle", "unitPriceOverrideLabel": "<PERSON><PERSON>m fiyat geçersiz kılma", "shippingOptionLabel": "teslimat seçeneği", "shippingOptionHint": "Taslak sipariş için teslimat seçeneğini seçin.", "shippingPriceOverrideLabel": "teslimat fiyatı geçersiz kılma", "shippingPriceOverrideHint": "Taslak sipariş için teslimat fiyatını geçersiz kılın.", "sendNotificationLabel": "<PERSON><PERSON><PERSON><PERSON>", "sendNotificationHint": "Taslak sipariş oluşturulduğunda müşteriye bir bildirim gönderin."}, "validation": {"requiredEmailOrCustomer": "E-posta veya müşteri gereklidir.", "requiredItems": "En az bir ürün gereklidir.", "invalidEmail": "E-posta geçerli bir e-posta adresi olmalıdır."}}, "stockLocations": {"domain": "Konumlar ve teslimat", "list": {"description": "Mağazanızın stok konumlarını ve teslimat seçeneklerini yönetin."}, "create": {"header": "Stok Konumu Oluştur", "hint": "Bir stok konumu, ürünlerin depolandığı ve gönderildiği fiziksel bir yerdir.", "successToast": "Konum {{name}} başarıyla oluşturuldu."}, "edit": {"header": "Stok Konumunu <PERSON>", "viewInventory": "<PERSON><PERSON><PERSON>", "successToast": "Konum {{name}} başarı<PERSON> güncellendi."}, "delete": {"confirmation": "Stok konumu {{name}} silinmek üzere. Bu işlem geri alınamaz."}, "fulfillmentProviders": {"header": "Tamamlama Sağlayıcıları", "shippingOptionsTooltip": "Bu açılır liste yalnızca bu konum için etkinleştirilen sağlayıcıları içerecektir. Açılır liste devre dışıysa konuma ekleyin.", "label": "Bağlı tamamlama sağlayıcıları", "connectedTo": "{{total}} sa<PERSON><PERSON><PERSON><PERSON><PERSON>dan {{count}}'ine bağlı", "noProviders": "Bu Stok Konumu hiçbir tamamlama sağlayıcısına bağlı değil.", "action": "Sağlayıcıları Bağla", "successToast": "Stok konumu için tamamlama sağlayıcıları başarıyla güncellendi."}, "fulfillmentSets": {"pickup": {"header": "Alma"}, "shipping": {"header": "Teslimat"}, "disable": {"confirmation": "{{name}} devre dışı bırakmak istediğinizden emin misiniz? Bu, ilgili tüm hizmet bölgelerini ve teslimat seçeneklerini silecektir ve geri alınamaz.", "pickup": "Alma başarıyla devre dışı bırakıldı.", "shipping": "Teslimat başarıyla devre dışı bırakıldı."}, "enable": {"pickup": "Alma başarıyla etkinleştirildi.", "shipping": "Teslimat başarıyla etkinleştirildi."}}, "sidebar": {"header": "Teslimat Yapılandırması", "shippingProfiles": {"label": "Teslimat Profilleri", "description": "Ürünleri teslimat gereksinimlerine göre gruplayın"}}, "salesChannels": {"header": "Satış Kanalları", "label": "Bağlı satış kanalları", "connectedTo": "{{total}} satı<PERSON> kanalından {{count}}'ine bağlı", "noChannels": "Konum hiçbir satış kanalına bağlı değil.", "action": "Satış kanallarını bağla", "successToast": "Satış kanalları başarıyla güncellendi."}, "shippingOptions": {"conditionalPrices": {"header": "{{name}} <PERSON><PERSON><PERSON>", "description": "Bu kargo seçeneği için koşullu fiyatları sepet ürün toplamına göre yönetin.", "attributes": {"cartItemTotal": "Sepet ürün <PERSON>lamı"}, "summaries": {"range": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> <1>{{gte}}</1> ile <2>{{lte}}</2> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "greaterThan": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "manageConditionalPrices": "<PERSON><PERSON><PERSON><PERSON> fiyatları yönet"}, "rules": {"amount": "Kargo seçeneği fiyatı", "gte": "Minimum sepet ürün top<PERSON>ı", "lte": "<PERSON><PERSON><PERSON><PERSON> sepet ürün <PERSON>ı"}, "customRules": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> k<PERSON><PERSON><PERSON>, y<PERSON><PERSON><PERSON>e yönetilemeyen kurallara <PERSON>.", "eq": "Sepet ürün toplamı şuna eşit olmalı", "gt": "Sepet ürün toplamı bundan büyük olmalı", "lt": "Sepet ürün toplamı bundan küçük olmalı"}, "errors": {"amountRequired": "Kargo seçeneği fiyatı gereklidir", "minOrMaxRequired": "Minimum veya maksimum sepet ürün toplamından en az biri sağlanmalıdır", "minGreaterThanMax": "Minimum sepet ürün toplamı, maksimum sepet ürün toplamından küçük veya eşit olmalıdır", "duplicateAmount": "<PERSON><PERSON> seç<PERSON>ği fiyatı her koşul için benzersiz olmalıdır", "overlappingConditions": "<PERSON><PERSON>ullar tüm fiyat kuralları arasında benzersiz olmalıdır"}}, "create": {"shipping": {"header": "{{zone}} <PERSON><PERSON><PERSON>çeneği Oluştur", "hint": "Bu konumdan ürünlerin nasıl gönderileceğini tanımlamak için yeni bir kargo seçeneği oluşturun.", "label": "<PERSON><PERSON>", "successToast": "<PERSON><PERSON> {{name}} başarıyla oluşturuldu."}, "returns": {"header": "{{zone}} <PERSON><PERSON><PERSON>ade Seçeneği Oluştur", "hint": "Bu konuma ürünlerin nasıl iade edileceğini tanımlamak için yeni bir iade seçeneği oluşturun.", "label": "<PERSON><PERSON>", "successToast": "İade seçeneği {{name}} başarıyla oluşturuldu."}, "tabs": {"details": "Detaylar", "prices": "<PERSON><PERSON><PERSON><PERSON>"}, "action": "Seçenek oluştur"}, "delete": {"confirmation": "<PERSON><PERSON> {{name}} silinmek üzere. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON> {{name}} ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "edit": {"header": "<PERSON><PERSON>", "action": "Seçeneği düzenle", "successToast": "<PERSON><PERSON> {{name}} başar<PERSON><PERSON> güncellendi."}, "pricing": {"action": "Fiyatları düzenle"}, "fields": {"count": {"shipping_one": "{{count}} <PERSON><PERSON>", "shipping_other": "{{count}} <PERSON><PERSON>", "returns_one": "{{count}} i<PERSON>", "returns_other": "{{count}} i<PERSON>"}, "priceType": {"label": "<PERSON><PERSON><PERSON>", "options": {"fixed": {"label": "Sabit", "hint": "<PERSON><PERSON> seçeneğinin fiyatı sabittir ve siparişin içeriğine göre değişmez."}, "calculated": {"label": "Hesaplanmış", "hint": "<PERSON><PERSON> se<PERSON><PERSON> fi<PERSON>t<PERSON>, ödeme sırasında tamamlama sağlayıcısı tarafından hesaplanır."}}}, "enableInStore": {"label": "Mağazada etkinleştir", "hint": "Müşterilerin bu seçeneği ödeme sırasında kullanıp kullanamayacağı."}, "provider": "Tamamlama sağlayıcısı", "profile": "<PERSON><PERSON> profili", "fulfillmentOption": "<PERSON><PERSON><PERSON><PERSON>"}}, "serviceZones": {"create": {"headerPickup": "{{location}} konumundan Alım için Hizmet Bölgesi Oluştur", "headerShipping": "{{location}} konumundan teslimat için Hizmet Bölgesi Oluştur", "action": "Hizmet b<PERSON><PERSON>si <PERSON>", "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> {{name}} başarıyla oluşturuldu."}, "edit": {"header": "Hizmet Bölgesini Düzenle", "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> {{name}} b<PERSON><PERSON><PERSON><PERSON><PERSON> gü<PERSON>di."}, "delete": {"confirmation": "<PERSON>z<PERSON> b<PERSON> {{name}} silinmek üzere. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> {{name}} b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "manageAreas": {"header": "{{name}} i<PERSON><PERSON>", "action": "Alanları yönet", "label": "<PERSON><PERSON>", "hint": "<PERSON>zmet b<PERSON><PERSON><PERSON><PERSON> ka<PERSON>ığı coğrafi alan<PERSON>ı seçin.", "successToast": "{{name}} i<PERSON><PERSON> al<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "fields": {"noRecords": "Ekleyebileceğiniz teslimat seçeneklerine sahip hizmet bölgeleri yok.", "tip": "<PERSON><PERSON> <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> bö<PERSON> veya alanlardan oluşur. <PERSON><PERSON>, beli<PERSON><PERSON> konumlara sunulabilir teslimat seçeneklerini sınırlamak için kullanılır."}}}, "shippingProfile": {"domain": "teslimat Profilleri", "subtitle": "Benzer teslimat gereksinimlerine sahip ürünleri profillere gruplandırın.", "create": {"header": "teslimat Profili Oluştur", "hint": "Benzer teslimat gereksinimlerine sahip ürünleri gruplandırmak için yeni bir teslimat profili oluşturun.", "successToast": "teslimat profili {{name}} başarıyla oluşturuldu."}, "delete": {"title": "teslimat Profilini Sil", "description": "teslimat profili {{name}} silinmek üzere. Bu işlem geri alınamaz.", "successToast": "teslimat profili {{name}} ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "tooltip": {"type": "teslimat profili tü<PERSON>, örneğ<PERSON>: <PERSON><PERSON><PERSON><PERSON>, Aşır<PERSON>, Sadece teslimat, vb."}}, "taxRegions": {"domain": "<PERSON><PERSON><PERSON>", "list": {"hint": "Müşterilerinizin farklı ülkeler ve bölgelerden alışveriş yaparken ödediği tutarı yönetin."}, "delete": {"confirmation": "Bir vergi bölgesini silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>."}, "create": {"header": "<PERSON><PERSON>gi Bölgesi <PERSON>", "hint": "Belirli bir ülke için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun.", "errors": {"rateIsRequired": "Varsayılan bir vergi oranı oluştururken vergi oranı gereklidir.", "nameIsRequired": "Varsayılan bir vergi oranı oluştururken isim gere<PERSON>lid<PERSON>."}, "successToast": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> başarıyla oluşturuldu."}, "province": {"header": "<PERSON><PERSON>", "create": {"header": "İl Vergi Bölgesi Oluştur", "hint": "Belirli bir il için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "state": {"header": "Eyaletler", "create": {"header": "Eyalet Vergi Bölgesi Oluştur", "hint": "Belirli bir eyalet için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "stateOrTerritory": {"header": "Eyaletler veya Bölgeler", "create": {"header": "Eyalet/Bölge Vergi Bölgesi Oluştur", "hint": "Belirli bir eyalet/bölge için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "county": {"header": "İlçeler", "create": {"header": "İlçe Vergi Bölgesi Oluştur", "hint": "Belirli bir ilçe için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "region": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Bölge Vergi Bölgesi Oluştur", "hint": "Belirli bir bölge için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "department": {"header": "Depar<PERSON><PERSON>", "create": {"header": "<PERSON><PERSON>tman Vergi Bölgesi Oluştur", "hint": "Belirli bir departman için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "territory": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Bölge Vergi Bölgesi Oluştur", "hint": "Belirli bir bölge için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "prefecture": {"header": "Prefektörlükler", "create": {"header": "Prefektörlük Vergi Bölgesi Oluştur", "hint": "Belirli bir prefektörlük için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "district": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Bölge Vergi Bölgesi Oluştur", "hint": "Belirli bir bölge için vergi oranlarını tanımlamak üzere yeni bir vergi bölgesi oluşturun."}}, "governorate": {"header": "Valilikler", "create": {"header": "Valilik Vergi Bölgesi Oluştur", "hint": "Belirli bir valilik için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "canton": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Kanton Vergi Bölgesi Oluştur", "hint": "Belirli bir kanton için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "emirate": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Emirlik Vergi Bölgesi Oluştur", "hint": "Belirli bir emirlik için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "sublevel": {"header": "<PERSON>", "create": {"header": "Alt Seviye Vergi Bölgesi Oluştur", "hint": "Belirli bir alt seviye için vergi oranlarını tanımlamak üzere yeni bir vergi bö<PERSON>si oluşturun."}}, "taxOverrides": {"header": "<PERSON><PERSON><PERSON>m <PERSON>ı", "create": {"header": "Aşım Oranı Oluştur", "hint": "Bel<PERSON>li koşullar için var<PERSON>ılan vergi oranlarını aşan bir vergi oranı oluşturun."}, "edit": {"header": "Aşım Oranını Düzenle", "hint": "Bel<PERSON>li koşullar için varsayılan vergi oranlarını aşan bir vergi oranını düzenleyin."}}, "taxRates": {"create": {"header": "Vergi Oranı Oluştur", "hint": "Bir bölge için vergi oranını tanımlamak üzere yeni bir vergi oranı oluşturun.", "successToast": "Vergi oranı başarıyla oluşturuldu."}, "edit": {"header": "Vergi Oranını Düzenle", "hint": "Bir bölge için vergi oranını tanımlamak üzere vergi oranını düzenleyin.", "successToast": "Vergi oranı başarıyla güncellendi."}, "delete": {"confirmation": "Vergi oranı {{name}} silinmek üzere. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON>gi oranı baş<PERSON><PERSON><PERSON> si<PERSON>."}}, "fields": {"isCombinable": {"label": "Birleştirilebilir", "hint": "Bu vergi oranının vergi bö<PERSON><PERSON><PERSON><PERSON> varsayılan oranla birleştirilebilir olup olmadığını belirtir.", "true": "Birleştirilebilir", "false": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "defaultTaxRate": {"label": "Varsayılan vergi oranı", "tooltip": "Bu bölge için var<PERSON>ılan vergi oranı. <PERSON><PERSON><PERSON>, bir <PERSON>lke veya bölge için standart KDV oranı.", "action": "Varsayılan vergi oranı oluştur"}, "taxRate": "V<PERSON>gi or<PERSON>ı", "taxCode": "<PERSON><PERSON><PERSON> kodu", "targets": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Bu vergi oranının uygulanacağı hedefle<PERSON> se<PERSON>.", "options": {"product": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productCollection": "Ürün k<PERSON>iyonları", "productTag": "<PERSON><PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Müşteri grupları"}, "operators": {"in": "içinde", "on": "üzerinde", "and": "ve"}, "placeholders": {"product": "Ürünleri ara", "productCollection": "Ürün koleksiyonlarını ara", "productTag": "<PERSON><PERSON>ün etiketlerini ara", "productType": "<PERSON><PERSON><PERSON><PERSON> tü<PERSON> ara", "customerGroup": "Müşteri gruplarını ara"}, "tags": {"product": "<PERSON><PERSON><PERSON><PERSON>", "productCollection": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "productTag": "<PERSON><PERSON><PERSON><PERSON>", "productType": "<PERSON><PERSON><PERSON><PERSON> türü", "customerGroup": "Müşteri grubu"}, "modal": {"header": "<PERSON><PERSON><PERSON>"}, "values_one": "{{count}} <PERSON><PERSON><PERSON>", "values_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "numberOfTargets_one": "{{count}} hedef", "numberOfTargets_other": "{{count}} he<PERSON><PERSON>r", "additionalValues_one": "ve {{count}} <PERSON><PERSON>", "additionalValues_other": "ve {{count}} <PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON>"}, "sublevels": {"labels": {"province": "İl", "state": "Eyalet", "region": "<PERSON><PERSON><PERSON>", "stateOrTerritory": "Eyalet/Bölge", "department": "<PERSON><PERSON><PERSON>", "county": "İlçe", "territory": "<PERSON><PERSON><PERSON>", "prefecture": "Prefektörlük", "district": "<PERSON><PERSON><PERSON>", "governorate": "Valilik", "emirate": "Emirlik", "canton": "<PERSON><PERSON>", "sublevel": "Alt seviye kodu"}, "placeholders": {"province": "<PERSON><PERSON>", "state": "Eyalet seçin", "region": "<PERSON><PERSON><PERSON>", "stateOrTerritory": "Eyalet/bö<PERSON> se<PERSON>", "department": "<PERSON><PERSON><PERSON> se<PERSON><PERSON>", "county": "İlçe seçin", "territory": "<PERSON><PERSON><PERSON>", "prefecture": "Prefektörlük seçin", "district": "<PERSON><PERSON><PERSON>", "governorate": "<PERSON>ilik seçin", "emirate": "<PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON>"}, "tooltips": {"sublevel": "Alt seviye vergi b<PERSON>lgesi için ISO 3166-2 kodunu girin.", "notPartOfCountry": "{{province}}, {{country}}'in bir parçası görünmüyor. Lütfen bu bilgiyi kontrol edin."}, "alert": {"header": "Bu vergi bö<PERSON><PERSON> için alt seviyeler devre dışı", "description": "<PERSON> seviye<PERSON>, bu b<PERSON><PERSON> i<PERSON><PERSON> var<PERSON>lan olarak devre dışıdır. <PERSON><PERSON><PERSON><PERSON>, iller veya bölgeler gibi alt seviyeleri oluşturmak için bunları etkinleştirebilirsiniz.", "action": "Alt seviyeleri etkinleştir"}}, "noDefaultRate": {"label": "Varsayılan oran yok", "tooltip": "Bu vergi bölgesinde varsayılan bir vergi oranı bulunmuyor. <PERSON><PERSON><PERSON><PERSON>, bir <PERSON>lkenin standart KDV'si gibi bir oran varsa, lütfen bu bölgeye ekleyin."}}}, "promotions": {"domain": "Promosyonlar", "sections": {"details": "Promosyon Detayları"}, "tabs": {"template": "<PERSON><PERSON><PERSON>", "details": "Detaylar", "campaign": "Kamp<PERSON><PERSON>"}, "fields": {"type": "<PERSON><PERSON><PERSON>", "value_type": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "campaign": "Kamp<PERSON><PERSON>", "method": "Yöntem", "allocation": "Dağıtım", "addCondition": "<PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amount": {"tooltip": "Miktarı ayarlamak için para birimi kodunu seçin"}, "conditions": {"rules": {"title": "Bu kodu kim kull<PERSON>?", "description": "Hangi müşterinin promosyon kodunu kullanmasına izin verilir? <PERSON><PERSON><PERSON>, promosyon kodu tüm müşteriler tarafından kullanılabilir."}, "target-rules": {"title": "Promosyon hangi ürünlere uygulanacak?", "description": "Promosyon, aşağıdaki koşulları karşılayan ürünlere uygulanacaktır."}, "buy-rules": {"title": "Promosyonu açmak için sepette ne olmalı?", "description": "<PERSON><PERSON> ko<PERSON>ullar <PERSON>ğlandığında, hede<PERSON> ürünlerde promosyon etkinleştirilir."}}}, "tooltips": {"campaignType": "Bir harcama bütçesi belirlemek için promosyonda para birimi kodu seçilmelidir."}, "errors": {"requiredField": "<PERSON><PERSON><PERSON><PERSON> alan", "promotionTabError": "Devam etmeden önce Promosyon Sekmesindeki hataları düzeltin"}, "toasts": {"promotionCreateSuccess": "Promosyon ({{code}}) başarıyla oluşturuldu."}, "create": {}, "edit": {"title": "Promosyon Detaylarını Düzenle", "rules": {"title": "Kullanım koşullarını düzenle"}, "target-rules": {"title": "Ürün koşullarını düzenle"}, "buy-rules": {"title": "Satın alma kurallarını düzenle"}}, "campaign": {"header": "Kamp<PERSON><PERSON>", "edit": {"header": "Kampanyayı Düzenle", "successToast": "Promosyonun kampanyası başarıyla güncellendi."}, "actions": {"goToCampaign": "Kampanyaya git"}}, "campaign_currency": {"tooltip": "Bu promosyonun para birimi. Detaylar sekmesinden değiştirin."}, "form": {"required": "Zorun<PERSON>", "and": "VE", "selectAttribute": "Özellik Seçin", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON>", "description": "Promosyonu mevcut bir kampanyaya ekle.", "placeholder": {"title": "Mevcut kampanya yok", "desc": "Birden fazla promosyonu izlemek ve bütçe sınırları belirlemek için bir tane oluşturabilirsiniz."}}, "new": {"title": "<PERSON><PERSON>", "description": "Bu promosyon için yeni bir kamp<PERSON>a oluştur."}, "none": {"title": "Kampanyasız", "description": "Promosyonu kampanyaya bağlamadan devam et"}}, "method": {"label": "Yöntem", "code": {"title": "Promosyon Kodu", "description": "Müşteriler bu kodu ödeme sırasında girmelidir"}, "automatic": {"title": "Otomatik", "description": "Müşteriler bu promosyonu ödeme sırasında görecektir"}}, "max_quantity": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bu promosyonun uygulanacağı maksimum ürün miktarı."}, "type": {"standard": {"title": "<PERSON><PERSON>", "description": "Standart bir promosyon"}, "buyget": {"title": "Satın Al Kazan", "description": "X Al Y Kazan promosyonu"}}, "allocation": {"each": {"title": "Her biri", "description": "<PERSON><PERSON><PERSON> her bir <PERSON><PERSON><PERSON><PERSON>"}, "across": {"title": "<PERSON><PERSON>", "description": "Değeri ürünler arasında uygular"}}, "code": {"title": "Kod", "description": "Müşterilerinizin ödeme sırasında gireceği kod."}, "value": {"title": "Promosyon Değeri"}, "value_type": {"fixed": {"title": "Promosyon Değeri", "description": "İndirim miktarı. Örn: 100"}, "percentage": {"title": "Promosyon Değeri", "description": "İndirim yüzdesi. Örn: %8"}}, "status": {"label": "Durum", "draft": {"title": "Taslak", "description": "Promosyon henüz tamamlanmamıştır."}, "active": {"title": "Aktif", "description": "Promosyon etkinleştirildi."}, "inactive": {"title": "<PERSON><PERSON><PERSON>", "description": "Promosyon devre dışı bırakıldı."}}}, "deleteWarning": "Promosyon {{code}}'u silmek üzeresiniz. Bu işlem geri alınamaz.", "createPromotionTitle": "Promosyon Oluştur", "type": "Promosyon türü", "conditions": {"add": "<PERSON><PERSON>", "list": {"noRecordsMessage": "Promosyonun uygulanacağı ürünleri kısıtlamak için bir şart ekleyin."}}}, "campaigns": {"domain": "Kampanyalar", "details": "Kampanya <PERSON>ı", "status": {"active": "Aktif", "expired": "S<PERSON><PERSON>i <PERSON>ş", "scheduled": "Planlanmış"}, "delete": {"title": "Emin misiniz?", "description": "'{{name}}' kampanyasını silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "'{{name}}' ka<PERSON><PERSON><PERSON><PERSON> başarıyla oluşturuldu."}, "edit": {"header": "Kampanyayı Düzenle", "description": "Kampanyanın de<PERSON>ylarını düzenleyin.", "successToast": "'{{name}}' ka<PERSON><PERSON><PERSON><PERSON> baş<PERSON><PERSON><PERSON> g<PERSON>."}, "configuration": {"header": "Yapılandırma", "edit": {"header": "Kampanya Yapılandırmasını Düzenle", "description": "Kampanyanın yapılandırmasını düzenleyin.", "successToast": "Kampanya yapılandırması başarıyla güncellendi."}}, "create": {"title": "Kampanya Oluştur", "description": "Bir promosyon kampanyası oluşturun.", "hint": "Bir promosyon kampanyası oluşturun.", "header": "Kampanya Oluştur", "successToast": "'{{name}}' ka<PERSON><PERSON><PERSON><PERSON> başarıyla oluşturuldu."}, "fields": {"name": "Ad", "identifier": "Tanımlayıcı", "start_date": "Başlangıç tarihi", "end_date": "Bitiş tarihi", "total_spend": "<PERSON><PERSON><PERSON> b<PERSON>", "total_used": "Kullanılan bütçe", "budget_limit": "Bütçe limiti", "campaign_id": {"hint": "Sadece promosyon ile aynı para birimine sahip kampanyalar bu listede gösterilir."}}, "budget": {"create": {"hint": "Kampanya iç<PERSON> bir b<PERSON><PERSON><PERSON><PERSON> oluşturun.", "header": "Kampanya Bü<PERSON>ç<PERSON>"}, "details": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"type": "<PERSON><PERSON><PERSON>", "currency": "Para birimi", "limit": "Limit", "used": "Kullanıldı"}, "type": {"spend": {"title": "Harcamalar", "description": "Tüm promosyon kullanımlarının toplam indirimli miktarına bir sınır koyun."}, "usage": {"title": "Kullanım", "description": "Promosyonun kaç kez kullanılabileceğine bir sınır koyun."}}, "edit": {"header": "Kampanya Bütçesini Düzenle"}}, "promotions": {"remove": {"title": "Promosyonu kamp<PERSON>adan kaldır", "description": "Kampanyadan {{count}} promosyon(ları) kaldırmak üzeresiniz. Bu işlem geri alınamaz."}, "alreadyAdded": "Bu promosyon zaten kampanyaya eklenmiştir.", "alreadyAddedDiffCampaign": "Bu promosyon zaten farklı bir kampanyaya eklenmiş ({{name}}).", "currencyMismatch": "Promosyon ve kampanyanın para birimi uyuşmuyor", "toast": {"success": "{{count}} promosyon başarıyla kampanyaya eklendi"}, "add": {"list": {"noRecordsMessage": "Önce bir promosyon oluşturun."}}, "list": {"noRecordsMessage": "Kampanyada promosyon bulunmamaktadır."}}, "deleteCampaignWarning": "{{name}} kampanyasını silmek üzeresiniz. Bu işlem geri alınamaz.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Belirli koşullar için satış veya fiyat geçersiz kılmalar oluşturun.", "delete": {"confirmation": "{{title}} fiyat listesini silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "{{title}} fiyat listesi ba<PERSON><PERSON><PERSON><PERSON>."}, "create": {"header": "Fiyat Listesi Oluştur", "subheader": "Ürünlerinizin fiyatlarını yönetmek için yeni bir fiyat listesi oluşturun.", "tabs": {"details": "Detaylar", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON><PERSON><PERSON>"}, "successToast": "{{title}} fiyat listesi başarıyla oluşturuldu.", "products": {"list": {"noRecordsMessage": "Önce bir ür<PERSON><PERSON>."}}}, "edit": {"header": "Fiyat Listesini <PERSON>le", "successToast": "{{title}} fiyat listesi ba<PERSON>ar<PERSON><PERSON> gü<PERSON>di."}, "configuration": {"header": "Yapılandırma", "edit": {"header": "Fiyat Listesi Yapılandırmasını Düzenle", "description": "Fiyat listesinin yapılandırmasını düzenleyin.", "successToast": "Fiyat listesi yapılandırması başarıyla güncellendi."}}, "products": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actions": {"addProducts": "Ürünleri ekle", "editPrices": "Fiyatları düzenle"}, "delete": {"confirmation_one": "Fiyat listesindeki {{count}} ürün için fiyatları silmek üzeresiniz. Bu işlem geri alınamaz.", "confirmation_other": "Fiyat listesindeki {{count}} ürün için fiyatları silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast_one": "{{count}} <PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "successToast_other": "{{count}} <PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>."}, "add": {"successToast": "Fiyatlar başarıyla fiyat listesine eklendi."}, "edit": {"successToast": "Fiyatlar başarıyla güncellendi."}}, "fields": {"priceOverrides": {"label": "Fiyat geçersiz kılmaları", "header": "Fiyat Geçersiz Kılmaları"}, "status": {"label": "Durum", "options": {"active": "Aktif", "draft": "Taslak", "expired": "S<PERSON><PERSON>i <PERSON>ş", "scheduled": "Planlanmış"}}, "type": {"label": "<PERSON><PERSON><PERSON>", "hint": "Oluşturmak istediğiniz fiyat listesi türünü seçin.", "options": {"sale": {"label": "Satış", "description": "Satış fiyatları ürünler için geçici fiyat değişiklikleridir."}, "override": {"label": "Geçersiz Kılma", "description": "Genellikle müşteri özelinde fiyatlar oluşturmak için kullanılır."}}}, "startsAt": {"label": "Fiyat listesi bir başlangıç tarihine sahip mi?", "hint": "Fiyat listesinin gelecekte aktif olmasını planlayın."}, "endsAt": {"label": "Fiyat listesi bir bitiş tarihine sahip mi?", "hint": "Fiyat listesinin gelecekte devre dışı kalmasını planlayın."}, "customerAvailability": {"header": "Müşteri gruplarını seçin", "label": "Müşteri uygunluğu", "hint": "Fiyat listesinin hangi müşteri gruplarına uygulanacağını seçin.", "placeholder": "Müşteri gruplarını arayın", "attribute": "Müşteri grupları"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Profil detaylarınızı yönetin.", "fields": {"languageLabel": "Dil", "usageInsightsLabel": "Kullanım istatistikleri"}, "edit": {"header": "<PERSON><PERSON>", "languageHint": "Yönetim panelinde kullanmak istediğiniz dili seçin. Bu, mağazanızın dilini <PERSON>ğiştirmez.", "languagePlaceholder": "<PERSON><PERSON>", "usageInsightsHint": "Kullanım istatistiklerini paylaşarak Medusa'nın iyileştirilmesine yardımcı olun. Topladığımız bilgiler ve nasıl kullandığımız hakkında daha fazla bilgiye <0>dokümantasyonda</0> ulaşabilirsiniz."}, "toast": {"edit": "<PERSON><PERSON>iklikleri kaydedildi"}}, "users": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editUser": "Kullanıcıyı Düzenle", "inviteUser": "Kullanıcı Davet Et", "inviteUserHint": "Mağazanıza yeni bir kullanıcı davet edin.", "sendInvite": "<PERSON><PERSON>", "pendingInvites": "<PERSON><PERSON><PERSON>", "deleteInviteWarning": "{{email}} i<PERSON>in daveti silmek üzeresiniz. Bu işlem geri alınamaz.", "resendInvite": "<PERSON><PERSON>", "copyInviteLink": "Davet b<PERSON>ğlantısını kopyala", "expiredOnDate": "{{date}} tari<PERSON><PERSON> süresi doldu", "validFromUntil": "<0>{{from}}</0> - <1>{{until}}</1> a<PERSON><PERSON> ge<PERSON><PERSON><PERSON>", "acceptedOnDate": "{{date}} tari<PERSON><PERSON> kabul edildi", "inviteStatus": {"accepted": "Kabul edildi", "pending": "Beklemede", "expired": "S<PERSON><PERSON>i doldu"}, "roles": {"admin": "Yönetici", "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "member": "Üye"}, "deleteUserWarning": "{{name}} kullanıcısını silmek üzeresiniz. Bu işlem geri alınamaz.", "invite": "<PERSON><PERSON>", "list": {"empty": {"heading": "Kullanıcı bulunamadı", "description": "Bir kullanıcı davet edil<PERSON>, burada görünecektir."}, "filtered": {"heading": "<PERSON><PERSON>ç yok", "description": "Hiçbir kullanıcı mevcut filtre kriterlerine uymuyor."}}, "deleteUserSuccess": "Kullanıcı {{name}} ba<PERSON><PERSON><PERSON><PERSON> silindi"}, "store": {"domain": "Mağaza", "manageYourStoresDetails": "Mağazanızın detaylarını yönetin", "editStore": "Mağazayı Düzenle", "defaultCurrency": "Varsayılan para birimi", "defaultRegion": "Varsayılan bölge", "swapLinkTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON> bağlantı şablonu", "paymentLinkTemplate": "Ödeme bağlantı şablonu", "inviteLinkTemplate": "Davet bağlantı şablonu", "currencies": "Para Birimleri", "addCurrencies": "Para birimleri ekle", "enableTaxInclusivePricing": "Vergi dahil fi<PERSON>tlandırmayı etkinleştir", "disableTaxInclusivePricing": "Vergi dahil fiyatlandırmayı devre dışı bırak", "removeCurrencyWarning_one": "{{count}} para birimini mağazanızdan kaldırmak üzeresiniz. Devam etmeden önce bu para birimiyle tüm fiyatları kaldırdığınızdan emin olun.", "removeCurrencyWarning_other": "{{count}} para birimini mağazanızdan kaldırmak üzeresiniz. Devam etmeden önce bu para birimleriyle tüm fiyatları kaldırdığınızdan emin olun.", "currencyAlreadyAdded": "Para birimi mağazanıza zaten eklenmiş.", "edit": {"header": "Mağazayı Düzenle"}, "toast": {"update": "Mağaza başarıyla güncellendi", "currenciesUpdated": "Para birimleri başarıyla güncellendi", "currenciesRemoved": "Para birimleri mağazadan başarıyla kaldırıldı", "updatedTaxInclusivitySuccessfully": "<PERSON>ergi dahil fi<PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON> g<PERSON>"}, "defaultSalesChannel": "Varsayılan satış kanalı", "defaultLocation": "Varsayılan konum"}, "regions": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> bö<PERSON>, <PERSON>r<PERSON><PERSON><PERSON><PERSON>zi sattığınız bir alandır. Birden fazla ülkeyi kapsayabilir ve farklı vergi oranları, sağlayıcılar ve para birimleri içerebilir.", "createRegion": "<PERSON><PERSON><PERSON>", "createRegionHint": "Belirli ülkeler için vergi oranlarını ve sağlayıcıları yönetin.", "addCountries": "<PERSON><PERSON><PERSON><PERSON>", "editRegion": "<PERSON><PERSON><PERSON>yi <PERSON>", "countriesHint": "<PERSON>u bölgeye dahil edilecek ülkeleri ekleyin.", "deleteRegionWarning": "{{name}} b<PERSON>lgesini silmek üzeresiniz. Bu işlem geri alınamaz.", "removeCountriesWarning_one": "{{count}} ülkeyi bölgeden kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "removeCountriesWarning_other": "{{count}} ülkeyi bölgeden kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "removeCountryWarning": "{{name}} ülkesini bölgeden kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "automaticTaxesHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vergiler yalnızca teslimat adresine göre ödeme sırasında hesaplanır.", "taxInclusiveHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON> fi<PERSON>ar vergi da<PERSON>ı<PERSON>.", "providersHint": "<PERSON>u bölgede kullanılabilir ödeme sağlayıcılarını ekleyin.", "shippingOptions": "<PERSON><PERSON>", "deleteShippingOptionWarning": "{{name}} kargo seçeneğini silmek üzeresiniz. Bu işlem geri alınamaz.", "return": "İade", "outbound": "<PERSON><PERSON><PERSON><PERSON>", "priceType": "<PERSON><PERSON><PERSON>", "flatRate": "Sabit <PERSON>", "calculated": "Hesaplanmış", "list": {"noRecordsMessage": "Satış yaptığı<PERSON><PERSON>z bölgeler için bir bölge oluşturun."}, "toast": {"delete": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON> ka<PERSON>", "create": "<PERSON><PERSON><PERSON> b<PERSON>ş<PERSON><PERSON><PERSON> oluşturuldu", "countries": "<PERSON><PERSON><PERSON> başar<PERSON><PERSON> güncellendi"}, "shippingOption": {"createShippingOption": "Kargo Seçeneği Oluştur", "createShippingOptionHint": "<PERSON><PERSON><PERSON> i<PERSON> yeni bir kargo seçeneği oluşturun.", "editShippingOption": "<PERSON><PERSON>", "fulfillmentMethod": "Karşılama Yöntemi", "type": {"outbound": "<PERSON><PERSON><PERSON><PERSON>", "outboundHint": "Ürünleri müşteriye göndermek için bir kargo seçeneği oluşturuyorsanız kullanın.", "return": "İade", "returnHint": "Müşterinin ürünleri size iade etmesi için bir kargo seçeneği oluşturuyorsanız kullanın."}, "priceType": {"label": "<PERSON><PERSON><PERSON>", "flatRate": "Sabit ücret", "calculated": "Hesaplanmış"}, "availability": {"adminOnly": "Yalnızca yönetici", "adminOnlyHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kargo seçeneği yalnızca yönetim panelinde kullanılabilir, ma<PERSON><PERSON><PERSON>ğ<PERSON>."}, "taxInclusiveHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kargo seçeneğinin fiyatı vergi dahil o<PERSON>ktır.", "requirements": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON> se<PERSON>i için gereksinimleri belirleyin."}}}, "taxes": {"domain": "<PERSON><PERSON><PERSON>", "domainDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "countries": {"taxCountriesHint": "Vergi ayarları listelenen ülkelere uygulanır."}, "settings": {"editTaxSettings": "Vergi Ayarlarını Düzenle", "taxProviderLabel": "Vergi <PERSON>ıcı", "systemTaxProviderLabel": "Sistem Vergi Sağlayıcı", "calculateTaxesAutomaticallyLabel": "Vergileri otomatik olarak hesapla", "calculateTaxesAutomaticallyHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vergi oranları otomatik olarak hesaplanır ve sepete uygulanır. <PERSON><PERSON> dış<PERSON> bırak<PERSON>ld<PERSON>ğ<PERSON><PERSON>, verg<PERSON> ödeme sırasında manuel olarak hesaplanmalıdır. <PERSON>, <PERSON>ç<PERSON>nc<PERSON> taraf vergi sa<PERSON>ıcılarıyla kullanım için önerilir.", "applyTaxesOnGiftCardsLabel": "Hediye kartlarına vergi uygula", "applyTaxesOnGiftCardsHint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hediye kartlarına ödeme sırasında vergi uygulanır. Bazı ülk<PERSON>rde, hediye kartlarının satın alındığında vergi uygulanmasını gerektiren vergi düzenlemeleri vardır.", "defaultTaxRateLabel": "Varsayılan vergi oranı", "defaultTaxCodeLabel": "Varsayılan vergi kodu"}, "defaultRate": {"sectionTitle": "Varsayılan Vergi Oranı"}, "taxRate": {"sectionTitle": "V<PERSON>gi <PERSON>", "createTaxRate": "Vergi Oranı Oluştur", "createTaxRateHint": "<PERSON><PERSON><PERSON> i<PERSON> yeni bir vergi oranı oluşturun.", "deleteRateDescription": "{{name}} vergi oranını silmek üzeresiniz. Bu işlem geri alınamaz.", "editTaxRate": "Vergi Oranını Düzenle", "editRateAction": "Oranı düzenle", "editOverridesAction": "Geçersiz kılmaları düzenle", "editOverridesTitle": "Vergi Oranı Geçersiz Kılmalarını Düzenle", "editOverridesHint": "Vergi oranı için geçersiz kılmaları belirtin.", "deleteTaxRateWarning": "{{name}} vergi oranını silmek üzeresiniz. Bu işlem geri alınamaz.", "productOverridesLabel": "Ürün geçersiz kılmaları", "productOverridesHint": "Vergi oranı için ürün geçersiz kılmalarını belirtin.", "addProductOverridesAction": "Ürün geçersiz kılmaları ekle", "productTypeOverridesLabel": "Ürün türü geçersiz kılmaları", "productTypeOverridesHint": "Vergi oranı için ürün türü geçersiz kılmalarını belirtin.", "addProductTypeOverridesAction": "<PERSON>rün türü geçersiz kılmaları ekle", "shippingOptionOverridesLabel": "Kargo seçeneği geçersiz kılmaları", "shippingOptionOverridesHint": "Vergi oranı için kargo seçeneği geçersiz kılmalarını belirtin.", "addShippingOptionOverridesAction": "Kargo seçeneği geçersiz kılmaları ekle", "productOverridesHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "productTypeOverridesHeader": "<PERSON><PERSON><PERSON><PERSON>", "shippingOptionOverridesHeader": "<PERSON><PERSON>"}}, "locations": {"domain": "Konumlar", "editLocation": "<PERSON><PERSON><PERSON>", "addSalesChannels": "Satış kanalları ekle", "noLocationsFound": "Hiçbir konum bulunamadı", "selectLocations": "Ürünü stoklayan konumları seçin.", "deleteLocationWarning": "{{name}} konumunu silmek üzeresiniz. Bu işlem geri alınamaz.", "removeSalesChannelsWarning_one": "{{count}} satış kanalını konumdan kaldırmak üzeresiniz.", "removeSalesChannelsWarning_other": "{{count}} satış kanalını konumdan kaldırmak üzeresiniz.", "toast": {"create": "Konum başarıyla oluşturuldu", "update": "Konum başarıyla gü<PERSON>llendi", "removeChannel": "Satış kanalı başarıyla kaldırıldı"}}, "reservations": {"domain": "Rezervasyonlar", "subtitle": "Envanter kalemlerinin ayrılan miktarını yönetin.", "deleteWarning": "Bir rezervasyonu silmek üzeresiniz. Bu işlem geri alınamaz."}, "salesChannels": {"domain": "Satış Kanalları", "subtitle": "Ürünlerinizi sattığınız çevrimiçi ve çevrimdışı kanalları yönetin.", "createSalesChannel": "Satış Kanalı Oluştur", "createSalesChannelHint": "Ürünlerinizi satmak için yeni bir satış kanalı oluşturun.", "enabledHint": "Satış kanalının etkin olup olmadığını belirtin.", "removeProductsWarning_one": "{{sales_channel}}'dan {{count}} ürünü kaldırmak üzeresiniz.", "removeProductsWarning_other": "{{sales_channel}}'dan {{count}} ürünü kaldırmak üzeresiniz.", "addProducts": "<PERSON><PERSON><PERSON><PERSON>", "editSalesChannel": "Satış kanalını düzenle", "productAlreadyAdded": "Ürün zaten satış kanalına eklenmiş.", "deleteSalesChannelWarning": "{{name}} satış kanalını silmek üzeresiniz. Bu işlem geri alınamaz.", "toast": {"create": "Satış kanalı başarıyla oluşturuldu", "update": "Satış kanalı başarıyla güncellendi", "delete": "Satış kanalı başarıyla silindi"}, "products": {"list": {"noRecordsMessage": "Satış kanalında ürün bulunmuyor."}, "add": {"list": {"noRecordsMessage": "Önce bir ür<PERSON><PERSON>."}}}, "tooltip": {"cannotDeleteDefault": "Varsayılan satış kanalı silinemez"}}, "apiKeyManagement": {"domain": {"publishable": "Yayınlanabilir API Anahtarları", "secret": "Gizli API Anahtarları"}, "subtitle": {"publishable": "Satış kanallarına yönelik taleplerin kapsamını sınırlamak için mağaza vitrini için kullanılan API anahtarlarını yönetin.", "secret": "Yönetici uygulamalarında yönetici kullanıcıları kimlik doğrulamak için kullanılan API anahtarlarını yönetin."}, "status": {"active": "Aktif", "revoked": "İptal Edildi"}, "type": {"publishable": "Yayınlanabilir", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Yayınlanabilir API Anahtarı Oluştur", "createPublishableHint": "Belirli satış kanallarına yönelik taleplerin kapsamını sınırlamak için yeni bir yayınlanabilir API anahtarı oluşturun.", "createSecretHeader": "Gizli API Anahtarı Oluştur", "createSecretHint": "Doğrulanmış bir yönetici kullanıcısı olarak Medusa API'ye erişmek için yeni bir gizli API anahtarı oluşturun.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON>", "secretKeyCreatedHint": "Yeni gizli anahtarınız oluşturuldu. Şimdi kopyalayın ve güvenli bir şekilde saklayın. Bu anahtar yalnızca bir kez görüntülenecektir.", "copySecretTokenSuccess": "Gizli anahtar panoya kopyalandı.", "copySecretTokenFailure": "Gizli anahtar panoya kopyalanamadı.", "successToast": "API anahtarı başarıyla oluşturuldu."}, "edit": {"header": "API Anahtarını Düzenle", "description": "API anahtarının başlığını düzenleyin.", "successToast": "API anahtarı {{title}} başarıyla güncellendi."}, "salesChannels": {"title": "Satış Kanalları Ekle", "description": "API anahtarının sınırlandırılması gereken satış kanallarını ekleyin.", "successToast_one": "{{count}} satış kanalı API anahtarına başarıyla eklendi.", "successToast_other": "{{count}} satış kanalı API anahtarına başarıyla eklendi.", "alreadyAddedTooltip": "Satış kanalı zaten API anahtarına eklenmiş.", "list": {"noRecordsMessage": "Yayınlanabilir API anahtarının kapsamındaki satış kanalı yok."}}, "delete": {"warning": "{{title}} API anahtarını silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "API anahtarı {{title}} ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "revoke": {"warning": "{{title}} API anahtarını iptal etmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "API anahtarı {{title}} başarıyla iptal edildi."}, "addSalesChannels": {"list": {"noRecordsMessage": "Önce bir satış kanalı oluşturun."}}, "removeSalesChannel": {"warning": "Satış kanalı {{name}}'ı API anahtarından kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "warningBatch_one": "{{count}} satış kanalını API anahtarından kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "warningBatch_other": "{{count}} satış kanalını API anahtarından kaldırmak üzeresiniz. Bu işlem geri alınamaz.", "successToast": "Satış kanalı API anahtarından başarıyla kaldırıldı.", "successToastBatch_one": "{{count}} satış kanalı API anahtarından başarıyla kaldırıldı.", "successToastBatch_other": "{{count}} satış kanalı API anahtarından başarıyla kaldırıldı."}, "actions": {"revoke": "API anahtarını iptal et", "copy": "API anahtarını kopyala", "copySuccessToast": "API anahtarı panoya kopyalandı."}, "table": {"lastUsedAtHeader": "<PERSON>", "createdAtHeader": "İptal Edilme <PERSON>"}, "fields": {"lastUsedAtLabel": "<PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON> zaman", "revokedByLabel": "İptal eden", "revokedAtLabel": "İptal zamanı", "createdByLabel": "Oluşturan"}}, "returnReasons": {"domain": "<PERSON><PERSON>", "subtitle": "İade edilen ürünler için nedenleri yönetin.", "calloutHint": "İadeleri kategorize etmek için nedenleri yönetin.", "editReason": "İade Nedenini Düzenle", "create": {"header": "<PERSON><PERSON>", "subtitle": "En yaygın iade nedenlerini belirtin.", "hint": "İadeleri kategorize etmek için yeni bir iade nedeni oluşturun.", "successToast": "İade nedeni {{label}} başarıyla oluşturuldu."}, "edit": {"header": "İade Nedenini Düzenle", "subtitle": "<PERSON>ade nedeninin değeri<PERSON>.", "successToast": "<PERSON>ade nedeni {{label}} başarı<PERSON> güncellendi."}, "delete": {"confirmation": "{{label}} iade nedenini silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "<PERSON>ade nedeni {{label}} ba<PERSON><PERSON><PERSON><PERSON> silind<PERSON>."}, "fields": {"value": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "yan<PERSON><PERSON><PERSON>_beden", "tooltip": "<PERSON><PERSON><PERSON>, iade nedeni i<PERSON>in ben<PERSON> bir tanımlayıcı olmalıdır."}, "label": {"label": "Etiket", "placeholder": "<PERSON><PERSON><PERSON><PERSON> beden"}, "description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>", "placeholder": "Müşteri yanlış beden aldı"}}}, "login": {"forgotPassword": "Şifrenizi mi unuttunuz? - <0>Sıfırla</0>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> alanına erişmek için oturum açın"}, "invite": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Aşağıda hesabınızı oluşturun", "backToLogin": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>n", "createAccount": "<PERSON><PERSON><PERSON>", "alreadyHaveAccount": "Zaten bir hesabın<PERSON>z var mı? - <0><PERSON><PERSON><PERSON></0>", "emailTooltip": "E-postanız değiştirilemez. Başka bir e-posta kullanmak istiyorsanız, yeni bir davet gönderilmelidir.", "invalidInvite": "Davet geçersiz veya süresi dolmuş.", "successTitle": "Hesabı<PERSON><PERSON>z ka<PERSON>", "successHint": "<PERSON><PERSON>ö<PERSON> ile başlayın.", "successAction": "Medusa Yöneticiye Başla", "invalidTokenTitle": "<PERSON>t <PERSON> g<PERSON>çersiz", "invalidTokenHint": "Yeni bir davet bağlantısı talep etmeyi deneyin.", "passwordMismatch": "<PERSON><PERSON><PERSON><PERSON>", "toast": {"accepted": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> kabul edildi"}}, "resetPassword": {"title": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "hint": "Aşağıya e-postanızı girin, şifrenizi nasıl sıfırlayacağınızı anlatan talimatları size göndereceğiz.", "email": "E-posta", "sendResetInstructions": "Sıfırlama talimatlarını gönder", "backToLogin": "<0><PERSON><PERSON><PERSON><PERSON></0>", "newPasswordHint": "Aşağıda yeni bir şif<PERSON> se<PERSON>.", "invalidTokenTitle": "Şifre sıfırlama tokeniniz geçersiz", "invalidTokenHint": "Yeni bir sıfırlama bağlantısı talep etmeyi deneyin.", "expiredTokenTitle": "Şifre sıfırlama tokeninizin süresi dolmuş", "goToResetPassword": "Şifre Sıfırla'ya Git", "resetPassword": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "<PERSON><PERSON> et", "tokenExpiresIn": "Token <0>{{time}}</0> da<PERSON><PERSON> i<PERSON>e sona eriyor", "successfulRequestTitle": "<PERSON>ze başarıyla bir e-posta gönderdik", "successfulRequest": "Şifrenizi sıfırlamak için kullanabileceğiniz bir e-posta gönderdik. Birkaç dakika içinde almazsanız, spam klasörünüzü kontrol edin.", "successfulResetTitle": "Şifre sıfırlama başarılı", "successfulReset": "Lütfen giriş sayfasında oturum açın.", "passwordMismatch": "<PERSON><PERSON><PERSON><PERSON>", "invalidLinkTitle": "Şifre sıfırlama bağlantınız geçersiz", "invalidLinkHint": "Şifrenizi yeniden sıfırlamayı deneyin."}, "workflowExecutions": {"domain": "İş Akışları", "subtitle": "Medusa uygulamanızdaki iş akışı yürütmelerini görüntüleyin ve takip edin.", "transactionIdLabel": "İşlem Kimliği", "workflowIdLabel": "İş Akışı Kimliği", "progressLabel": "<PERSON><PERSON><PERSON><PERSON>", "stepsCompletedLabel_one": "{{count}} adımın {{completed}}'i tama<PERSON><PERSON>ı", "stepsCompletedLabel_other": "{{count}} adımın {{completed}}'i tama<PERSON><PERSON>ı", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>z herhangi bir iş akışı yürütülmedi."}, "history": {"sectionTitle": "Geçmiş", "runningState": "Çalışıyor...", "awaitingState": "Bekleniyor", "failedState": "Başarısız", "skippedState": "Atlandı", "skippedFailureState": "Atlandı (Başarısızlık)", "definitionLabel": "Tanım", "outputLabel": "Çıktı", "compensateInputLabel": "Tel<PERSON><PERSON> gir<PERSON>i", "revertedLabel": "<PERSON><PERSON>", "errorLabel": "<PERSON><PERSON>"}, "state": {"done": "Tamamlandı", "failed": "Başarısız", "reverted": "<PERSON><PERSON>", "invoking": "Çağırılıyor", "compensating": "Telafi ediliyor", "notStarted": "Başlamadı"}, "transaction": {"state": {"waitingToCompensate": "Telafi i<PERSON> be<PERSON>"}}, "step": {"state": {"skipped": "Atlandı", "skippedFailure": "Atlandı (Başarısızlık)", "dormant": "Beklemede", "timeout": "Zaman aşımı"}}}, "productTypes": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Ürünlerinizi türlere göre organize edin.", "create": {"header": "<PERSON><PERSON><PERSON><PERSON> Tü<PERSON>", "hint": "Ürünlerinizi kategorize etmek için yeni bir ürün türü oluşturun.", "successToast": "<PERSON><PERSON><PERSON><PERSON> türü {{value}} başarıyla oluşturuldu."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "<PERSON><PERSON><PERSON><PERSON> türü {{value}} başarı<PERSON> güncellendi."}, "delete": {"confirmation": "{{value}} ürün türünü silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON><PERSON><PERSON> türü {{value}} ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "fields": {"value": "<PERSON><PERSON><PERSON>"}}, "productTags": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Ürün Etiketi Oluştur", "subtitle": "Ürünlerinizi kategorize etmek için yeni bir ürün etiketi oluşturun.", "successToast": "<PERSON><PERSON><PERSON><PERSON> etiketi {{value}} başarıyla oluşturuldu."}, "edit": {"header": "Ürün Etiketini Düzenle", "subtitle": "<PERSON><PERSON><PERSON><PERSON> et<PERSON><PERSON>.", "successToast": "<PERSON><PERSON><PERSON><PERSON> etiketi {{value}} başar<PERSON><PERSON> gü<PERSON>di."}, "delete": {"confirmation": "{{value}} ürün etiketini silmek üzeresiniz. Bu işlem geri alınamaz.", "successToast": "<PERSON><PERSON><PERSON><PERSON> etiketi {{value}} ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>."}, "fields": {"value": "<PERSON><PERSON><PERSON>"}}, "notifications": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "emptyState": {"title": "<PERSON><PERSON><PERSON><PERSON> yok", "description": "<PERSON><PERSON> anda herhangi bir bildiri<PERSON>z yok, ancak olduğunda burada görünecekler."}, "accessibility": {"description": "Medusa aktiviteleri hakkında bildirimler burada listelenecek."}}, "errors": {"serverError": "<PERSON><PERSON><PERSON> hat<PERSON> - <PERSON><PERSON> sonra tekrar deney<PERSON>.", "invalidCredentials": "Yanlış e-posta veya şifre"}, "statuses": {"scheduled": "Planlandı", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktif", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Devre Dışı", "inactive": "<PERSON><PERSON><PERSON>", "draft": "Taslak"}, "labels": {"productVariant": "<PERSON><PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON><PERSON><PERSON>", "available": "Mevcut", "inStock": "Stokta Var", "added": "Eklendi", "removed": "Kaldırıldı", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "beaware": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor"}, "fields": {"amount": "<PERSON><PERSON><PERSON>", "refundAmount": "İade miktarı", "name": "İsim", "default": "Varsayılan", "lastName": "Soyadı", "firstName": "Ad<PERSON>", "title": "Başlık", "customTitle": "<PERSON><PERSON> başlık", "manageInventory": "<PERSON><PERSON><PERSON>", "inventoryKit": "<PERSON><PERSON><PERSON> seti var", "inventoryItems": "<PERSON><PERSON><PERSON>", "inventoryItem": "<PERSON><PERSON><PERSON>", "requiredQuantity": "<PERSON><PERSON><PERSON><PERSON> miktar", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "email": "E-posta", "password": "Şifre", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "repeatNewPassword": "<PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON>", "configurations": "Yapılandırmalar", "conditions": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "collection": "Koleksiyon", "discountable": "İndirim uygulanabilir", "handle": "Tanıtıcı", "subtitle": "Alt başlık", "item": "<PERSON><PERSON><PERSON>", "qty": "adet.", "limit": "Limit", "tags": "<PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "reason": "Sebep", "none": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON><PERSON>", "search": "Ara", "percentage": "<PERSON><PERSON>z<PERSON>", "sales_channels": "Satış Kanalları", "customer_groups": "Müşteri Grupları", "product_tags": "<PERSON><PERSON><PERSON><PERSON>", "product_types": "<PERSON><PERSON><PERSON><PERSON>", "product_collections": "<PERSON><PERSON><PERSON>n <PERSON>", "status": "Durum", "code": "Kod", "value": "<PERSON><PERSON><PERSON>", "disabled": "<PERSON><PERSON> dı<PERSON>ı", "dynamic": "Dinamik", "normal": "Normal", "years": "<PERSON><PERSON><PERSON>", "months": "<PERSON><PERSON><PERSON>", "days": "<PERSON><PERSON><PERSON><PERSON>", "hours": "<PERSON><PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "totalRedemptions": "Toplam Kullanım", "countries": "Ülkeler", "paymentProviders": "Ödeme Sağlayıcıları", "refundReason": "<PERSON><PERSON>", "fulfillmentProviders": "Tamamlama Sağlayıcıları", "fulfillmentProvider": "Tamamlama Sağlayıcısı", "providers": "Sağlayıcılar", "availability": "Mevcudiyet", "inventory": "<PERSON><PERSON><PERSON>", "optional": "Opsiyonel", "note": "Not", "automaticTaxes": "Otomatik Vergiler", "taxInclusivePricing": "<PERSON><PERSON><PERSON> da<PERSON>", "currency": "Para Birimi", "address": "<PERSON><PERSON>", "address2": "<PERSON><PERSON>, apartman vb.", "city": "Şehir", "postalCode": "Posta Kodu", "country": "<PERSON><PERSON><PERSON>", "state": "Eyalet", "province": "İl", "company": "Şirket", "phone": "Telefon", "metadata": "Meta veri", "selectCountry": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variants": "<PERSON><PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "total": "Toplam Sipariş", "paidTotal": "Toplam tahsilat", "totalExclTax": "<PERSON><PERSON><PERSON>", "subtotal": "<PERSON>", "shipping": "<PERSON><PERSON>", "outboundShipping": "<PERSON><PERSON>", "returnShipping": "<PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "customer": "Müş<PERSON>i", "date": "<PERSON><PERSON><PERSON>", "order": "Sipariş", "fulfillment": "<PERSON><PERSON><PERSON><PERSON>", "provider": "Sağlayıcı", "payment": "Ödeme", "items": "<PERSON><PERSON><PERSON>r", "salesChannel": "Satış Kanalı", "region": "<PERSON><PERSON><PERSON>", "discount": "İndirim", "role": "Rol", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "salesChannels": "Satış Kanalları", "product": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revokedAt": "<PERSON><PERSON>", "true": "Do<PERSON><PERSON>", "false": "Yanlış", "giftCard": "Hediye Kartı", "tag": "Etiket", "dateIssued": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issuedDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expiryDate": "<PERSON>", "price": "<PERSON><PERSON><PERSON>", "priceTemplate": "Fiyat {{regionOrCurrency}}", "height": "Yükseklik", "width": "Genişlik", "length": "Uzunluk", "weight": "Ağırlık", "midCode": "MID kodu", "hsCode": "HS kodu", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "<PERSON><PERSON><PERSON> mi<PERSON>", "barcode": "Barkod", "countryOfOrigin": "<PERSON><PERSON><PERSON>", "material": "Malzeme", "thumbnail": "Küçük resim", "sku": "SKU", "managedInventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowBackorder": "<PERSON><PERSON> sip<PERSON> izin ver", "inStock": "<PERSON><PERSON><PERSON> var", "location": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON><PERSON><PERSON>", "minSubtotal": "Minimum Ara Toplam", "maxSubtotal": "<PERSON><PERSON><PERSON><PERSON>", "shippingProfile": "<PERSON><PERSON>", "summary": "Özet", "details": "Detaylar", "label": "Etiket", "rate": "<PERSON><PERSON>", "requiresShipping": "<PERSON><PERSON> gerektirir", "unitPrice": "<PERSON><PERSON><PERSON>", "startDate": "Başlangıç <PERSON>", "endDate": "Bitiş Tarihi", "draft": "Taslak", "values": "<PERSON><PERSON><PERSON><PERSON>", "by": "Tara<PERSON><PERSON><PERSON><PERSON>"}, "dateTime": {"years_one": "<PERSON><PERSON><PERSON>", "years_other": "<PERSON><PERSON><PERSON>", "months_one": "Ay", "months_other": "<PERSON><PERSON><PERSON>", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "Haftalar", "days_one": "<PERSON><PERSON><PERSON>", "days_other": "<PERSON><PERSON><PERSON><PERSON>", "hours_one": "Saat", "hours_other": "<PERSON><PERSON><PERSON>", "minutes_one": "Dakika", "minutes_other": "<PERSON><PERSON><PERSON><PERSON>", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "<PERSON><PERSON><PERSON><PERSON>"}}