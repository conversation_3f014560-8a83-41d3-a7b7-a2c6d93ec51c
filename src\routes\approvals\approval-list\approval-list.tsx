import { Container, Heading } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { SingleColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { ApprovalsTable } from "./components"

export const ApprovalList = () => {
  const { t } = useTranslation()
  const { getWidgets } = useExtension()

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("approval.list.after"),
        before: getWidgets("approval.list.before"),
      }}
    >
      <Container className="flex flex-col p-0 overflow-hidden">
        <div className="p-6 pb-0">
          <Heading className="font-sans font-medium h1-core">
            {t("approvals.title", "Approvals")}
          </Heading>
        </div>
        <ApprovalsTable />
      </Container>
    </SingleColumnPage>
  )
}