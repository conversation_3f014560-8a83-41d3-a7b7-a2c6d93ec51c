import { SingleColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { ApprovalsTable } from "./components"

export const ApprovalList = () => {
  const { getWidgets } = useExtension()

  return (
    <SingleColumnPage
      widgets={{
        after: getWidgets("approval.list.after"),
        before: getWidgets("approval.list.before"),
      }}
    >
      <ApprovalsTable />
    </SingleColumnPage>
  )
}