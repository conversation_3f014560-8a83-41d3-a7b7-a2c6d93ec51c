import { Select } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Approval } from "../../../../types"

interface ApprovalsTableFiltersProps {
  status?: Approval["status"]
  onStatusChange: (status?: Approval["status"]) => void
}

export const ApprovalsTableFilters = ({
  status,
  onStatusChange,
}: ApprovalsTableFiltersProps) => {
  const { t } = useTranslation()

  const statusOptions = [
    { value: "", label: t("general.all", "All") },
    { 
      value: "pending", 
      label: t("approvals.status.pending", "Pending") 
    },
    { 
      value: "approved", 
      label: t("approvals.status.approved", "Approved") 
    },
    { 
      value: "rejected", 
      label: t("approvals.status.rejected", "Rejected") 
    },
    { 
      value: "expired", 
      label: t("approvals.status.expired", "Expired") 
    },
  ]

  return (
    <div className="flex items-center gap-2 p-4">
      <Select
        value={status || ""}
        onValueChange={(value) => 
          onStatusChange(value === "" ? undefined : value as Approval["status"])
        }
      >
        <Select.Trigger className="w-48">
          <Select.Value placeholder={t("approvals.filters.status", "Filter by status")} />
        </Select.Trigger>
        <Select.Content>
          {statusOptions.map((option) => (
            <Select.Item key={option.value} value={option.value}>
              {option.label}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  )
}