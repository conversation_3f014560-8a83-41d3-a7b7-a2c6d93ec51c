import { <PERSON>lip<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "@medusajs/icons"
import { DropdownMenu, IconButton, toast, usePrompt } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Company, Employee } from "../../../../../types"
import { useDeleteEmployee } from "../../../../../hooks/api/companies"
import { EmployeesUpdateDrawer } from "./employees-update-drawer"

interface EmployeesActionsMenuProps {
  company: Company
  employee: Employee
}

export const EmployeesActionsMenu = ({ company, employee }: EmployeesActionsMenuProps) => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const [editOpen, setEditOpen] = useState(false)

  const { mutateAsync: deleteEmployee, isPending: loadingDelete } = useDeleteEmployee(
    company.id,
    employee.id
  )

  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: `确定要删除员工 ${employee.customer.first_name} ${employee.customer.last_name} 吗？`,
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    await deleteEmployee(undefined, {
      onSuccess: () => {
        toast.success("员工删除成功")
      },
      onError: (e) => {
        toast.error(e.message)
      },
    })
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <IconButton size="small" variant="transparent">
            <EllipsisHorizontal />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content>
          <DropdownMenu.Item
            className="gap-x-2"
            onClick={() => setEditOpen(true)}
          >
            <PencilSquare className="text-ui-fg-subtle" />
            {t("actions.edit")}
          </DropdownMenu.Item>
          <DropdownMenu.Separator />
          <DropdownMenu.Item
            className="gap-x-2"
            onClick={handleDelete}
            disabled={loadingDelete}
          >
            <Trash className="text-ui-fg-subtle" />
            {t("actions.delete")}
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu>
      <EmployeesUpdateDrawer
        company={company}
        employee={employee}
        open={editOpen}
        setOpen={setEditOpen}
      />
    </>
  )
}
