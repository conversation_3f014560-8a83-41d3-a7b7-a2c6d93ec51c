{"$schema": "./$schema.json", "general": {"ascending": "Ascendente", "descending": "Descendente", "add": "Agregar", "start": "Iniciar", "end": "Finalizar", "open": "Abrir", "close": "<PERSON><PERSON><PERSON>", "apply": "Aplicar", "range": "Ra<PERSON>", "search": "Buscar", "of": "de", "results": "resultados", "pages": "p<PERSON><PERSON><PERSON>", "next": "Siguient<PERSON>", "prev": "Anterior", "is": "es", "timeline": "Cronograma", "success": "Éxito", "warning": "Advertencia", "tip": "Consejo", "error": "Error", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "active": "Activo", "revoked": "Revocado", "new": "Nuevo", "modified": "Modificado", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "Eliminado", "admin": "Administrador", "store": "Tienda", "details": "Detalles", "items_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "countSelected": "{{count}} se<PERSON><PERSON><PERSON><PERSON>(s)", "countOfTotalSelected": "{{count}} de {{total}} seleccionado(s)", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} más", "areYouSure": "¿Estás seguro?", "noRecordsFound": "No se encontraron registros", "typeToConfirm": "Por favor escribe {val} para confirmar:", "noResultsTitle": "Sin resultados", "noResultsMessage": "Intenta cambiar los filtros o la consulta de búsqueda", "noSearchResults": "No se encontraron resultados", "noSearchResultsFor": "No se encontraron resultados para <0>'{{query}}'</0>", "noRecordsTitle": "No hay registros", "noRecordsMessage": "No hay registros para mostrar", "unsavedChangesTitle": "¿Estás seguro de que deseas abandonar este formulario?", "unsavedChangesDescription": "Tienes cambios no guardados que se perderán si sales de este formulario.", "includesTaxTooltip": "Los precios en esta columna incluyen impuestos.", "excludesTaxTooltip": "Los precios en esta columna no incluyen impuestos.", "noMoreData": "No hay más datos"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} clave", "numberOfKeys_other": "{{count}} claves", "drawer": {"header_one": "JSON <0>· {{count}} clave</0>", "header_other": "JSON <0>· {{count}} claves</0>", "description": "Ver los datos JSON de este objeto."}}, "metadata": {"header": "Metadatos", "numberOfKeys_one": "{{count}} clave", "numberOfKeys_other": "{{count}} claves", "edit": {"header": "<PERSON><PERSON>", "description": "Edita los metadatos de este objeto.", "successToast": "Los metadatos se actualizaron correctamente.", "actions": {"insertRowAbove": "Insertar fila arriba", "insertRowBelow": "Insertar fila abajo", "deleteRow": "Eliminar fila"}, "labels": {"key": "Clave", "value": "Valor"}, "complexRow": {"label": "Algunas filas están deshabilitadas", "description": "Este objeto contiene metadatos no primitivos, como arreglos u objetos, que no se pueden editar aquí. Para editar las filas deshabilitadas, usa la API directamente.", "tooltip": "Esta fila está deshabilitada porque contiene datos no primitivos."}}}, "validation": {"mustBeInt": "El valor debe ser un número entero.", "mustBePositive": "El valor debe ser un número positivo."}, "actions": {"save": "Guardar", "saveAsDraft": "Guardar como borrador", "copy": "Copiar", "copied": "Copiado", "duplicate": "Duplicar", "publish": "Publicar", "create": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "remove": "<PERSON><PERSON><PERSON>", "revoke": "Revocar", "cancel": "<PERSON><PERSON><PERSON>", "forceConfirm": "Forzar <PERSON>ac<PERSON>", "continueEdit": "Continuar edición", "enable": "Habilitar", "disable": "Deshabilitar", "undo": "<PERSON><PERSON><PERSON>", "complete": "Completar", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "back": "Atrás", "close": "<PERSON><PERSON><PERSON>", "showMore": "Mostrar más", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "Con<PERSON><PERSON><PERSON> con Email", "idCopiedToClipboard": "ID copiado al portapapeles", "addReason": "Agregar motivo", "addNote": "Agregar nota", "reset": "Restablecer", "confirm": "Confirmar", "edit": "<PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "clear": "Bo<PERSON>r", "clearAll": "<PERSON><PERSON><PERSON> todo", "apply": "Aplicar", "add": "Agregar", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "browse": "Explorar", "logout": "<PERSON><PERSON><PERSON>", "hide": "Ocultar", "export": "Exportar", "import": "Importar"}, "operators": {"in": "En"}, "app": {"search": {"label": "Buscar", "title": "Buscar", "description": "Busca en toda tu tienda, incluyendo pedidos, productos, clientes y más.", "allAreas": "Todas las áreas", "navigation": "Navegación", "openResult": "Abrir resultado", "showMore": "Mostrar más", "placeholder": "Salta a o busca cualquier cosa...", "noResultsTitle": "No se encontraron resultados", "noResultsMessage": "No pudimos encontrar nada que coincidiera con tu búsqueda.", "emptySearchTitle": "Escribe para buscar", "emptySearchMessage": "Ingresa una palabra clave o frase para explorar.", "loadMore": "<PERSON>gar {{count}} más", "groups": {"all": "Todas las áreas", "customer": "Clientes", "customerGroup": "Grupos de clientes", "product": "Productos", "productVariant": "Variantes de productos", "inventory": "Inventario", "reservation": "<PERSON><PERSON><PERSON>", "category": "Categorías", "collection": "Colecciones", "order": "Pedidos", "promotion": "Promociones", "campaign": "Campañas", "priceList": "Listas de precios", "user": "Usuarios", "region": "Regiones", "taxRegion": "Regiones fiscales", "returnReason": "Motivos de devolución", "salesChannel": "Canales de venta", "productType": "Tipos de productos", "productTag": "Etiquetas de productos", "location": "Ubicaciones", "shippingProfile": "<PERSON><PERSON><PERSON> de env<PERSON>", "publishableApiKey": "Claves API publicables", "secretApiKey": "Claves API secretas", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navegación"}}, "keyboardShortcuts": {"pageShortcut": "Saltar a", "settingShortcut": "Configuraciones", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "entonces", "navigation": {"goToOrders": "Pedidos", "goToProducts": "Productos", "goToCollections": "Colecciones", "goToCategories": "Categorías", "goToCustomers": "Clientes", "goToCustomerGroups": "Grupos de clientes", "goToInventory": "Inventario", "goToReservations": "<PERSON><PERSON><PERSON>", "goToPriceLists": "Listas de precios", "goToPromotions": "Promociones", "goToCampaigns": "Campañas"}, "settings": {"goToSettings": "Configuraciones", "goToStore": "Tienda", "goToUsers": "Usuarios", "goToRegions": "Regiones", "goToTaxRegions": "Regiones fiscales", "goToSalesChannels": "Canales de venta", "goToProductTypes": "Tipos de productos", "goToLocations": "Ubicaciones", "goToPublishableApiKeys": "Claves API publicables", "goToSecretApiKeys": "Claves API secretas", "goToWorkflows": "Flujos de trabajo", "goToProfile": "Perfil", "goToReturnReasons": "Motivos de devolución"}}, "menus": {"user": {"documentation": "Documentación", "changelog": "Registro de cambios", "shortcuts": "<PERSON><PERSON><PERSON>", "profileSettings": "Configuraciones del perfil", "theme": {"label": "<PERSON><PERSON>", "dark": "Oscuro", "light": "<PERSON><PERSON><PERSON>", "system": "Sistema"}}, "store": {"label": "Tienda", "storeSettings": "Configuraciones de la tienda"}, "actions": {"logout": "<PERSON><PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "Navegación", "description": "Menú de navegación para el panel."}, "common": {"extensions": "Extensiones"}, "main": {"store": "Tienda", "storeSettings": "Configuraciones de la tienda"}, "settings": {"header": "Configuraciones", "general": "General", "developer": "Desarrollador", "myAccount": "Mi cuenta"}}}, "dataGrid": {"columns": {"view": "<PERSON>er", "resetToDefault": "Restablecer a predeterminado", "disabled": "Cambiar qué columnas son visibles está deshabilitado."}, "shortcuts": {"label": "<PERSON><PERSON><PERSON>", "commands": {"undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "paste": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "clear": "Bo<PERSON>r", "moveUp": "Mover hacia arriba", "moveDown": "Mover hacia abajo", "moveLeft": "Mover hacia la izquierda", "moveRight": "Mover hacia la derecha", "moveTop": "Mover al principio", "moveBottom": "Mover al final", "selectDown": "<PERSON><PERSON><PERSON><PERSON>r hacia abajo", "selectUp": "Seleccionar hacia arriba", "selectColumnDown": "Seleccionar columna hacia abajo", "selectColumnUp": "Seleccionar columna hacia arriba", "focusToolbar": "<PERSON><PERSON><PERSON> de <PERSON>", "focusCancel": "<PERSON><PERSON><PERSON>"}}, "errors": {"fixError": "Arreglar error", "count_one": "{{count}} error", "count_other": "{{count}} errores"}}, "filters": {"date": {"today": "Hoy", "lastSevenDays": "Últimos 7 días", "lastThirtyDays": "Últimos 30 días", "lastNinetyDays": "Últimos 90 días", "lastTwelveMonths": "Últimos 12 meses", "custom": "Personalizado", "from": "De", "to": "A"}, "compare": {"lessThan": "<PERSON><PERSON> que", "greaterThan": "Mayor que", "exact": "Exacto", "range": "Ra<PERSON>", "lessThanLabel": "menor que {{value}}", "greaterThanLabel": "mayor que {{value}}", "andLabel": "y"}, "radio": {"yes": "Sí", "no": "No", "true": "Verdadero", "false": "<PERSON><PERSON><PERSON>"}, "addFilter": "Agregar filtro"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON> incorrecta", "badRequestMessage": "El servidor no pudo entender la solicitud debido a una sintaxis incorrecta.", "notFoundTitle": "404 - No hay página en esta dirección", "notFoundMessage": "Verifica la URL e intenta nuevamente, o usa la barra de búsqueda para encontrar lo que buscas.", "internalServerErrorTitle": "500 - E<PERSON>r interno del servidor", "internalServerErrorMessage": "Ocurrió un error inesperado en el servidor. Intenta nuevamente más tarde.", "defaultTitle": "Ocurrió un error", "defaultMessage": "Ocurrió un error inesperado mientras se renderizaba esta página.", "noMatchMessage": "La página que buscas no existe.", "backToDashboard": "Volver al panel"}, "addresses": {"title": "Direcciones", "shippingAddress": {"header": "Dirección de envío", "editHeader": "Editar dirección de envío", "editLabel": "Dirección de envío", "label": "Dirección de envío"}, "billingAddress": {"header": "Dirección de facturación", "editHeader": "Editar dirección de facturación", "editLabel": "Dirección de facturación", "label": "Dirección de facturación", "sameAsShipping": "Misma que la dirección de envío"}, "contactHeading": "Contacto", "locationHeading": "Ubicación"}, "email": {"editHeader": "Editar correo electrónico", "editLabel": "Correo electrónico", "label": "Correo electrónico"}, "transferOwnership": {"header": "Transferir propiedad", "label": "Transferir propiedad", "details": {"order": "Detalles del pedido", "draft": "Detalles del borrador"}, "currentOwner": {"label": "Propietario actual", "hint": "El propietario actual del pedido."}, "newOwner": {"label": "Nuevo propietario", "hint": "El nuevo propietario al que transferir el pedido."}, "validation": {"mustBeDifferent": "El nuevo propietario debe ser diferente al propietario actual.", "required": "El nuevo propietario es obligatorio."}}, "sales_channels": {"availableIn": "Disponible en <0>{{x}}</0> de <1>{{y}}</1> canales de venta"}, "products": {"domain": "Productos", "list": {"noRecordsMessage": "Crea tu primer producto para comenzar a vender."}, "edit": {"header": "<PERSON><PERSON>", "description": "Edita los detalles del producto.", "successToast": "Producto {{title}} actualizado correctamente."}, "create": {"title": "<PERSON><PERSON><PERSON>", "description": "Crea un nuevo producto.", "header": "General", "tabs": {"details": "Detalles", "organize": "Organizar", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Kits de inventario"}, "errors": {"variants": "Por favor, selecciona al menos una variante.", "options": "Por favor, crea al menos una opción.", "uniqueSku": "El SKU debe ser único."}, "inventory": {"heading": "Kits de inventario", "label": "Agrega ítems de inventario al kit de inventario de la variante.", "itemPlaceholder": "Selecciona ítem de inventario", "quantityPlaceholder": "¿Cuántos de estos se necesitan para el kit?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "Sí, este es un producto con variantes", "subHeadingDescription": "Si no está marcado, crearemos una variante predeterminada para ti", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "Pequeño, Mediano, Grande"}, "productVariants": {"label": "Variantes de producto", "hint": "Este orden afectará el orden de las variantes en tu tienda.", "alert": "Agrega opciones para crear variantes.", "tip": "Las variantes sin marcar no serán creadas. Siempre puedes crear y editar variantes después, pero esta lista se adapta a las variaciones en tus opciones de producto."}, "productOptions": {"label": "Opciones de producto", "hint": "Define las opciones del producto, por ejemplo, color, tamaño, etc."}}, "successToast": "Producto {{title}} creado correctamente."}, "export": {"header": "Exportar lista de productos", "description": "Exporta la lista de productos a un archivo CSV.", "success": {"title": "Estamos procesando tu exportación", "description": "Exportar los datos puede tomar unos minutos. Te notificaremos cuando hayamos terminado."}, "filters": {"title": "<PERSON><PERSON><PERSON>", "description": "Aplica filtros en la vista de la tabla para ajustar esta vista"}, "columns": {"title": "Columnas", "description": "Personaliza los datos exportados para cumplir con necesidades específicas"}}, "import": {"header": "Importar lista de productos", "uploadLabel": "Importar Productos", "uploadHint": "Arrastra y suelta un archivo CSV o haz clic para cargar", "description": "Importa productos proporcionando un archivo CSV en un formato predefinido", "template": {"title": "¿No estás seguro de cómo organizar tu lista?", "description": "Descarga la plantilla a continuación para asegurarte de seguir el formato correcto."}, "upload": {"title": "Subir un archivo CSV", "description": "A través de las importaciones puedes agregar o actualizar productos. Para actualizar productos existentes debes usar el identificador y ID existentes. Se te pedirá confirmación antes de que importemos los productos.", "preprocessing": "Preprocesando...", "productsToCreate": "Productos serán creados", "productsToUpdate": "Productos serán actualizados"}, "success": {"title": "Estamos procesando tu importación", "description": "Importar los datos puede tardar un poco. Te notificaremos cuando hayamos terminado."}}, "deleteWarning": "Estás a punto de eliminar el producto {{title}}. Esta acción no puede deshacerse.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "No hay variantes", "description": "No hay variantes para mostrar."}, "filtered": {"heading": "No hay resultados", "description": "No hay variantes que coincidan con los criterios de filtro actuales."}}, "attributes": "Atributos", "editAttributes": "<PERSON><PERSON>", "editOptions": "Editar Opciones", "editPrices": "<PERSON><PERSON>", "media": {"label": "Medios", "editHint": "Agrega medios al producto para mostrarlo en tu tienda.", "makeThumbnail": "Crear miniatura", "uploadImagesLabel": "Subir imágenes", "uploadImagesHint": "Arrastra y suelta imágenes aquí o haz clic para cargar.", "invalidFileType": "'{{name}}' no es un tipo de archivo compatible. Los tipos de archivo soportados son: {{types}}.", "failedToUpload": "Error al subir los medios agregados. Por favor, intenta nuevamente.", "deleteWarning_one": "Estás a punto de eliminar {{count}} imagen. Esta acción no puede deshacerse.", "deleteWarning_other": "Estás a punto de eliminar {{count}} imágenes. Esta acción no puede deshacerse.", "deleteWarningWithThumbnail_one": "Estás a punto de eliminar {{count}} imagen incluyendo la miniatura. Esta acción no puede deshacerse.", "deleteWarningWithThumbnail_other": "Estás a punto de eliminar {{count}} imágenes incluyendo la miniatura. Esta acción no puede deshacerse.", "thumbnailTooltip": "Miniatura", "galleryLabel": "Galería", "downloadImageLabel": "<PERSON><PERSON><PERSON> imagen actual", "deleteImageLabel": "Eliminar imagen actual", "emptyState": {"header": "Aún no hay medios", "description": "Agrega medios al producto para mostrarlo en tu tienda.", "action": "Agregar medios"}, "successToast": "Medios actualizados correctamente."}, "discountableHint": "Si no está marcado, no se aplicarán descuentos a este producto.", "noSalesChannels": "No disponible en ningún canal de venta", "variantCount_one": "{{count}} variante", "variantCount_other": "{{count}} variantes", "deleteVariantWarning": "Estás a punto de eliminar la variante {{title}}. Esta acción no puede deshacerse.", "productStatus": {"draft": "<PERSON><PERSON><PERSON>", "published": "Publicado", "proposed": "Propuesto", "rejected": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Dale a tu producto un título corto y claro.<0/>Se recomienda una longitud de 50-60 caracteres para los motores de búsqueda.", "placeholder": "Chaqueta de invierno"}, "subtitle": {"label": "Subtítulo", "placeholder": "Cálido y acogedor"}, "handle": {"label": "Manejador", "tooltip": "El manejador se usa para hacer referencia al producto en tu tienda. Si no se especifica, el manejador será generado a partir del título del producto.", "placeholder": "chaqueta-de-invierno"}, "description": {"label": "Descripción", "hint": "Dale a tu producto una descripción corta y clara.<0/>Se recomienda una longitud de 120-160 caracteres para los motores de búsqueda.", "placeholder": "Una chaqueta cálida y acogedora"}, "discountable": {"label": "Descuento aplicable", "hint": "Si no está marcado, no se aplicarán descuentos a este producto"}, "type": {"label": "Tipo"}, "collection": {"label": "Colección"}, "categories": {"label": "Categorías"}, "tags": {"label": "Etiquetas"}, "sales_channels": {"label": "Canales de venta", "hint": "Este producto solo estará disponible en el canal de ventas predeterminado si no se modifica."}, "countryOrigin": {"label": "<PERSON><PERSON> de origen"}, "material": {"label": "Material"}, "width": {"label": "<PERSON><PERSON>"}, "length": {"label": "Largo"}, "height": {"label": "Alto"}, "weight": {"label": "Peso"}, "options": {"label": "Opciones de producto", "hint": "Las opciones se usan para definir el color, tamaño, etc. del producto", "add": "Agregar opción", "optionTitle": "Título de opción", "optionTitlePlaceholder": "Color", "variations": "Variaciones (separadas por comas)", "variantionsPlaceholder": "Rojo, Azul, Verde"}, "variants": {"label": "Variantes de producto", "hint": "Las variantes sin marcar no serán creadas, <PERSON>ste orden afectará cómo se clasifican las variantes en tu frontend."}, "mid_code": {"label": "Código medio"}, "hs_code": {"label": "<PERSON><PERSON><PERSON> a<PERSON>"}}, "variant": {"edit": {"header": "<PERSON><PERSON>", "success": "Variante de producto editada correctamente"}, "create": {"header": "Detalles de la Variante"}, "deleteWarning": "¿Estás seguro de que quieres eliminar esta variante?", "pricesPagination": "1 - {{current}} de {{total}} precios", "tableItemAvailable": "{{availableCount}} disponible", "tableItem_one": "{{availableCount}} disponible en {{locationCount}} ubicación", "tableItem_other": "{{availableCount}} disponible en {{locationCount}} ubicaciones", "inventory": {"notManaged": "No gestionado", "manageItems": "Gestionar ítems de inventario", "notManagedDesc": "El inventario no está gestionado para esta variante. Activa 'Gestionar Inventario' para hacer un seguimiento del inventario de la variante.", "manageKit": "Gestionar kit de inventario", "navigateToItem": "<PERSON>r al ítem de inventario", "actions": {"inventoryItems": "<PERSON>r al ítem de inventario", "inventoryKit": "Mostrar ítems de inventario"}, "inventoryKit": "<PERSON> In<PERSON>", "inventoryKitHint": "¿Esta variante consiste en varios ítems de inventario?", "validation": {"itemId": "Por favor, selecciona un ítem de inventario.", "quantity": "Se requiere la cantidad. Por favor, ingresa un número positivo."}, "header": "Stock & Inventario", "editItemDetails": "Editar detalles del ítem", "manageInventoryLabel": "Gestionar inventario", "manageInventoryHint": "Cuando se habilite, cambiaremos la cantidad de inventario por ti cuando se creen pedidos y devoluciones.", "allowBackordersLabel": "<PERSON><PERSON><PERSON> pedido<PERSON> pendientes", "allowBackordersHint": "<PERSON>uando se habilite, los clientes podrán comprar la variante incluso si no hay cantidad disponible.", "toast": {"levelsBatch": "Niveles de inventario actualizados.", "update": "Ítem de inventario actualizado correctamente.", "updateLevel": "Nivel de inventario actualizado correctamente.", "itemsManageSuccess": "Ítems de inventario actualizados correctamente."}}}, "options": {"header": "Opciones", "edit": {"header": "Editar <PERSON>", "successToast": "Opción {{title}} actualizada correctamente."}, "create": {"header": "Crear <PERSON>", "successToast": "Opción {{title}} creada correctamente."}, "deleteWarning": "Estás a punto de eliminar la opción del producto: {{title}}. Esta acción no puede deshacerse."}, "organization": {"header": "Organizar", "edit": {"header": "Editar Organización", "toasts": {"success": "Organización de {{title}} actualizada correctamente."}}}, "toasts": {"delete": {"success": {"header": "Producto eliminado", "description": "{{title}} eliminado correctamente."}, "error": {"header": "No se pudo eliminar el producto"}}}}, "collections": {"domain": "Colecciones", "subtitle": "Organiza los productos en colecciones.", "createCollection": "<PERSON><PERSON><PERSON>", "createCollectionHint": "Crea una nueva colección para organizar tus productos.", "createSuccess": "Colección creada exitosamente.", "editCollection": "<PERSON><PERSON>", "handleTooltip": "El identificador se usa para referenciar la colección en tu tienda online. Si no se especifica, el identificador se generará a partir del título de la colección.", "deleteWarning": "Estás a punto de eliminar la colección {{title}}. Esta acción no puede deshacerse.", "removeSingleProductWarning": "Estás a punto de eliminar el producto {{title}} de la colección. Esta acción no puede deshacerse.", "removeProductsWarning_one": "Estás a punto de eliminar {{count}} producto de la colección. Esta acción no puede deshacerse.", "removeProductsWarning_other": "Estás a punto de eliminar {{count}} productos de la colección. Esta acción no puede deshacerse.", "products": {"list": {"noRecordsMessage": "No hay productos en la colección."}, "add": {"successToast_one": "Producto agregado exitosamente a la colección.", "successToast_other": "Productos agregados exitosamente a la colección."}, "remove": {"successToast_one": "Producto eliminado exitosamente de la colección.", "successToast_other": "Productos eliminados exitosamente de la colección."}}}, "categories": {"domain": "Categorías", "subtitle": "Organiza productos en categorías, y administra el ranking y jerarquía de esas categorías.", "create": {"header": "Crear Categoría", "hint": "Crea una nueva categoría para organizar tus productos.", "tabs": {"details": "Detalles", "organize": "Organizar Ranking"}, "successToast": "Categoría {{name}} creada correctamente."}, "edit": {"header": "Editar Categoría", "description": "Edita la categoría para actualizar sus detalles.", "successToast": "Categoría actualizada correctamente."}, "delete": {"confirmation": "Estás a punto de eliminar la categoría {{name}}. Esta acción no puede deshacerse.", "successToast": "Categoría {{name}} eliminada correctamente."}, "products": {"add": {"disabledTooltip": "El producto ya está en esta categoría.", "successToast_one": "Se agregó {{count}} producto a la categoría.", "successToast_other": "Se agregaron {{count}} productos a la categoría."}, "remove": {"confirmation_one": "Estás a punto de eliminar {{count}} producto de la categoría. Esta acción no puede deshacerse.", "confirmation_other": "Estás a punto de eliminar {{count}} productos de la categoría. Esta acción no puede deshacerse.", "successToast_one": "Se eliminó {{count}} producto de la categoría.", "successToast_other": "Se eliminaron {{count}} productos de la categoría."}, "list": {"noRecordsMessage": "No hay productos en la categoría."}}, "organize": {"header": "Organizar", "action": "Editar ranking"}, "fields": {"visibility": {"label": "Visibilidad", "internal": "Interna", "public": "Pública"}, "status": {"label": "Estado", "active": "Activa", "inactive": "Inactiva"}, "path": {"label": "<PERSON><PERSON>", "tooltip": "Muestra la ruta completa de la categoría."}, "children": {"label": "<PERSON><PERSON>"}, "new": {"label": "Nueva"}}}, "inventory": {"domain": "Inventario", "subtitle": "Gestiona tus ítems de inventario", "reserved": "Reservado", "available": "Disponible", "locationLevels": "Ubicaciones", "associatedVariants": "<PERSON><PERSON><PERSON> asociadas", "manageLocations": "Gestionar ubicaciones", "deleteWarning": "Estás a punto de eliminar un ítem de inventario. Esta acción no puede deshacerse.", "editItemDetails": "Editar detalles del ítem", "quantityAcrossLocations": "{{quantity}} en {{locations}} ubicaciones", "create": {"title": "<PERSON><PERSON><PERSON> de Inventario", "details": "Detalles", "availability": "Disponibilidad", "locations": "Ubicaciones", "attributes": "Atributos", "requiresShipping": "Requiere envío", "requiresShippingHint": "¿El ítem de inventario requiere envío?", "successToast": "Ítem de inventario creado correctamente."}, "reservation": {"header": "Reserva de {{itemName}}", "editItemDetails": "Editar reserva", "lineItemId": "ID de ítem de línea", "orderID": "ID de pedido", "description": "Descripción", "location": "Ubicación", "inStockAtLocation": "En stock en esta ubicación", "availableAtLocation": "Disponible en esta ubicación", "reservedAtLocation": "Reservado en esta ubicación", "reservedAmount": "Cantidad reservada", "create": "<PERSON><PERSON>r reserva", "itemToReserve": "Ítem a reservar", "quantityPlaceholder": "¿Cuánto deseas reservar?", "descriptionPlaceholder": "¿Qué tipo de reserva es esta?", "successToast": "Reserva creada correctamente.", "updateSuccessToast": "Reserva actualizada correctamente.", "deleteSuccessToast": "Reserva eliminada correctamente.", "errors": {"noAvaliableQuantity": "La ubicación no tiene cantidad disponible.", "quantityOutOfRange": "La cantidad mínima es 1 y la cantidad máxima es {{max}}"}}, "toast": {"updateLocations": "Ubicaciones actualizadas correctamente.", "updateLevel": "Nivel de inventario actualizado correctamente.", "updateItem": "Ítem de inventario actualizado correctamente."}}, "giftCards": {"domain": "Tarjetas de Regalo", "editGiftCard": "Editar Tarjeta de Regalo", "createGiftCard": "Crear Tarjeta de Regalo", "createGiftCardHint": "Crea manualmente una tarjeta de regalo que se puede usar como método de pago en tu tienda.", "selectRegionFirst": "Selecciona primero una región", "deleteGiftCardWarning": "Estás a punto de eliminar la tarjeta de regalo {{code}}. Esta acción no puede deshacerse.", "balanceHigherThanValue": "El saldo no puede ser mayor que la cantidad original.", "balanceLowerThanZero": "El saldo no puede ser negativo.", "expiryDateHint": "Los países tienen diferentes leyes sobre las fechas de vencimiento de las tarjetas de regalo. Asegúrate de verificar las regulaciones locales antes de establecer una fecha de vencimiento.", "regionHint": "Cambiar la región de la tarjeta de regalo también cambiará su moneda, lo que puede afectar su valor monetario.", "enabledHint": "Especifica si la tarjeta de regalo está habilitada o deshabilitada.", "balance": "<PERSON><PERSON>", "currentBalance": "<PERSON><PERSON>", "initialBalance": "<PERSON><PERSON> inicial", "personalMessage": "<PERSON><PERSON>je personal", "recipient": "Destinatario"}, "customers": {"domain": "Clientes", "list": {"noRecordsMessage": "Tus clientes aparecerán aquí."}, "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Crea un nuevo cliente y administra sus detalles.", "successToast": "Cliente {{email}} creado correctamente."}, "groups": {"label": "Grupos de clientes", "remove": "¿Estás seguro de que deseas eliminar al cliente del grupo de clientes \"{{name}}\"?", "removeMany": "¿Estás seguro de que deseas eliminar al cliente de los siguientes grupos de clientes: {{groups}}?", "alreadyAddedTooltip": "El cliente ya está en este grupo de clientes.", "list": {"noRecordsMessage": "Este cliente no pertenece a ningún grupo."}, "add": {"success": "<PERSON><PERSON><PERSON> agregado a: {{groups}}.", "list": {"noRecordsMessage": "Por favor, crea un grupo de clientes primero."}}, "removed": {"success": "Cliente eliminado de: {{groups}}.", "list": {"noRecordsMessage": "Por favor, crea un grupo de clientes primero."}}}, "edit": {"header": "<PERSON><PERSON>", "emailDisabledTooltip": "La dirección de correo electrónico no puede ser cambiada para clientes registrados.", "successToast": "Cliente {{email}} actualizado correctamente."}, "delete": {"title": "Eliminar Cliente", "description": "Estás a punto de eliminar al cliente {{email}}. Esta acción no puede deshacerse.", "successToast": "Cliente {{email}} eliminado correctamente."}, "fields": {"guest": "<PERSON><PERSON><PERSON><PERSON>", "registered": "Registrado", "groups": "Grupos"}, "registered": "Registrado", "guest": "<PERSON><PERSON><PERSON><PERSON>", "hasAccount": "Tiene cuenta"}, "customerGroups": {"domain": "Grupos de Clientes", "subtitle": "Organiza a los clientes en grupos. Los grupos pueden tener promociones y precios diferentes.", "create": {"header": "Crear Grupo de Clientes", "hint": "Crea un nuevo grupo de clientes para segmentar a tus clientes.", "successToast": "Grupo de clientes {{name}} creado correctamente."}, "edit": {"header": "Editar Grupo de Clientes", "successToast": "Grupo de clientes {{name}} actualizado correctamente."}, "delete": {"title": "Eliminar Grupo de Clientes", "description": "Estás a punto de eliminar el grupo de clientes {{name}}. Esta acción no puede deshacerse.", "successToast": "Grupo de clientes {{name}} eliminado correctamente."}, "customers": {"alreadyAddedTooltip": "El cliente ya ha sido agregado al grupo.", "add": {"successToast_one": "Cliente agregado correctamente al grupo.", "successToast_other": "Clientes agregados correctamente al grupo.", "list": {"noRecordsMessage": "Crea un cliente primero."}}, "remove": {"title_one": "Eliminar cliente", "title_other": "Eliminar clientes", "description_one": "Estás a punto de eliminar {{count}} cliente del grupo de clientes. Esta acción no puede deshacerse.", "description_other": "Estás a punto de eliminar {{count}} clientes del grupo de clientes. Esta acción no puede deshacerse."}, "list": {"noRecordsMessage": "Este grupo no tiene clientes."}}}, "orders": {"fields": {"displayId": "ID de visualización", "refundableAmount": "Monto reembolsable", "returnableQuantity": "Cantidad retornable"}, "domain": "Pedidos", "claim": "<PERSON><PERSON><PERSON><PERSON>", "exchange": "Intercambiar", "return": "Devolver", "cancelWarning": "Estás a punto de cancelar el pedido {{id}}. Esta acción no puede deshacerse.", "onDateFromSalesChannel": "{{date}} desde {{salesChannel}}", "list": {"noRecordsMessage": "Tus pedidos aparecerán aquí."}, "summary": {"requestReturn": "Solicitar devolución", "allocateItems": "<PERSON><PERSON><PERSON>", "editOrder": "<PERSON><PERSON> pedido", "editOrderContinue": "Continuar con la edición del pedido", "inventoryKit": "Consiste en {{count}}x artículos de inventario", "itemTotal": "Total del artículo", "shippingTotal": "Total del envío", "discountTotal": "Total del descuento", "taxTotalIncl": "Total de impuestos (incluidos)", "itemSubtotal": "Subtotal del artículo", "shippingSubtotal": "Subtotal del envío", "discountSubtotal": "Subtotal del descuento", "taxTotal": "Total de impuestos"}, "transfer": {"title": "Transferir propiedad", "requestSuccess": "Solicitud de transferencia de pedido enviada a: {{email}}.", "currentOwner": "Propietario actual", "newOwner": "Nuevo propietario", "currentOwnerDescription": "El cliente actualmente relacionado con este pedido.", "newOwnerDescription": "El cliente a quien se transferirá este pedido."}, "payment": {"title": "Pagos", "isReadyToBeCaptured": "El pago <0/> está listo para ser capturado.", "totalPaidByCustomer": "Total pagado por el cliente", "capture": "Capturar pago", "capture_short": "Capturar", "refund": "Reembolso", "markAsPaid": "Marcar como pagado", "statusLabel": "Estado del pago", "statusTitle": "Estado del pago", "status": {"notPaid": "No pagado", "authorized": "Autorizado", "partiallyAuthorized": "Parcialmente autorizado", "awaiting": "<PERSON><PERSON><PERSON><PERSON>", "captured": "<PERSON><PERSON><PERSON>", "partiallyRefunded": "Parcialmente reembolsado", "partiallyCaptured": "Parcialmente capturado", "refunded": "Reembolsado", "canceled": "Cancelado", "requiresAction": "Requiere acción"}, "capturePayment": "Se capturará el pago de {{amount}}.", "capturePaymentSuccess": "Pago de {{amount}} capturado correctamente", "markAsPaidPayment": "El pago de {{amount}} será marcado como pagado.", "markAsPaidPaymentSuccess": "Pago de {{amount}} marcado como pagado correctamente", "createRefund": "<PERSON><PERSON><PERSON> reembolso", "refundPaymentSuccess": "<PERSON><PERSON><PERSON><PERSON> de {{amount}} exitoso", "createRefundWrongQuantity": "La cantidad debe ser un número entre 1 y {{number}}", "refundAmount": "Reembolso {{ amount }}", "paymentLink": "<PERSON><PERSON><PERSON> enlace de pago para {{ amount }}", "selectPaymentToRefund": "Selecciona el pago a reembolsar"}, "edits": {"title": "<PERSON><PERSON> pedido", "confirm": "Confirmar edición", "confirmText": "Estás a punto de confirmar una edición de pedido. Esta acción no puede deshacerse.", "cancel": "Cancelar edición", "currentItems": "Artí<PERSON>los actuales", "currentItemsDescription": "Ajusta la cantidad de artículos o elimínalos.", "addItemsDescription": "Puedes agregar nuevos artículos al pedido.", "addItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "amountPaid": "<PERSON><PERSON> paga<PERSON>", "newTotal": "Nuevo total", "differenceDue": "Diferencia a pagar", "create": "<PERSON><PERSON> pedido", "currentTotal": "Total actual", "noteHint": "Agrega una nota interna para la edición", "cancelSuccessToast": "Edición de pedido cancelada", "createSuccessToast": "Solicitud de edición de pedido creada", "activeChangeError": "Ya existe un cambio de pedido activo (devolución, reclamación, intercambio, etc.). Por favor, termina o cancela el cambio antes de editar el pedido.", "panel": {"title": "Edición de pedido solicitada", "titlePending": "Edición de pedido pendiente"}, "toast": {"canceledSuccessfully": "Edición de pedido cancelada", "confirmedSuccessfully": "Edición de pedido confirmada"}, "validation": {"quantityLowerThanFulfillment": "No se puede establecer la cantidad menor o igual a la cantidad cumplida"}}, "returns": {"create": "Crear devolución", "confirm": "Confirmar devolu<PERSON>", "confirmText": "Estás a punto de confirmar una devolución. Esta acción no puede deshacerse.", "inbound": "<PERSON><PERSON><PERSON>", "outbound": "Saliente", "sendNotification": "Enviar notificación", "sendNotificationHint": "Notificar al cliente sobre la devolución.", "returnTotal": "Total de la devolución", "inboundTotal": "Total entrante", "refundAmount": "Monto a reembolsar", "outstandingAmount": "Monto pendiente", "reason": "Razón", "reasonHint": "Elige la razón por la cual el cliente quiere devolver los artículos.", "note": "<PERSON>a", "noInventoryLevel": "Sin nivel de inventario", "noInventoryLevelDesc": "La ubicación seleccionada no tiene un nivel de inventario para los artículos seleccionados. La devolución puede solicitarse, pero no podrá recibirse hasta que se cree un nivel de inventario para la ubicación seleccionada.", "noteHint": "Puedes escribir libremente si deseas especificar algo.", "location": "Ubicación", "locationHint": "Elige a qué ubicación deseas devolver los artículos.", "inboundShipping": "Envío de devolución", "inboundShippingHint": "Elige el método que deseas utilizar.", "returnableQuantityLabel": "Cantidad retornable", "refundableAmountLabel": "Monto reembolsable", "returnRequestedInfo": "{{requestedItemsCount}}x artículos de devolución solicitados", "returnReceivedInfo": "{{requestedItemsCount}}x artículos de devolución recibidos", "itemReceived": "<PERSON><PERSON><PERSON><PERSON>", "returnRequested": "Devolución solicitada", "damagedItemReceived": "<PERSON><PERSON><PERSON><PERSON> recibid<PERSON>", "damagedItemsReturned": "{{quantity}}x art<PERSON><PERSON>los <PERSON>vueltos", "activeChangeError": "Hay un cambio activo en el pedido. Por favor, termina o descarta el cambio primero.", "cancel": {"title": "Cancelar devolución", "description": "¿Estás seguro de que deseas cancelar la solicitud de devolución?"}, "placeholders": {"noReturnShippingOptions": {"title": "No se encontraron opciones de envío para devoluciones", "hint": "No se crearon opciones de envío para devoluciones para la ubicación. Puedes crear una en <LinkComponent>Ubicación y Envío</LinkComponent>."}, "outboundShippingOptions": {"title": "No se encontraron opciones de envío para salida", "hint": "No se crearon opciones de envío para salida para la ubicación. Puedes crear una en <LinkComponent>Ubicación y Envío</LinkComponent>."}}, "receive": {"action": "Recibir <PERSON>í<PERSON>", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "<PERSON>oner todos los artículos", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON>", "title": "Recibir artí<PERSON> para #{{returnId}}", "sendNotificationHint": "Notificar al cliente sobre la devolución recibida.", "inventoryWarning": "Tenga en cuenta que ajustaremos automáticamente los niveles de inventario según tu entrada anterior.", "writeOffInputLabel": "¿Cuántos de los artículos están dañados?", "toast": {"success": "Devolución recibida correctamente.", "errorLargeValue": "La cantidad es mayor que la cantidad solicitada del artículo.", "errorNegativeValue": "La cantidad no puede ser un valor negativo.", "errorLargeDamagedValue": "La cantidad de artículos dañados + la cantidad de artículos no dañados recibidos supera la cantidad total del artículo en la devolución. Por favor, disminuye la cantidad de artículos no dañados."}}, "toast": {"canceledSuccessfully": "Devolución cancelada correctamente", "confirmedSuccessfully": "Devolución confirmada correctamente"}, "panel": {"title": "Devolución iniciada", "description": "Hay una solicitud de devolución pendiente de completar"}}, "claims": {"create": "<PERSON><PERSON>r Reclamación", "confirm": "Confirmar Reclamación", "confirmText": "Estás a punto de confirmar una reclamación. Esta acción no puede deshacerse.", "manage": "Gestionar Reclamación", "outbound": "Saliente", "outboundItemAdded": "{{itemsCount}}x agregados a través de la reclamación", "outboundTotal": "Total saliente", "outboundShipping": "Envío saliente", "outboundShippingHint": "Elige el método que deseas utilizar.", "refundAmount": "Diferencia estimada", "activeChangeError": "Hay un cambio de pedido activo en este pedido. Por favor, termina o descarta el cambio anterior.", "actions": {"cancelClaim": {"successToast": "Reclamación cancelada correctamente."}}, "cancel": {"title": "Cancelar Reclamación", "description": "¿Estás seguro de que deseas cancelar la reclamación?"}, "tooltips": {"onlyReturnShippingOptions": "Esta lista consistirá solo de opciones de envío para devoluciones."}, "toast": {"canceledSuccessfully": "Reclamación cancelada correctamente", "confirmedSuccessfully": "Reclamación confirmada correctamente"}, "panel": {"title": "Reclamación iniciada", "description": "Hay una solicitud de reclamación pendiente de completar"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON>", "manage": "Gestionar Intercambio", "confirm": "Confirmar <PERSON>", "confirmText": "Estás a punto de confirmar un intercambio. Esta acción no puede deshacerse.", "outbound": "Saliente", "outboundItemAdded": "{{itemsCount}}x agregados a través del intercambio", "outboundTotal": "Total saliente", "outboundShipping": "Envío saliente", "outboundShippingHint": "Elige el método que deseas utilizar.", "refundAmount": "Diferencia estimada", "activeChangeError": "Hay un cambio de pedido activo en este pedido. Por favor, termina o descarta el cambio anterior.", "actions": {"cancelExchange": {"successToast": "Intercambio cancelado correctamente."}}, "cancel": {"title": "Cancelar <PERSON>cambio", "description": "¿Estás seguro de que deseas cancelar el intercambio?"}, "tooltips": {"onlyReturnShippingOptions": "Esta lista consistirá solo de opciones de envío para devoluciones."}, "toast": {"canceledSuccessfully": "Intercambio cancelado correctamente", "confirmedSuccessfully": "Intercambio confirmado correctamente"}, "panel": {"title": "Intercambio iniciado", "description": "Hay una solicitud de intercambio pendiente de completar"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON>", "notAllocatedLabel": "No asignado"}, "allocateItems": {"action": "<PERSON><PERSON><PERSON>", "title": "<PERSON>ignar artí<PERSON> del pedido", "locationDescription": "Elige de qué ubicación deseas asignar.", "itemsToAllocate": "Artí<PERSON>los a <PERSON>ignar", "itemsToAllocateDesc": "Selecciona el número de artículos que deseas asignar", "search": "Buscar artículos", "consistsOf": "Consiste en {{num}}x artículos de inventario", "requires": "Requiere {{num}} por variante", "toast": {"created": "<PERSON><PERSON><PERSON><PERSON> asignado<PERSON> correctamente"}, "error": {"quantityNotAllocated": "Existen artículos no asignados."}}, "shipment": {"title": "Marcar cumplimiento enviado", "trackingNumber": "Número de seguimiento", "addTracking": "Agregar número de seguimiento", "sendNotification": "Enviar notificación", "sendNotificationHint": "Notificar al cliente sobre este envío.", "toastCreated": "Envío creado correctamente."}, "fulfillment": {"cancelWarning": "Estás a punto de cancelar un cumplimiento. Esta acción no puede deshacerse.", "markAsDeliveredWarning": "Estás a punto de marcar el cumplimiento como entregado. Esta acción no puede deshacerse.", "unfulfilledItems": "Artículos no cumplidos", "statusLabel": "Estado del cumplimiento", "statusTitle": "Estado del cumplimiento", "fulfillItems": "<PERSON><PERSON><PERSON><PERSON>", "awaitingFulfillmentBadge": "Esperando cumplimiento", "requiresShipping": "Requiere envío", "number": "Cumplimiento #{{number}}", "itemsToFulfill": "Artí<PERSON>los a cumplir", "create": "<PERSON><PERSON><PERSON>", "available": "Disponible", "inStock": "En stock", "markAsShipped": "Marcar como enviado", "markAsDelivered": "Marcar como entregado", "itemsToFulfillDesc": "Elige los artículos y las cantidades para cumplir", "locationDescription": "Elige de qué ubicación deseas cumplir los artículos.", "sendNotificationHint": "Notificar a los clientes sobre el cumplimiento creado.", "methodDescription": "Elige un método de envío diferente al seleccionado por el cliente", "error": {"wrongQuantity": "Solo hay un artículo disponible para cumplir", "wrongQuantity_other": "La cantidad debe ser un número entre 1 y {{number}}", "noItems": "No hay artículos para cumplir."}, "status": {"notFulfilled": "No cumplido", "partiallyFulfilled": "Parcia<PERSON><PERSON>o", "fulfilled": "<PERSON><PERSON><PERSON><PERSON>", "partiallyShipped": "Parcialmente enviado", "shipped": "Enviado", "delivered": "<PERSON><PERSON><PERSON>", "partiallyDelivered": "<PERSON><PERSON><PERSON><PERSON> en<PERSON>gado", "partiallyReturned": "Parcialmente devue<PERSON>o", "returned": "Devuel<PERSON>", "canceled": "Cancelado", "requiresAction": "Requiere acción"}, "toast": {"created": "Cumplimiento creado correctamente", "canceled": "Cumplimiento cancelado correctamente", "fulfillmentShipped": "No se puede cancelar un cumplimiento ya enviado", "fulfillmentDelivered": "Cumplimiento marcado como entregado correctamente"}, "trackingLabel": "Segu<PERSON><PERSON><PERSON>", "shippingFromLabel": "Enviado desde", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "refund": {"title": "<PERSON><PERSON><PERSON>", "sendNotificationHint": "Notificar a los clientes sobre el reembolso creado.", "systemPayment": "Pago del sistema", "systemPaymentDesc": "Uno o más de tus pagos son pagos del sistema. Ten en cuenta que Medusa no maneja las capturas y reembolsos para dichos pagos.", "error": {"amountToLarge": "No se puede reembolsar más que el monto original del pedido.", "amountNegative": "El monto del reembolso debe ser un número positivo.", "reasonRequired": "Por favor, selecciona una razón para el reembolso."}}, "customer": {"contactLabel": "Contacto", "editEmail": "Editar correo electrónico", "transferOwnership": "Transferir propiedad", "editBillingAddress": "Editar dirección de facturación", "editShippingAddress": "Editar dirección de envío"}, "activity": {"header": "Actividad", "showMoreActivities_one": "Mostrar {{count}} actividad más", "showMoreActivities_other": "Mostrar {{count}} actividades más", "comment": {"label": "Comentario", "placeholder": "Deja un comentario", "addButtonText": "Agregar comentario", "deleteButtonText": "Eliminar comentario"}, "from": "De", "to": "A", "events": {"common": {"toReturn": "Por devolver", "toSend": "Por enviar"}, "placed": {"title": "Pedido realizado", "fromSalesChannel": "desde {{salesChannel}}"}, "canceled": {"title": "Pedido cancelado"}, "payment": {"awaiting": "Esperando pago", "captured": "<PERSON><PERSON> capturado", "canceled": "Pago cancelado", "refunded": "<PERSON><PERSON> reem<PERSON>o"}, "fulfillment": {"created": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "Cumplimiento cancelado", "shipped": "Artículos enviados", "delivered": "<PERSON><PERSON><PERSON><PERSON>", "items_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "return": {"created": "Devolución #{{returnId}} solicitada", "canceled": "Devolución #{{returnId}} cancelada", "received": "Devolución #{{returnId}} recibida", "items_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>"}, "note": {"comment": "Comentario", "byLine": "por {{author}}"}, "claim": {"created": "Reclamación #{{claimId}} solicitada", "canceled": "Reclamación #{{claimId}} cancelada", "itemsInbound": "{{count}} art<PERSON><PERSON><PERSON> para devolver", "itemsOutbound": "{{count}} art<PERSON><PERSON><PERSON> para enviar"}, "exchange": {"created": "Intercambio #{{exchangeId}} solicitado", "canceled": "Intercambio #{{exchangeId}} cancelado", "itemsInbound": "{{count}} art<PERSON><PERSON><PERSON> para devolver", "itemsOutbound": "{{count}} art<PERSON><PERSON><PERSON> para enviar"}, "edit": {"requested": "Edición de pedido #{{editId}} solicitada", "confirmed": "Edición de pedido #{{editId}} confirmada"}, "transfer": {"requested": "Transferencia de pedido #{{transferId}} solicitada", "confirmed": "Transferencia de pedido #{{transferId}} confirmada"}}}}, "draftOrders": {"domain": "Pedidos Borradores", "deleteWarning": "Estás a punto de eliminar el pedido borrador {{id}}. Esta acción no puede deshacerse.", "paymentLinkLabel": "Enlace de pago", "cartIdLabel": "ID del carrito", "markAsPaid": {"label": "Marcar como pagado", "warningTitle": "Marcar como pagado", "warningDescription": "Estás a punto de marcar el pedido borrador como pagado. Esta acción no puede deshacerse y no será posible realizar el cobro del pago más tarde."}, "status": {"open": "<PERSON>bie<PERSON>o", "completed": "Completado"}, "create": {"createDraftOrder": "<PERSON><PERSON><PERSON><PERSON>", "createDraftOrderHint": "Crea un nuevo pedido borrador para gestionar los detalles de un pedido antes de que se realice.", "chooseRegionHint": "<PERSON><PERSON> regi<PERSON>", "existingItemsLabel": "Artículos existentes", "existingItemsHint": "Agrega productos existentes al pedido borrador.", "customItemsLabel": "Artí<PERSON>los personalizados", "customItemsHint": "Agrega artículos personalizados al pedido borrador.", "addExistingItemsAction": "Agregar artí<PERSON>los existentes", "addCustomItemAction": "Agregar art<PERSON><PERSON>lo <PERSON>", "noCustomItemsAddedLabel": "Aún no se han agregado artículos personalizados", "noExistingItemsAddedLabel": "Aún no se han agregado artículos existentes", "chooseRegionTooltip": "Elige una región primero", "useExistingCustomerLabel": "Usar cliente existente", "addShippingMethodsAction": "Agregar métodos de envío", "unitPriceOverrideLabel": "Anulación de precio unitario", "shippingOptionLabel": "Opción de envío", "shippingOptionHint": "Elige la opción de envío para el pedido borrador.", "shippingPriceOverrideLabel": "Anulación del precio de envío", "shippingPriceOverrideHint": "Anula el precio de envío para el pedido borrador.", "sendNotificationLabel": "Enviar notificación", "sendNotificationHint": "Envía una notificación al cliente cuando se cree el pedido borrador."}, "validation": {"requiredEmailOrCustomer": "Se requiere un correo electrónico o un cliente.", "requiredItems": "Se requiere al menos un artículo.", "invalidEmail": "El correo electrónico debe ser una dirección de correo válida."}}, "stockLocations": {"domain": "Ubicaciones & Envío", "list": {"description": "Gestiona las ubicaciones de inventario de tu tienda y las opciones de envío."}, "create": {"header": "Crear Ubicación de Inventario", "hint": "Una ubicación de inventario es un sitio físico donde se almacenan y envían los productos.", "successToast": "Ubicación {{name}} creada correctamente."}, "edit": {"header": "Editar Ubicación de Inventario", "viewInventory": "Ver inventario", "successToast": "Ubicación {{name}} actualizada correctamente."}, "delete": {"confirmation": "Estás a punto de eliminar la ubicación de inventario {{name}}. Esta acción no puede deshacerse."}, "fulfillmentProviders": {"header": "Proveedores de Cumplimiento", "shippingOptionsTooltip": "Este menú desplegable solo consistirá en proveedores habilitados para esta ubicación. Agréguelos a la ubicación si el menú desplegable está deshabilitado.", "label": "Proveedores de cumplimiento conectados", "connectedTo": "Conectado a {{count}} de {{total}} proveedores de cumplimiento", "noProviders": "Esta Ubicación de Inventario no está conectada a ningún proveedor de cumplimiento.", "action": "Conectar Proveedores", "successToast": "Proveedores de cumplimiento para la ubicación de inventario actualizados correctamente."}, "fulfillmentSets": {"pickup": {"header": "Recogida"}, "shipping": {"header": "Envío"}, "disable": {"confirmation": "¿Estás seguro de que deseas deshabilitar {{name}}? Esto eliminará todas las zonas de servicio y opciones de envío asociadas, y no podrá deshacerse.", "pickup": "La recogida se deshabilitó correctamente.", "shipping": "El envío se deshabilitó correctamente."}, "enable": {"pickup": "La recogida se habilitó correctamente.", "shipping": "El envío se habilitó correctamente."}}, "sidebar": {"header": "Configuración de Envío", "shippingProfiles": {"label": "<PERSON><PERSON><PERSON> de Envío", "description": "Agrupa productos por requisitos de envío"}}, "salesChannels": {"header": "Canales de Venta", "label": "Canales de venta conectados", "connectedTo": "Conectado a {{count}} de {{total}} canales de venta", "noChannels": "La ubicación no está conectada a ningún canal de venta.", "action": "Conectar canales de venta", "successToast": "Canales de venta actualizados correctamente."}, "shippingOptions": {"create": {"shipping": {"header": "Crear Opción de Envío para {{zone}}", "hint": "Crea una nueva opción de envío para definir cómo se enviarán los productos desde esta ubicación.", "label": "Opciones de envío", "successToast": "Opción de envío {{name}} creada correctamente."}, "returns": {"header": "Crear Opción de Devolución para {{zone}}", "hint": "Crea una nueva opción de devolución para definir cómo se devolverán los productos a esta ubicación.", "label": "Opciones de devolución", "successToast": "Opción de devolución {{name}} creada correctamente."}, "tabs": {"details": "Detalles", "prices": "<PERSON><PERSON><PERSON>"}, "action": "Crear opción"}, "delete": {"confirmation": "Estás a punto de eliminar la opción de envío {{name}}. Esta acción no puede deshacerse.", "successToast": "Opción de envío {{name}} eliminada correctamente."}, "edit": {"header": "Editar Opción de Envío", "action": "Editar opción", "successToast": "Opción de envío {{name}} actualizada correctamente."}, "pricing": {"action": "<PERSON><PERSON>"}, "fields": {"count": {"shipping_one": "{{count}} opción de envío", "shipping_other": "{{count}} opciones de envío", "returns_one": "{{count}} opción de devolución", "returns_other": "{{count}} opciones de devolución"}, "priceType": {"label": "Tipo de precio", "options": {"fixed": {"label": "<PERSON><PERSON>", "hint": "El precio de la opción de envío es fijo y no cambia según el contenido del pedido."}, "calculated": {"label": "Calculado", "hint": "El precio de la opción de envío es calculado por el proveedor de cumplimiento durante el proceso de pago."}}}, "enableInStore": {"label": "Habilitar en tienda", "hint": "Si los clientes pueden usar esta opción durante el proceso de pago."}, "provider": "<PERSON><PERSON><PERSON><PERSON> de cumplimiento", "profile": "Perfil de envío"}}, "serviceZones": {"create": {"headerPickup": "<PERSON><PERSON>r Zona de Servicio para Recogida desde {{location}}", "headerShipping": "<PERSON><PERSON>r Zona de Servicio para Envío desde {{location}}", "action": "Crear zona de servicio", "successToast": "Zona de servicio {{name}} creada correctamente."}, "edit": {"header": "Editar Zona de Servicio", "successToast": "Zona de servicio {{name}} actualizada correctamente."}, "delete": {"confirmation": "Estás a punto de eliminar la zona de servicio {{name}}. Esta acción no puede deshacerse.", "successToast": "Zona de servicio {{name}} eliminada correctamente."}, "manageAreas": {"header": "Gestionar <PERSON><PERSON> para {{name}}", "action": "Gest<PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "hint": "Selecciona las áreas geográficas que cubre la zona de servicio.", "successToast": "<PERSON><PERSON><PERSON> para {{name}} actualizadas correctamente."}, "fields": {"noRecords": "No hay zonas de servicio para agregar opciones de envío.", "tip": "Una zona de servicio es un conjunto de zonas o áreas geográficas. Se usa para restringir las opciones de envío disponibles a un conjunto definido de ubicaciones."}}}, "shippingProfile": {"domain": "<PERSON><PERSON><PERSON> de Envío", "subtitle": "Agrupa productos con requisitos similares de envío en perfiles.", "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Crea un nuevo perfil de envío para agrupar productos con requisitos similares de envío.", "successToast": "<PERSON><PERSON>l de envío {{name}} creado correctamente."}, "delete": {"title": "Eliminar Perfil de Envío", "description": "Estás a punto de eliminar el perfil de envío {{name}}. Esta acción no puede deshacerse.", "successToast": "Perfil de envío {{name}} eliminado correctamente."}, "tooltip": {"type": "Introduce el tipo de perfil de envío, por ejemplo: Pesado, Sobredimensionado, Solo carga, etc."}}, "taxRegions": {"domain": "Regiones Impositivas", "list": {"hint": "Gestiona lo que cobras a tus clientes cuando compran desde diferentes países y regiones."}, "delete": {"confirmation": "Estás a punto de eliminar una región impositiva. Esta acción no puede deshacerse.", "successToast": "La región impositiva fue eliminada correctamente."}, "create": {"header": "Crear Región Impositiva", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos de un país específico.", "errors": {"rateIsRequired": "La tasa de impuesto es obligatoria al crear una tasa de impuesto predeterminada.", "nameIsRequired": "El nombre es obligatorio al crear una tasa de impuesto predeterminada."}, "successToast": "La región impositiva fue creada correctamente."}, "province": {"header": "Provincias", "create": {"header": "Crear Región Impositiva para Provincia", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para una provincia específica."}}, "state": {"header": "Estados", "create": {"header": "Crear Región Impositiva para Estado", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un estado específico."}}, "stateOrTerritory": {"header": "Estados o Territorios", "create": {"header": "Crear Región Impositiva para Estado/Territorio", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un estado/territorio específico."}}, "county": {"header": "Condados", "create": {"header": "Crear Región Impositiva para Condado", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un condado específico."}}, "region": {"header": "Regiones", "create": {"header": "Crear Región Impositiva para Región", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para una región específica."}}, "department": {"header": "Departamentos", "create": {"header": "Crear Región Impositiva para Departamento", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un departamento específico."}}, "territory": {"header": "Territorios", "create": {"header": "Crear Región Impositiva para Territorio", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un territorio específico."}}, "prefecture": {"header": "Prefecturas", "create": {"header": "Crear Región Impositiva para Prefectura", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para una prefectura específica."}}, "district": {"header": "Distritos", "create": {"header": "Crear Región Impositiva para Distrito", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un distrito específico."}}, "governorate": {"header": "Gobernaturas", "create": {"header": "Crear Región Impositiva para Gobernatura", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para una gobernatura específica."}}, "canton": {"header": "Cantones", "create": {"header": "Crear Región Impositiva para Cantón", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un cantón específico."}}, "emirate": {"header": "Emiratos", "create": {"header": "Crear Región Impositiva para Emirato", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un emirato específico."}}, "sublevel": {"header": "Subniveles", "create": {"header": "Crear Región Impositiva para Subnivel", "hint": "Crea una nueva región impositiva para definir las tasas de impuestos para un subnivel específico."}}, "taxOverrides": {"header": "Anulaciones", "create": {"header": "<PERSON><PERSON><PERSON>", "hint": "Crea una tasa de impuestos que anule las tasas de impuestos predeterminadas para condiciones seleccionadas."}, "edit": {"header": "<PERSON><PERSON>", "hint": "Edita la tasa de impuestos que anula las tasas de impuestos predeterminadas para condiciones seleccionadas."}}, "taxRates": {"create": {"header": "<PERSON><PERSON><PERSON>mp<PERSON>", "hint": "Crea una nueva tasa de impuestos para definir la tasa de impuestos para una región.", "successToast": "Tasa de impuestos creada correctamente."}, "edit": {"header": "Editar Tasa de Impuesto", "hint": "Edita la tasa de impuestos para definir la tasa de impuestos para una región.", "successToast": "Tasa de impuestos actualizada correctamente."}, "delete": {"confirmation": "Estás a punto de eliminar la tasa de impuestos {{name}}. Esta acción no puede deshacerse.", "successToast": "Tasa de impuestos eliminada correctamente."}}, "fields": {"isCombinable": {"label": "Combinable", "hint": "Si esta tasa de impuestos puede combinarse con la tasa predeterminada de la región impositiva.", "true": "Combinable", "false": "No combinable"}, "defaultTaxRate": {"label": "Tasa de impuesto predeterminada", "tooltip": "La tasa de impuestos predeterminada para esta región. Un ejemplo es la tasa estándar de IVA para un país o región.", "action": "<PERSON><PERSON><PERSON> tasa de impuesto predeterminada"}, "taxRate": "Tasa de impuesto", "taxCode": "Código de impuesto", "targets": {"label": "Objetivos", "hint": "Selecciona los objetivos a los que se aplicará esta tasa de impuestos.", "options": {"product": "Productos", "productCollection": "Colecciones de productos", "productTag": "Etiquetas de productos", "productType": "Tipos de productos", "customerGroup": "Grupos de clientes"}, "operators": {"in": "en", "on": "en", "and": "y"}, "placeholders": {"product": "Buscar productos", "productCollection": "Buscar colecciones de productos", "productTag": "Buscar etiquetas de productos", "productType": "Buscar tipos de productos", "customerGroup": "Buscar grupos de clientes"}, "tags": {"product": "Producto", "productCollection": "Colección de producto", "productTag": "Etiqueta de producto", "productType": "Tipo de producto", "customerGroup": "Grupo de clientes"}, "modal": {"header": "Agregar objetivos"}, "values_one": "{{count}} valor", "values_other": "{{count}} valores", "numberOfTargets_one": "{{count}} objetivo", "numberOfTargets_other": "{{count}} objetivos", "additionalValues_one": "y {{count}} valor más", "additionalValues_other": "y {{count}} valores más", "action": "Agregar objetivo"}, "sublevels": {"labels": {"province": "Provincia", "state": "Estado", "region": "Región", "stateOrTerritory": "Estado/Territorio", "department": "Departamento", "county": "Condado", "territory": "Territorio", "prefecture": "Prefectura", "district": "Distrito", "governorate": "Gobernatura", "emirate": "Emirato", "canton": "Cantón", "sublevel": "Código subnivel"}, "placeholders": {"province": "Seleccionar provincia", "state": "Seleccionar estado", "region": "Seleccionar región", "stateOrTerritory": "Seleccionar estado/territorio", "department": "Seleccionar departamento", "county": "Seleccionar condado", "territory": "Seleccionar territorio", "prefecture": "Seleccionar prefectura", "district": "Seleccionar distrito", "governorate": "Seleccionar gober<PERSON>", "emirate": "Seleccionar emirato", "canton": "<PERSON><PERSON><PERSON><PERSON><PERSON> can<PERSON>"}, "tooltips": {"sublevel": "Introduce el código ISO 3166-2 para la región impositiva subnivel.", "notPartOfCountry": "{{province}} parece no ser parte de {{country}}. Por favor, revisa si esto es correcto."}, "alert": {"header": "Las regiones de subnivel están deshabilitadas para esta región impositiva", "description": "Las regiones de subnivel están deshabilitadas para esta región por defecto. Puedes habilitarlas para crear regiones de subnivel como provincias, estados o territorios.", "action": "Habilitar regiones de subnivel"}}, "noDefaultRate": {"label": "Sin tasa predeterminada", "tooltip": "Esta región impositiva no tiene una tasa de impuesto predeterminada. Si existe una tasa estándar, como el IVA de un país, por favor agrégala a esta región."}}}, "promotions": {"domain": "Promociones", "sections": {"details": "Detalles de la Promoción"}, "tabs": {"template": "Tipo", "details": "Detalles", "campaign": "Campaña"}, "fields": {"type": "Tipo", "value_type": "<PERSON><PERSON><PERSON> de valor", "value": "Valor", "campaign": "Campaña", "method": "<PERSON><PERSON><PERSON><PERSON>", "allocation": "Asignación", "addCondition": "Agregar condición", "clearAll": "<PERSON><PERSON><PERSON> todo", "amount": {"tooltip": "Selecciona el código de moneda para habilitar la configuración del monto"}, "conditions": {"rules": {"title": "¿Quién puede usar este código?", "description": "¿Qué cliente puede usar el código promocional? El código promocional puede ser usado por todos los clientes si no se toca."}, "target-rules": {"title": "¿A qué artículos se aplicará la promoción?", "description": "La promoción se aplicará a los artículos que coincidan con las siguientes condiciones."}, "buy-rules": {"title": "¿Qué debe haber en el carrito para desbloquear la promoción?", "description": "Si estas condiciones coinciden, habilitaremos la promoción en los artículos objetivo."}}}, "tooltips": {"campaignType": "El código de moneda debe ser seleccionado en la promoción para establecer un presupuesto de gasto."}, "errors": {"requiredField": "Campo obligatorio", "promotionTabError": "Corrige los errores en la pestaña de Promoción antes de continuar"}, "toasts": {"promotionCreateSuccess": "La promoción ({{code}}) fue creada correctamente."}, "create": {}, "edit": {"title": "Editar Detalles de la Promoción", "rules": {"title": "Editar condiciones de uso"}, "target-rules": {"title": "Editar condiciones de los artículos"}, "buy-rules": {"title": "<PERSON>ar reglas de compra"}}, "campaign": {"header": "Campaña", "edit": {"header": "<PERSON><PERSON>", "successToast": "La campaña de la promoción se actualizó correctamente."}, "actions": {"goToCampaign": "Ir a la campaña"}}, "campaign_currency": {"tooltip": "Esta es la moneda de la promoción. Cámbiala desde la pestaña Detalles."}, "form": {"required": "Obligatorio", "and": "Y", "selectAttribute": "Seleccionar atributo", "campaign": {"existing": {"title": "Campaña existente", "description": "Agregar promoción a una campaña existente.", "placeholder": {"title": "No hay campañas existentes", "desc": "<PERSON><PERSON><PERSON> crear una para hacer un seguimiento de múltiples promociones y establecer límites de presupuesto."}}, "new": {"title": "Nueva Campaña", "description": "Crear una nueva campaña para esta promoción."}, "none": {"title": "Sin Campaña", "description": "Proceder sin asociar la promoción con una campaña"}}, "status": {"title": "Estado"}, "method": {"label": "<PERSON><PERSON><PERSON><PERSON>", "code": {"title": "Código de promoción", "description": "Los clientes deben ingresar este código al realizar el pago"}, "automatic": {"title": "Automático", "description": "Los clientes verán esta promoción al realizar el pago"}}, "max_quantity": {"title": "Cantidad máxima", "description": "Cantidad máxima de artículos a los que se aplica esta promoción."}, "type": {"standard": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Una promoción estándar"}, "buyget": {"title": "Comprar y Obtener", "description": "Promoción de comprar X y obtener Y"}}, "allocation": {"each": {"title": "Cada uno", "description": "Aplica el valor a cada artículo"}, "across": {"title": "A través de", "description": "Aplica el valor a través de los artículos"}}, "code": {"title": "Código", "description": "El código que tus clientes ingresarán durante el proceso de pago."}, "value": {"title": "Valor de la promoción"}, "value_type": {"fixed": {"title": "Valor de la promoción", "description": "La cantidad que se descontará. Ej. 100"}, "percentage": {"title": "Valor de la promoción", "description": "El porcentaje a descontar. Ej. 8%"}}}, "deleteWarning": "Estás a punto de eliminar la promoción {{code}}. Esta acción no puede deshacerse.", "createPromotionTitle": "Crear Promoción", "type": "Tipo de promoción", "conditions": {"add": "Agregar condición", "list": {"noRecordsMessage": "Agrega una condición para restringir los artículos a los que se aplica la promoción."}}}, "campaigns": {"domain": "Campañas", "details": "Detalles de la campaña", "status": {"active": "Activa", "expired": "Expirada", "scheduled": "Programada"}, "delete": {"title": "¿Estás seguro?", "description": "Estás a punto de eliminar la campaña '{{name}}'. Esta acción no puede deshacerse.", "successToast": "La campaña '{{name}}' fue creada correctamente."}, "edit": {"header": "<PERSON><PERSON>", "description": "Editar los detalles de la campaña.", "successToast": "La campaña '{{name}}' fue actualizada correctamente."}, "configuration": {"header": "Configuración", "edit": {"header": "Editar Configuración de la Campaña", "description": "Editar la configuración de la campaña.", "successToast": "La configuración de la campaña se actualizó correctamente."}}, "create": {"title": "<PERSON><PERSON><PERSON>", "description": "Crear una campaña promocional.", "hint": "Crea una campaña promocional.", "header": "<PERSON><PERSON><PERSON>", "successToast": "La campaña '{{name}}' fue creada correctamente."}, "fields": {"name": "Nombre", "identifier": "Identificador", "start_date": "Fecha de inicio", "end_date": "Fecha de finalización", "total_spend": "Presupuesto gastado", "total_used": "Presupuesto utilizado", "budget_limit": "Límite de presupuesto", "campaign_id": {"hint": "Solo se muestran las campañas con el mismo código de moneda que la promoción."}}, "budget": {"create": {"hint": "Crea un presupuesto para la campaña.", "header": "Presupuesto de Campaña"}, "details": "Presupuesto de campaña", "fields": {"type": "Tipo", "currency": "Moneda", "limit": "Límite", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Gasto", "description": "Establece un límite en el monto total descontado de todos los usos de la promoción."}, "usage": {"title": "<PERSON><PERSON>", "description": "Establece un límite en cuántas veces puede ser usada la promoción."}}, "edit": {"header": "Editar Presupuesto de la Campaña"}}, "promotions": {"remove": {"title": "Eliminar promoción de la campaña", "description": "Estás a punto de eliminar {{count}} promoción(es) de la campaña. Esta acción no puede deshacerse."}, "alreadyAdded": "Esta promoción ya ha sido agregada a la campaña.", "alreadyAddedDiffCampaign": "Esta promoción ya ha sido agregada a otra campaña ({{name}}).", "currencyMismatch": "La moneda de la promoción y la campaña no coinciden.", "toast": {"success": "Se agregaron correctamente {{count}} promoción(es) a la campaña."}, "add": {"list": {"noRecordsMessage": "Crea una promoción primero."}}, "list": {"noRecordsMessage": "No hay promociones en la campaña."}}, "deleteCampaignWarning": "Estás a punto de eliminar la campaña {{name}}. Esta acción no puede deshacerse.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Listas de Precios", "subtitle": "<PERSON>rea precios de venta o anula precios para condiciones específicas.", "delete": {"confirmation": "Estás a punto de eliminar la lista de precios {{title}}. Esta acción no puede deshacerse.", "successToast": "La lista de precios {{title}} fue eliminada exitosamente."}, "create": {"header": "<PERSON>rear Lista de Precios", "subheader": "Crea una nueva lista de precios para gestionar los precios de tus productos.", "tabs": {"details": "Detalles", "products": "Productos", "prices": "<PERSON><PERSON><PERSON>"}, "successToast": "La lista de precios {{title}} fue creada exitosamente.", "products": {"list": {"noRecordsMessage": "Crea un producto primero."}}}, "edit": {"header": "Editar Lista de Precios", "successToast": "La lista de precios {{title}} fue actualizada exitosamente."}, "configuration": {"header": "Configuración", "edit": {"header": "Editar Configuración de Lista de Precios", "description": "Edita la configuración de la lista de precios.", "successToast": "La configuración de la lista de precios fue actualizada exitosamente."}}, "products": {"header": "Productos", "actions": {"addProducts": "Agregar productos", "editPrices": "<PERSON><PERSON>"}, "delete": {"confirmation_one": "Estás a punto de eliminar los precios de {{count}} producto en la lista de precios. Esta acción no puede deshacerse.", "confirmation_other": "Estás a punto de eliminar los precios de {{count}} productos en la lista de precios. Esta acción no puede deshacerse.", "successToast_one": "Precios de {{count}} producto eliminados exitosamente.", "successToast_other": "Precios de {{count}} productos eliminados exitosamente."}, "add": {"successToast": "Precios agregados exitosamente a la lista de precios."}, "edit": {"successToast": "Precios actualizados exitosamente."}}, "fields": {"priceOverrides": {"label": "Anulaciones de precios", "header": "Anulaciones de Precios"}, "status": {"label": "Estado", "options": {"active": "Activo", "draft": "<PERSON><PERSON><PERSON>", "expired": "V<PERSON>cid<PERSON>", "scheduled": "Programado"}}, "type": {"label": "Tipo", "hint": "Elige el tipo de lista de precios que deseas crear.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON>", "description": "Los precios de venta son cambios temporales de precios para productos."}, "override": {"label": "Anulación", "description": "Las anulaciones generalmente se usan para crear precios específicos para clientes."}}}, "startsAt": {"label": "¿La lista de precios tiene una fecha de inicio?", "hint": "Programa la lista de precios para que se active en el futuro."}, "endsAt": {"label": "¿La lista de precios tiene una fecha de caducidad?", "hint": "Programa la lista de precios para que se desactive en el futuro."}, "customerAvailability": {"header": "Elige grupos de clientes", "label": "Disponibilidad para clientes", "hint": "Elige qué grupos de clientes deben aplicar la lista de precios.", "placeholder": "Buscar grupos de clientes", "attribute": "Grupos de clientes"}}}, "profile": {"domain": "Perfil", "manageYourProfileDetails": "Gestiona los detalles de tu perfil.", "fields": {"languageLabel": "Idioma", "usageInsightsLabel": "Información de uso"}, "edit": {"header": "<PERSON><PERSON>", "languageHint": "El idioma que deseas usar en el panel de administración. Esto no cambia el idioma de tu tienda.", "languagePlaceholder": "Selecciona idioma", "usageInsightsHint": "Comparte información de uso y ayúdanos a mejorar Medusa. Puedes leer más sobre lo que recopilamos y cómo lo usamos en nuestra <0>documentación</0>."}, "toast": {"edit": "Cambios en el perfil guardados"}}, "users": {"domain": "Usuarios", "editUser": "<PERSON><PERSON>", "inviteUser": "Invitar <PERSON>", "inviteUserHint": "Invita a un nuevo usuario a tu tienda.", "sendInvite": "Enviar invitación", "pendingInvites": "Invitaciones Pendientes", "deleteInviteWarning": "Estás a punto de eliminar la invitación para {{email}}. Esta acción no puede deshacerse.", "resendInvite": "Reenviar invitación", "copyInviteLink": "<PERSON><PERSON>r enlace de invitación", "expiredOnDate": "<PERSON><PERSON><PERSON><PERSON> {{date}}", "validFromUntil": "<PERSON><PERSON><PERSON><PERSON> <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Ace<PERSON><PERSON> el {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON><PERSON>", "pending": "Pendiente", "expired": "V<PERSON>cid<PERSON>"}, "roles": {"admin": "Administrador", "developer": "Desarrollador", "member": "Miembro"}, "deleteUserWarning": "Estás a punto de eliminar al usuario {{name}}. Esta acción no puede deshacerse.", "invite": "Invitar"}, "store": {"domain": "Tienda", "manageYourStoresDetails": "Gestiona los detalles de tu tienda", "editStore": "<PERSON><PERSON> tienda", "defaultCurrency": "Moneda por defecto", "defaultRegion": "Región por defecto", "swapLinkTemplate": "Plantilla de enlace de cambio", "paymentLinkTemplate": "Plantilla de enlace de pago", "inviteLinkTemplate": "Plantilla de enlace de invitación", "currencies": "<PERSON><PERSON><PERSON>", "addCurrencies": "Agregar monedas", "enableTaxInclusivePricing": "Habilitar precios con impuestos incluidos", "disableTaxInclusivePricing": "Deshabilitar precios con impuestos incluidos", "removeCurrencyWarning_one": "Estás a punto de eliminar {{count}} moneda de tu tienda. Asegúrate de haber eliminado todos los precios que usen esta moneda antes de continuar.", "removeCurrencyWarning_other": "Estás a punto de eliminar {{count}} monedas de tu tienda. Asegúrate de haber eliminado todos los precios que usen estas monedas antes de continuar.", "currencyAlreadyAdded": "La moneda ya ha sido agregada a tu tienda.", "edit": {"header": "<PERSON>ar <PERSON>"}, "toast": {"update": "Tienda actualizada exitosamente", "currenciesUpdated": "Monedas actualizadas exitosamente", "currenciesRemoved": "Monedas eliminadas de la tienda exitosamente", "updatedTaxInclusivitySuccessfully": "Precios con impuestos incluidos actualizados exitosamente"}}, "regions": {"domain": "Regiones", "subtitle": "Una región es un área donde vendes productos. Puede cubrir varios países y tiene diferentes tasas impositivas, proveedores y monedas.", "createRegion": "Crear Región", "createRegionHint": "Gestiona tasas impositivas y proveedores para un conjunto de países.", "addCountries": "Agregar países", "editRegion": "Editar Región", "countriesHint": "Agrega los países incluidos en esta región.", "deleteRegionWarning": "Estás a punto de eliminar la región {{name}}. Esta acción no puede deshacerse.", "removeCountriesWarning_one": "Estás a punto de eliminar {{count}} país de la región. Esta acción no puede deshacerse.", "removeCountriesWarning_other": "Estás a punto de eliminar {{count}} países de la región. Esta acción no puede deshacerse.", "removeCountryWarning": "Estás a punto de eliminar el país {{name}} de la región. Esta acción no puede deshacerse.", "automaticTaxesHint": "Cuando está habilitado, los impuestos solo se calcularán en el proceso de pago según la dirección de envío.", "taxInclusiveHint": "Cuando está habilitado, los precios en la región incluirán los impuestos.", "providersHint": "Agrega los proveedores de pago disponibles en esta región.", "shippingOptions": "Opciones de Envío", "deleteShippingOptionWarning": "Estás a punto de eliminar la opción de envío {{name}}. Esta acción no puede deshacerse.", "return": "Devolución", "outbound": "Envío", "priceType": "Tipo de Precio", "flatRate": "Ta<PERSON><PERSON>", "calculated": "Calculado", "list": {"noRecordsMessage": "Crea una región para las áreas donde vendes."}, "toast": {"delete": "Región eliminada exitosamente", "edit": "Edición de región guardada", "create": "Región creada exitosamente", "countries": "Países de la región actualizados exitosamente"}, "shippingOption": {"createShippingOption": "Crear Opción de Envío", "createShippingOptionHint": "Crea una nueva opción de envío para la región.", "editShippingOption": "Editar Opción de Envío", "fulfillmentMethod": "Método de Cumplimiento", "type": {"outbound": "Envío", "outboundHint": "Usa esto si estás creando una opción de envío para enviar productos al cliente.", "return": "Devolución", "returnHint": "Usa esto si estás creando una opción de envío para que el cliente te devuelva productos."}, "priceType": {"label": "Tipo de Precio", "flatRate": "Tarifa fija", "calculated": "Calculado"}, "availability": {"adminOnly": "Solo administrador", "adminOnlyHint": "<PERSON>uando está habilitado, la opción de envío solo estará disponible en el panel de administración, y no en la tienda en línea."}, "taxInclusiveHint": "Cuando está habilitado, el precio de la opción de envío incluirá impuestos.", "requirements": {"label": "Requisitos", "hint": "Especifica los requisitos para la opción de envío."}}}, "taxes": {"domain": "Regiones Fiscales", "domainDescription": "Gestiona tu región fiscal", "countries": {"taxCountriesHint": "La configuración de impuestos se aplica a los países listados."}, "settings": {"editTaxSettings": "Editar configuración de impuestos", "taxProviderLabel": "Prove<PERSON>or de impuestos", "systemTaxProviderLabel": "Proveedor de impuestos del sistema", "calculateTaxesAutomaticallyLabel": "Calcular impuestos automáticamente", "calculateTaxesAutomaticallyHint": "<PERSON>uando está habilitado, las tasas impositivas se calcularán automáticamente y se aplicarán al carrito. Cuando está deshabilitado, los impuestos deben calcularse manualmente en el proceso de pago. Se recomienda usar impuestos manuales con proveedores de impuestos de terceros.", "applyTaxesOnGiftCardsLabel": "Aplicar impuestos en tarjetas de regalo", "applyTaxesOnGiftCardsHint": "Cuando está habilitado, se aplicarán impuestos a las tarjetas de regalo en el proceso de pago. En algunos países, las regulaciones fiscales requieren que se apliquen impuestos a las tarjetas de regalo al momento de la compra.", "defaultTaxRateLabel": "Tasa de impuestos por defecto", "defaultTaxCodeLabel": "Código de impuestos por defecto"}, "defaultRate": {"sectionTitle": "Tasa de impuestos por defecto"}, "taxRate": {"sectionTitle": "Tasas de impuestos", "createTaxRate": "<PERSON><PERSON><PERSON> tasa de impuestos", "createTaxRateHint": "Crea una nueva tasa de impuestos para la región.", "deleteRateDescription": "Estás a punto de eliminar la tasa de impuestos {{name}}. Esta acción no puede deshacerse.", "editTaxRate": "Editar tasa de impuestos", "editRateAction": "<PERSON><PERSON> tasa", "editOverridesAction": "Editar anulaciones", "editOverridesTitle": "Editar anulaciones de tasa de impuestos", "editOverridesHint": "Especifica las anulaciones para la tasa de impuestos.", "deleteTaxRateWarning": "Estás a punto de eliminar la tasa de impuestos {{name}}. Esta acción no puede deshacerse.", "productOverridesLabel": "Anulaciones de producto", "productOverridesHint": "Especifica las anulaciones de producto para la tasa de impuestos.", "addProductOverridesAction": "Agregar anulaciones de producto", "productTypeOverridesLabel": "Anulaciones de tipo de producto", "productTypeOverridesHint": "Especifica las anulaciones de tipo de producto para la tasa de impuestos.", "addProductTypeOverridesAction": "Agregar anulaciones de tipo de producto", "shippingOptionOverridesLabel": "Anulaciones de opción de envío", "shippingOptionOverridesHint": "Especifica las anulaciones de opción de envío para la tasa de impuestos.", "addShippingOptionOverridesAction": "Agregar anulaciones de opción de envío", "productOverridesHeader": "Productos", "productTypeOverridesHeader": "Tipos de producto", "shippingOptionOverridesHeader": "Opciones de envío"}}, "locations": {"domain": "Ubicaciones", "editLocation": "Editar ubicación", "addSalesChannels": "Agregar canales de venta", "noLocationsFound": "No se encontraron ubicaciones", "selectLocations": "Selecciona ubicaciones que tengan el artículo en stock.", "deleteLocationWarning": "Estás a punto de eliminar la ubicación {{name}}. Esta acción no puede deshacerse.", "removeSalesChannelsWarning_one": "Estás a punto de eliminar {{count}} canal de ventas de la ubicación.", "removeSalesChannelsWarning_other": "Estás a punto de eliminar {{count}} canales de ventas de la ubicación.", "toast": {"create": "Ubicación creada exitosamente", "update": "Ubicación actualizada exitosamente", "removeChannel": "Canal de ventas eliminado exitosamente"}}, "reservations": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Gestiona la cantidad reservada de los artículos de inventario.", "deleteWarning": "Estás a punto de eliminar una reserva. Esta acción no puede deshacerse."}, "salesChannels": {"domain": "Canales de Venta", "subtitle": "Gestiona los canales online y offline en los que vendes productos.", "createSalesChannel": "Crear Canal de Venta", "createSalesChannelHint": "Crea un nuevo canal de venta para vender tus productos.", "enabledHint": "Especifica si el canal de venta está habilitado.", "removeProductsWarning_one": "Estás a punto de eliminar {{count}} producto de {{sales_channel}}.", "removeProductsWarning_other": "Estás a punto de eliminar {{count}} productos de {{sales_channel}}.", "addProducts": "Agregar Productos", "editSalesChannel": "Editar canal de venta", "productAlreadyAdded": "El producto ya ha sido agregado al canal de venta.", "deleteSalesChannelWarning": "Estás a punto de eliminar el canal de venta {{name}}. Esta acción no puede deshacerse.", "toast": {"create": "Canal de venta creado exitosamente", "update": "Canal de venta actualizado exitosamente", "delete": "Canal de venta eliminado exitosamente"}, "products": {"list": {"noRecordsMessage": "No hay productos en el canal de venta."}, "add": {"list": {"noRecordsMessage": "Crea un producto primero."}}}}, "apiKeyManagement": {"domain": {"publishable": "Claves API Publicables", "secret": "Claves API Secretas"}, "subtitle": {"publishable": "Gestiona las claves API usadas en la tienda online para limitar el alcance de las solicitudes a canales de venta específicos.", "secret": "Gestiona las claves API usadas para autenticar a los usuarios administradores en las aplicaciones de administración."}, "status": {"active": "Activo", "revoked": "Revocado"}, "type": {"publishable": "Publicable", "secret": "<PERSON>a"}, "create": {"createPublishableHeader": "Crear Clave API Publicable", "createPublishableHint": "Crea una nueva clave API publicable para limitar el alcance de las solicitudes a canales de venta específicos.", "createSecretHeader": "Crear Clave API Secreta", "createSecretHint": "Crea una nueva clave API secreta para acceder a la API de Medusa como usuario administrador autenticado.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON>", "secretKeyCreatedHint": "Tu nueva clave secreta ha sido generada. Cópiala y guárdala de manera segura ahora. Esta es la única vez que se mostrará.", "copySecretTokenSuccess": "Clave secreta copiada al portapapeles.", "copySecretTokenFailure": "No se pudo copiar la clave secreta al portapapeles.", "successToast": "Clave API creada exitosamente."}, "edit": {"header": "Editar Clave API", "description": "Edita el título de la clave API.", "successToast": "Clave API {{title}} actualizada exitosamente."}, "salesChannels": {"title": "Agregar Canales de Venta", "description": "Agrega los canales de venta a los que debe estar limitada la clave API.", "successToast_one": "{{count}} canal de venta fue agregado exitosamente a la clave API.", "successToast_other": "{{count}} canales de venta fueron agregados exitosamente a la clave API.", "alreadyAddedTooltip": "El canal de venta ya ha sido agregado a la clave API.", "list": {"noRecordsMessage": "No hay canales de venta en el alcance de la clave API publicable."}}, "delete": {"warning": "Estás a punto de eliminar la clave API {{title}}. Esta acción no puede deshacerse.", "successToast": "Clave API {{title}} eliminada exitosamente."}, "revoke": {"warning": "Estás a punto de revocar la clave API {{title}}. Esta acción no puede deshacerse.", "successToast": "Clave API {{title}} revocada exitosamente."}, "addSalesChannels": {"list": {"noRecordsMessage": "Crea un canal de venta primero."}}, "removeSalesChannel": {"warning": "Estás a punto de eliminar el canal de venta {{name}} de la clave API. Esta acción no puede deshacerse.", "warningBatch_one": "Estás a punto de eliminar {{count}} canal de venta de la clave API. Esta acción no puede deshacerse.", "warningBatch_other": "Estás a punto de eliminar {{count}} canales de venta de la clave API. Esta acción no puede deshacerse.", "successToast": "Canal de venta eliminado exitosamente de la clave API.", "successToastBatch_one": "{{count}} canal de venta eliminado exitosamente de la clave API.", "successToastBatch_other": "{{count}} canales de venta eliminados exitosamente de la clave API."}, "actions": {"revoke": "Revocar clave API", "copy": "Copiar clave API", "copySuccessToast": "Clave API copiada al portapapeles."}, "table": {"lastUsedAtHeader": "<PERSON><PERSON><PERSON>", "createdAtHeader": "Creado En"}, "fields": {"lastUsedAtLabel": "Último uso", "revokedByLabel": "Revocado por", "revokedAtLabel": "Revocado en", "createdByLabel": "<PERSON><PERSON>o por"}}, "returnReasons": {"domain": "Razones de Devolución", "subtitle": "Gestiona las razones para los artículos devueltos.", "calloutHint": "Gestiona las razones para categorizar las devoluciones.", "editReason": "Editar Razón de Devolución", "create": {"header": "Agregar Razón de Devolución", "subtitle": "Especifica las razones más comunes para las devoluciones.", "hint": "Crea una nueva razón de devolución para categorizar las devoluciones.", "successToast": "Razón de devolución {{label}} creada exitosamente."}, "edit": {"header": "Editar Razón de Devolución", "subtitle": "Edita el valor de la razón de devolución.", "successToast": "Razón de devolución {{label}} actualizada exitosamente."}, "delete": {"confirmation": "Estás a punto de eliminar la razón de devolución {{label}}. Esta acción no puede deshacerse.", "successToast": "Razón de devolución {{label}} eliminada exitosamente."}, "fields": {"value": {"label": "Valor", "placeholder": "wrong_size", "tooltip": "El valor debe ser un identificador único para la razón de devolución."}, "label": {"label": "Etiqueta", "placeholder": "<PERSON><PERSON><PERSON>"}, "description": {"label": "Descripción", "placeholder": "El cliente recibió el tamaño incorrecto"}}}, "login": {"forgotPassword": "¿Olvidaste la contraseña? - <0>Restablecer</0>", "title": "Bienvenido a Medusa", "hint": "Inicia sesión para acceder a la área de cuentas"}, "invite": {"title": "Bienvenido a Medusa", "hint": "Crea tu cuenta a continuación", "backToLogin": "<PERSON>ver al login", "createAccount": "<PERSON><PERSON><PERSON> cuenta", "alreadyHaveAccount": "¿Ya tienes una cuenta? - <0>In<PERSON>ar se<PERSON><PERSON></0>", "emailTooltip": "Tu correo electrónico no puede ser cambiado. Si deseas usar otro correo, debe enviarse una nueva invitación.", "invalidInvite": "La invitación es inválida o ha expirado.", "successTitle": "Tu cuenta ha sido registrada", "successHint": "Comienza a usar Medusa Admin de inmediato.", "successAction": "Iniciar <PERSON><PERSON>", "invalidTokenTitle": "Tu token de invitación es inválido", "invalidTokenHint": "Intenta solicitar un nuevo enlace de invitación.", "passwordMismatch": "Las contraseñas no coinciden", "toast": {"accepted": "Invitación aceptada exitosamente"}}, "resetPassword": {"title": "Restablecer contraseña", "hint": "Ingresa tu correo a continuación, y te enviaremos instrucciones para restablecer tu contraseña.", "email": "Correo electrónico", "sendResetInstructions": "Enviar instrucciones de restablecimiento", "backToLogin": "<0>Volver al login</0>", "newPasswordHint": "Elige una nueva contraseña a continuación.", "invalidTokenTitle": "Tu token de restablecimiento es inválido", "invalidTokenHint": "Intenta solicitar un nuevo enlace de restablecimiento.", "expiredTokenTitle": "Tu token de restablecimiento ha expirado", "goToResetPassword": "<PERSON><PERSON> a <PERSON><PERSON><PERSON>", "resetPassword": "Restablecer contraseña", "newPassword": "Nueva contraseña", "repeatNewPassword": "Repetir nueva contraseña", "tokenExpiresIn": "El token expira en <0>{{time}}</0> minutos", "successfulRequestTitle": "Te enviamos un correo exitosamente", "successfulRequest": "Te hemos enviado un correo que puedes usar para restablecer tu contraseña. Revisa tu carpeta de spam si no lo has recibido después de unos minutos.", "successfulResetTitle": "Restablecimiento de contraseña exitoso", "successfulReset": "Por favor, inicia sesión en la página de login.", "passwordMismatch": "Las contraseñas no coinciden", "invalidLinkTitle": "Tu enlace de restablecimiento es inválido", "invalidLinkHint": "Intenta restablecer tu contraseña nuevamente."}, "workflowExecutions": {"domain": "Flujos de trabajo", "subtitle": "Visualiza y realiza un seguimiento de las ejecuciones de flujos de trabajo en tu aplicación Medusa.", "transactionIdLabel": "ID de Transacción", "workflowIdLabel": "ID de Flujo de Trabajo", "progressLabel": "Progreso", "stepsCompletedLabel_one": "{{completed}} de {{count}} paso", "stepsCompletedLabel_other": "{{completed}} de {{count}} pasos", "list": {"noRecordsMessage": "Aún no se han ejecutado flujos de trabajo."}, "history": {"sectionTitle": "Historial", "runningState": "En ejecución...", "awaitingState": "<PERSON><PERSON><PERSON><PERSON>", "failedState": "Fallido", "skippedState": "Omitido", "skippedFailureState": "Omitido (Error)", "definitionLabel": "Definición", "outputLabel": "Salida", "compensateInputLabel": "Entrada compensada", "revertedLabel": "Revertido", "errorLabel": "Error"}, "state": {"done": "<PERSON><PERSON>", "failed": "Fallido", "reverted": "Revertido", "invoking": "Invocando", "compensating": "Compensando", "notStarted": "No iniciado"}, "transaction": {"state": {"waitingToCompensate": "Esperando para compensar"}}, "step": {"state": {"skipped": "Omitido", "skippedFailure": "Omitido (Error)", "dormant": "Inactivo", "timeout": "<PERSON>ie<PERSON>"}}}, "productTypes": {"domain": "Tipos de Producto", "subtitle": "Organiza tus productos en tipos.", "create": {"header": "<PERSON><PERSON>r Tipo de Producto", "hint": "Crea un nuevo tipo de producto para categorizar tus productos.", "successToast": "Tipo de producto {{value}} creado exitosamente."}, "edit": {"header": "Editar Tipo de Producto", "successToast": "Tipo de producto {{value}} actualizado exitosamente."}, "delete": {"confirmation": "Estás a punto de eliminar el tipo de producto {{value}}. Esta acción no puede deshacerse.", "successToast": "Tipo de producto {{value}} eliminado exitosamente."}, "fields": {"value": "Valor"}}, "productTags": {"domain": "Etiquetas de Producto", "create": {"header": "<PERSON><PERSON><PERSON> Etiqueta de Producto", "subtitle": "Crea una nueva etiqueta de producto para categorizar tus productos.", "successToast": "Etiqueta de producto {{value}} creada exitosamente."}, "edit": {"header": "Editar Etiqueta de Producto", "subtitle": "Edita el valor de la etiqueta de producto.", "successToast": "Etiqueta de producto {{value}} actualizada exitosamente."}, "delete": {"confirmation": "Estás a punto de eliminar la etiqueta de producto {{value}}. Esta acción no puede deshacerse.", "successToast": "Etiqueta de producto {{value}} eliminada exitosamente."}, "fields": {"value": "Valor"}}, "notifications": {"domain": "Notificaciones", "emptyState": {"title": "Sin notificaciones", "description": "No tienes notificaciones por el momento, pero una vez las tengas aparecerán aquí."}, "accessibility": {"description": "Las notificaciones sobre las actividades de Medusa se mostrarán aquí."}}, "errors": {"serverError": "Error de servidor - Intenta nuevamente más tarde.", "invalidCredentials": "Correo electrónico o contraseña incorrectos"}, "statuses": {"scheduled": "Programado", "expired": "V<PERSON>cid<PERSON>", "active": "Activo", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "labels": {"productVariant": "Variante de Producto", "prices": "<PERSON><PERSON><PERSON>", "available": "Disponible", "inStock": "En stock", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "Eliminado"}, "fields": {"amount": "Monto", "refundAmount": "Monto de reembolso", "name": "Nombre", "default": "Predeterminado", "lastName": "Apellido", "firstName": "Nombre", "title": "<PERSON><PERSON><PERSON><PERSON>", "customTitle": "<PERSON><PERSON><PERSON><PERSON>", "manageInventory": "Gestionar inventario", "inventoryKit": "Tiene kit de inventario", "inventoryItems": "Artículos de inventario", "inventoryItem": "Artículo de inventario", "requiredQuantity": "Cantidad requerida", "description": "Descripción", "email": "Correo electrónico", "password": "Contraseña", "repeatPassword": "<PERSON><PERSON>r con<PERSON>", "confirmPassword": "Confirmar con<PERSON>", "newPassword": "Nueva contraseña", "repeatNewPassword": "Repetir nueva contraseña", "categories": "Categorías", "shippingMethod": "M<PERSON><PERSON><PERSON>", "configurations": "Configuraciones", "conditions": "Condiciones", "category": "Categoría", "collection": "Colección", "discountable": "Descontable", "handle": "<PERSON><PERSON><PERSON>", "subtitle": "Subtítulo", "item": "<PERSON><PERSON><PERSON><PERSON>", "qty": "Cant.", "limit": "Límite", "tags": "Etiquetas", "type": "Tipo", "reason": "Razón", "none": "ning<PERSON>", "all": "todos", "search": "Buscar", "percentage": "Po<PERSON>entaj<PERSON>", "sales_channels": "Canales de venta", "customer_groups": "Grupos de clientes", "product_tags": "Etiquetas de productos", "product_types": "Tipos de productos", "product_collections": "Colecciones de productos", "status": "Estado", "code": "Código", "value": "Valor", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamic": "Dinámico", "normal": "Normal", "years": "<PERSON><PERSON><PERSON>", "months": "Meses", "days": "Días", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "totalRedemptions": "Redenciones totales", "countries": "Países", "paymentProviders": "Proveedores de pago", "refundReason": "Razón de reembolso", "fulfillmentProviders": "Proveedores de cumplimiento", "fulfillmentProvider": "<PERSON><PERSON><PERSON><PERSON> de cumplimiento", "providers": "<PERSON>veed<PERSON>", "availability": "Disponibilidad", "inventory": "Inventario", "optional": "Opcional", "note": "<PERSON>a", "automaticTaxes": "Impuestos automáticos", "taxInclusivePricing": "Precios con impuestos incluidos", "currency": "Moneda", "address": "Dirección", "address2": "Apartamento, suite, etc.", "city": "Ciudad", "postalCode": "Código postal", "country": "<PERSON><PERSON>", "state": "Estado", "province": "Provincia", "company": "Compañía", "phone": "Teléfono", "metadata": "Metadatos", "selectCountry": "Seleccionar país", "products": "Productos", "variants": "<PERSON><PERSON><PERSON>", "orders": "Pedidos", "account": "C<PERSON><PERSON>", "total": "Total del pedido", "paidTotal": "Total capturado", "totalExclTax": "Total excl. impuestos", "subtotal": "Subtotal", "shipping": "Envío", "outboundShipping": "Env<PERSON>", "returnShipping": "Envío <PERSON>", "tax": "Impuesto", "created": "<PERSON><PERSON><PERSON>", "key": "Clave", "customer": "Cliente", "date": "<PERSON><PERSON>", "order": "Pedido", "fulfillment": "Cumplimiento", "provider": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Pago", "items": "<PERSON><PERSON><PERSON><PERSON>", "salesChannel": "Canal de venta", "region": "Región", "discount": "Descuento", "role": "Rol", "sent": "Enviado", "salesChannels": "Canales de venta", "product": "Producto", "createdAt": "Creado en", "updatedAt": "Actualizado en", "revokedAt": "Revocado en", "true": "Verdadero", "false": "<PERSON><PERSON><PERSON>", "giftCard": "Tarjeta de regalo", "tag": "Etiqueta", "dateIssued": "<PERSON>cha de emisión", "issuedDate": "<PERSON>cha de emisión", "expiryDate": "<PERSON><PERSON>nc<PERSON>o", "price": "Precio", "priceTemplate": "Precio {{regionOrCurrency}}", "height": "Altura", "width": "<PERSON><PERSON>", "length": "Largo", "weight": "Peso", "midCode": "Código <PERSON>", "hsCode": "Código HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Cantidad de inventario", "barcode": "Código de <PERSON>", "countryOfOrigin": "<PERSON><PERSON> de origen", "material": "Material", "thumbnail": "Miniatura", "sku": "SKU", "managedInventory": "Inventario gestionado", "allowBackorder": "<PERSON><PERSON><PERSON> pedido pendiente", "inStock": "En stock", "location": "Ubicación", "quantity": "Cantidad", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON>", "minSubtotal": "Subtotal mínimo", "maxSubtotal": "Subtotal máximo", "shippingProfile": "Perfil de envío", "summary": "Resumen", "details": "Detalles", "label": "Etiqueta", "rate": "<PERSON><PERSON>", "requiresShipping": "Requiere envío", "unitPrice": "Precio unitario", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "draft": "<PERSON><PERSON><PERSON>", "values": "Valores"}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON><PERSON>", "months_one": "<PERSON><PERSON>", "months_other": "Meses", "weeks_one": "Se<PERSON>", "weeks_other": "Semanas", "days_one": "Día", "days_other": "Días", "hours_one": "<PERSON><PERSON>", "hours_other": "<PERSON><PERSON>", "minutes_one": "Min<PERSON>", "minutes_other": "<PERSON><PERSON><PERSON>", "seconds_one": "<PERSON><PERSON><PERSON>", "seconds_other": "<PERSON><PERSON><PERSON>"}}