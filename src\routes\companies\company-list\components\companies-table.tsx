import { Container, Heading } from "@medusajs/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import { useMemo } from "react"

import { _DataTable } from "../../../../components/table/data-table/data-table"
import { useCompanies } from "../../../../hooks/api/companies"
import { useDataTable } from "../../../../hooks/use-data-table"
import { useCompaniesTableColumns } from "./table/columns"
import { useCompaniesTableFilters } from "./table/filters"
import { useCompaniesTableQuery } from "./table/query"
import { CompanyCreateDrawer } from "./company-create-drawer"

const PAGE_SIZE = 20

export const CompaniesTable = () => {
  const { t } = useTranslation()
  const { searchParams, raw, customerGroupFilter } = useCompaniesTableQuery({
    pageSize: PAGE_SIZE,
  })

  // 从searchParams中提取搜索关键词
  const searchQuery = searchParams.q

  const { companies, count, isError, error, isLoading } = useCompanies(
    {
      // 移除搜索参数，因为我们要在客户端实现搜索
      ...searchParams,
      q: undefined,
    },
    {
      placeholderData: keepPreviousData,
    }
  )

  // 客户端筛选：根据搜索关键词和客户群组筛选公司
  const filteredCompanies = useMemo(() => {
    let result = companies ?? []

    // 根据搜索关键词筛选
    if (searchQuery) {
      result = result.filter((company) => {
        const searchTerm = searchQuery.toLowerCase()
        return (
          company.name?.toLowerCase().includes(searchTerm) ||
          company.email?.toLowerCase().includes(searchTerm) ||
          company.phone?.toLowerCase().includes(searchTerm) ||
          company.address?.toLowerCase().includes(searchTerm)
        )
      })
    }

    // 根据客户群组筛选
    if (customerGroupFilter) {
      result = result.filter((company) => {
        return company.customer_group?.id === customerGroupFilter
      })
    }

    return result
  }, [companies, searchQuery, customerGroupFilter])

  // 计算筛选后的数量
  const filteredCount = useMemo(() => {
    return filteredCompanies.length
  }, [filteredCompanies.length])

  const filters = useCompaniesTableFilters()
  const columns = useCompaniesTableColumns()

  const { table } = useDataTable({
    data: filteredCompanies,
    columns,
    enablePagination: true,
    count: filteredCount,
    pageSize: PAGE_SIZE,
    enableColumnResizing: false,
    columnResizeMode: "onChange",
  })

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{t("companies.title", "Companies")}</Heading>
        <CompanyCreateDrawer />
      </div>
      <style>{`
        [data-table-header-id="avatar"] {
          width: 48px !important;
          min-width: 48px !important;
          max-width: 48px !important;
        }
        [data-table-header-id="avatar"] + th {
          width: auto !important;
        }
      `}</style>
      <_DataTable
        columns={columns}
        table={table}
        navigateTo={(row) => `/companies/${row.original.id}`}
        filters={filters}
        count={filteredCount}
        search
        isLoading={isLoading}
        pageSize={PAGE_SIZE}
        queryObject={raw}
        pagination
        orderBy={[
          { key: "name", label: t("fields.name", "Name") },
          { key: "email", label: t("fields.email", "Email") },
          { key: "phone", label: t("fields.phone", "Phone") },
        ]}
        noRecords={{
          message: t("companies.noCompanies", "No companies found"),
        }}
      />
    </Container>
  )
}