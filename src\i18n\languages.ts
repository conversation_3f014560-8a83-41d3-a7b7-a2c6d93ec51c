import {
  de,
  bg,
  enUS,
  es,
  fr,
  he,
  it,
  ja,
  pl,
  ptBR,
  th,
  tr,
  uk,
  ro,
  mk,
  mn,
  ar,
  zhCN,
  faIR,
  cs,
  ru,
  el,
  lt,
  vi,
  ko,
  nl,
  hu,
  bs,
  id,
} from "date-fns/locale"
import { Language } from "./types"

export const languages: Language[] = [
  {
    code: "bs",
    display_name: "Bosanski",
    ltr: true,
    date_locale: bs,
  },
  {
    code: "bg",
    display_name: "Български",
    ltr: true,
    date_locale: bg,
  },
  {
    code: "en",
    display_name: "English",
    ltr: true,
    date_locale: enUS,
  },
  {
    code: "es",
    display_name: "Español",
    ltr: true,
    date_locale: es,
  },
  {
    code: "el",
    display_name: "Ελληνικά",
    ltr: true,
    date_locale: el,
  },
  {
    code: "de",
    display_name: "<PERSON><PERSON><PERSON>",
    ltr: true,
    date_locale: de,
  },
  {
    code: "fr",
    display_name: "<PERSON>an<PERSON>",
    ltr: true,
    date_locale: fr,
  },
  {
    code: "he",
    display_name: "עברית",
    ltr: false,
    date_locale: he,
  },
  {
    code: "hu",
    display_name: "Magyar",
    ltr: true,
    date_locale: hu,
  },
  {
    code: "it",
    display_name: "Italiano",
    ltr: true,
    date_locale: it,
  },
  {
    code: "ja",
    display_name: "日本語",
    ltr: true,
    date_locale: ja,
  },
  {
    code: "pl",
    display_name: "Polski",
    ltr: true,
    date_locale: pl,
  },
  {
    code: "ptBR",
    display_name: "Português (Brasil)",
    ltr: true,
    date_locale: ptBR,
  },
  {
    code: "tr",
    display_name: "Türkçe",
    ltr: true,
    date_locale: tr,
  },
  {
    code: "th",
    display_name: "ไทย",
    ltr: true,
    date_locale: th,
  },
  {
    code: "uk",
    display_name: "Українська",
    ltr: true,
    date_locale: uk,
  },
  {
    code: "ro",
    display_name: "Română",
    ltr: true,
    date_locale: ro,
  },
  {
    code: "mk",
    display_name: "Македонски",
    ltr: true,
    date_locale: mk,
  },
  {
    code: "mn",
    display_name: "Монгол",
    ltr: true,
    date_locale: mn,
  },
  {
    code: "ar",
    display_name: "العربية",
    ltr: false,
    date_locale: ar,
  },
  {
    code: "zhCN",
    display_name: "简体中文",
    ltr: true,
    date_locale: zhCN,
  },
  {
    code: "fa",
    display_name: "فارسی",
    ltr: false,
    date_locale: faIR,
  },
  {
    code: "cs",
    display_name: "Čeština",
    ltr: true,
    date_locale: cs,
  },
  {
    code: "ru",
    display_name: "Русский",
    ltr: true,
    date_locale: ru,
  },
  {
    code: "lt",
    display_name: "Lietuviškai",
    ltr: true,
    date_locale: lt,
  },
  {
    code: "vi",
    display_name: "Tiếng Việt",
    ltr: true,
    date_locale: vi,
  },
  {
    code: "id",
    display_name: "Bahasa Indonesia",
    ltr: true,
    date_locale: id,
  },
  {
    code: "ko",
    display_name: "한국어",
    ltr: true,
    date_locale: ko,
  },
  {
    code: "nl",
    display_name: "Nederlands",
    ltr: true,
    date_locale: nl,
  },
]
