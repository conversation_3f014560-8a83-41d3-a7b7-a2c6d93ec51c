import { ExclamationCircle } from "@medusajs/icons"
import { <PERSON><PERSON>, Badge, Container, Heading, Table, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { Company, Employee } from "../../../../types"
import { getLocaleAmount } from "../../../../lib/money-amount-helpers"
import { EmployeeCreateDrawer, EmployeesActionsMenu } from "./employees"

// 格式化金额的辅助函数
const formatAmount = (amount: number | null | undefined, currencyCode: string) => {
  if (!amount) return "-"
  return getLocaleAmount(amount, currencyCode)
}

interface CompanyEmployeesSectionProps {
  company: Company
}

export const CompanyEmployeesSection = ({ company }: CompanyEmployeesSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return (
    <Container className="flex flex-col p-0 overflow-hidden">
      <div className="flex items-center gap-2 px-6 py-4 justify-between border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Heading className="font-sans font-medium h1-core">
            员工
          </Heading>
        </div>
        <EmployeeCreateDrawer company={company} />
      </div>
      {company?.employees && company?.employees.length > 0 ? (
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell></Table.HeaderCell>
              <Table.HeaderCell>姓名</Table.HeaderCell>
              <Table.HeaderCell>邮箱</Table.HeaderCell>
              <Table.HeaderCell>消费限额</Table.HeaderCell>
              <Table.HeaderCell>操作</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {company?.employees.map((employee: QueryEmployee) => (
              <Table.Row
                key={employee.id}
                onClick={() => {
                  navigate(`/customers/${employee!.customer!.id}`)
                }}
                className="cursor-pointer"
              >
                <Table.Cell className="w-6 h-6 items-center justify-center">
                  <Avatar
                    fallback={
                      employee.customer?.first_name?.charAt(0) || ""
                    }
                  />
                </Table.Cell>
                <Table.Cell className="flex w-fit gap-2 items-center">
                  {employee.customer?.first_name}{" "}
                  {employee.customer?.last_name}
                  {employee.is_admin && (
                    <Badge
                      size="2xsmall"
                      color={employee.is_admin ? "green" : "grey"}
                    >
                      管理员
                    </Badge>
                  )}
                </Table.Cell>
                <Table.Cell>{employee.customer?.email}</Table.Cell>
                <Table.Cell>
                  {formatAmount(
                    employee.spending_limit,
                    company?.currency_code || "USD"
                  )}
                </Table.Cell>
                <Table.Cell onClick={(e) => e.stopPropagation()}>
                  <EmployeesActionsMenu
                    company={company}
                    employee={employee}
                  />
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="flex h-[400px] w-full flex-col items-center justify-center gap-y-4">
          <div className="flex flex-col items-center gap-y-3">
            <ExclamationCircle />
            <div className="flex flex-col items-center gap-y-1">
              <Text className="font-medium font-sans txt-compact-small">
                暂无员工
              </Text>
              <Text className="txt-small text-ui-fg-muted">
                该公司暂无员工
              </Text>
            </div>
          </div>
        </div>
      )}
    </Container>
  )
}