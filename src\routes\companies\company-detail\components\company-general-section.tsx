import { <PERSON><PERSON>, Badge, Container, Heading, Table, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Company } from "../../../../types"
import { CompanyActionsMenu } from "./company-actions-menu"

interface CompanyGeneralSectionProps {
  company: Company
}

export const CompanyGeneralSection = ({ company }: CompanyGeneralSectionProps) => {
  const { t } = useTranslation()

  return (
    <Container className="flex flex-col p-0 overflow-hidden">
      <div className="flex items-center gap-2 px-6 py-4 border-b border-gray-200 justify-between">
        <div className="flex items-center gap-2">
          <Avatar
            src={company.logo_url || undefined}
            fallback={company.name?.charAt(0)}
          />
          <Heading className="font-sans font-medium h1-core">
            {company.name}
          </Heading>
        </div>
        <CompanyActionsMenu company={company} />
      </div>
      <Table>
        <Table.Body>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small max-w-fit">
              电话
            </Table.Cell>
            <Table.Cell>{company.phone}</Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              邮箱
            </Table.Cell>
            <Table.Cell>{company.email}</Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              地址
            </Table.Cell>
            <Table.Cell>{company.address}</Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              城市
            </Table.Cell>
            <Table.Cell>{company.city}</Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              省份
            </Table.Cell>
            <Table.Cell>{company.state}</Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              货币
            </Table.Cell>
            <Table.Cell>
              {company.currency_code?.toUpperCase()}
            </Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              客户组
            </Table.Cell>
            <Table.Cell>
              {company.customer_group ? (
                <Badge size="small" color="blue">
                  {company.customer_group.name}
                </Badge>
              ) : (
                "-"
              )}
            </Table.Cell>
          </Table.Row>
          <Table.Row>
            <Table.Cell className="font-medium font-sans txt-compact-small">
              审批设置
            </Table.Cell>
            <Table.Cell>
              <div className="flex gap-2">
                {company.approval_settings?.requires_admin_approval && (
                  <Badge size="small" color="purple">
                    需要管理员审批
                  </Badge>
                )}
                {company.approval_settings?.requires_sales_manager_approval && (
                  <Badge size="small" color="purple">
                    需要销售经理审批
                  </Badge>
                )}
                {!company.approval_settings?.requires_admin_approval &&
                  !company.approval_settings?.requires_sales_manager_approval && (
                    <Badge size="small" color="grey">
                      无需审批
                    </Badge>
                  )}
              </div>
            </Table.Cell>
          </Table.Row>
        </Table.Body>
      </Table>
    </Container>
  )
}