import { renderHook } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactNode } from "react"
import { 
  useCompanies, 
  useCompany, 
  useCreateCompany, 
  useUpdateCompany, 
  useDeleteCompany,
  useAddCompanyToCustomerGroup,
  useRemoveCompanyFromCustomerGroup
} from "../companies"

// 创建测试用的QueryClient
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

// 测试包装器
const createWrapper = () => {
  const queryClient = createTestQueryClient()
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe("Companies API Hooks", () => {
  beforeEach(() => {
    // Mock fetch
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  describe("useCompanies", () => {
    it("should create query with correct key", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useCompanies(), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.data).toBe("undefined")
      expect(typeof result.current.isLoading).toBe("boolean")
      expect(typeof result.current.error).toBe("object")
    })

    it("should create query with parameters", () => {
      const wrapper = createWrapper()
      const query = { fields: "*employees,*customer_group" }
      const { result } = renderHook(() => useCompanies(query), { wrapper })
      
      expect(result.current).toBeDefined()
    })
  })

  describe("useCompany", () => {
    it("should create query for single company", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useCompany("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
    })
  })

  describe("useCreateCompany", () => {
    it("should create mutation for creating company", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useCreateCompany(), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
      expect(typeof result.current.mutateAsync).toBe("function")
    })
  })

  describe("useUpdateCompany", () => {
    it("should create mutation for updating company", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useUpdateCompany("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })

  describe("useDeleteCompany", () => {
    it("should create mutation for deleting company", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useDeleteCompany("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })

  describe("useAddCompanyToCustomerGroup", () => {
    it("should create mutation for adding company to customer group", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useAddCompanyToCustomerGroup("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })

  describe("useRemoveCompanyFromCustomerGroup", () => {
    it("should create mutation for removing company from customer group", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useRemoveCompanyFromCustomerGroup("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })
})