import { <PERSON>, LockClosedSolid, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "@medusajs/icons"
import { toast, usePrompt } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { ActionMenu } from "../../../../components/common/action-menu"
import { useDeleteCompany } from "../../../../hooks/api/companies"
import { Company } from "../../../../types"
import { CompanyUpdateDrawer } from "./company-update-drawer"
import { CompanyCustomerGroupDrawer } from "./company-customer-group-drawer"
import { CompanyApprovalSettingsDrawer } from "./company-approval-settings-drawer"

interface CompanyActionsMenuProps {
  company: Company
}

export const CompanyActionsMenu = ({ company }: CompanyActionsMenuProps) => {
  const { t } = useTranslation()
  const prompt = usePrompt()

  const [editOpen, setEditOpen] = useState(false)
  const [customerGroupOpen, setCustomerGroupOpen] = useState(false)
  const [approvalSettingsOpen, setApprovalSettingsOpen] = useState(false)

  const { mutateAsync: deleteCompany, isPending: isDeleting } = useDeleteCompany(company.id, {
    onSuccess: () => {
      toast.success(t("companies.toasts.deleteSuccess", "Successfully deleted company"))
    },
    onError: (error) => {
      toast.error(t("companies.toasts.deleteError", "Failed to delete company"))
    },
  })

  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure", "Are you sure?"),
      description: t("companies.deleteWarning", "This will permanently delete the company and all associated data.", {
        name: company.name,
      }),
      confirmText: t("actions.delete", "Delete"),
      cancelText: t("actions.cancel", "Cancel"),
    })

    if (!res) {
      return
    }

    await deleteCompany()
  }

  return (
    <>
      <ActionMenu
        groups={[
          {
            actions: [
              {
                icon: <PencilSquare />,
                label: t("companies.actions.editDetails", "Edit Details"),
                onClick: () => setEditOpen(true),
              },
              {
                icon: <Link />,
                label: t("companies.actions.manageCustomerGroup", "Manage Customer Group"),
                onClick: () => setCustomerGroupOpen(true),
              },
              {
                icon: <LockClosedSolid />,
                label: t("companies.actions.approvalSettings", "Approval Settings"),
                onClick: () => setApprovalSettingsOpen(true),
              },
            ],
          },
          {
            actions: [
              {
                icon: <Trash />,
                label: t("actions.delete", "Delete"),
                onClick: handleDelete,
              },
            ],
          },
        ]}
      />

      <CompanyUpdateDrawer
        company={company}
        open={editOpen}
        setOpen={setEditOpen}
      />

      <CompanyCustomerGroupDrawer
        company={company}
        open={customerGroupOpen}
        setOpen={setCustomerGroupOpen}
      />

      <CompanyApprovalSettingsDrawer
        company={company}
        open={approvalSettingsOpen}
        setOpen={setApprovalSettingsOpen}
      />
    </>
  )
}
