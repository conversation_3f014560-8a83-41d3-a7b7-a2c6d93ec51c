// 报价相关类型
export type {
  Quote,
  QuoteItem,
  QuoteMessage,
  QuoteFilterParams,
  AdminQuoteResponse,
  AdminQuotesResponse,
  AdminCreateQuoteMessage,
} from "./quotes"

// Store 响应类型
export interface StoreQuotesResponse {
  quotes: Quote[]
  count: number
  offset: number
  limit: number
}

export interface StoreQuoteResponse {
  quote: Quote
}

// 公司相关类型
export type {
  Company,
  Employee,
  ApprovalSettings,
  AdminCompanyResponse,
  AdminCompaniesResponse,
  AdminCreateCompany,
  AdminUpdateCompany,
  AdminCreateEmployee,
  AdminUpdateEmployee,
  AdminEmployeeResponse,
} from "./companies"

// 审批相关类型
export type {
  Approval,
  AdminApproval,
  AdminApprovalsResponse,
  AdminUpdateApproval,
  AdminUpdateApprovalSettings,
  AdminApprovalSettings,
} from "./approvals"