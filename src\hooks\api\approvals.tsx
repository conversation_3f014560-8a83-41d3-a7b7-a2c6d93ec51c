import { FetchError } from "@medusajs/js-sdk"
import {
  QueryKey,
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
} from "@tanstack/react-query"
import { sdk } from "../../lib/client"
import { queryClient } from "../../lib/query-client"
import {
  Approval,
  AdminApprovalsResponse,
  AdminUpdateApproval,
  AdminUpdateApprovalSettings,
  AdminApprovalSettings,
} from "../../types"
import { approvalsQueryKeys, approvalSettingsQueryKeys, companiesQueryKeys } from "./query-keys"

// 基础查询hooks
export const useApprovals = (
  query?: Record<string, any>,
  options?: Omit<
    UseQueryOptions<
      AdminApprovalsResponse,
      FetchError,
      AdminApprovalsResponse,
      QueryKey
    >,
    "queryFn" | "queryKey"
  >
) => {
  const { data, ...rest } = useQuery({
    queryKey: approvalsQueryKeys.list(query),
    queryFn: async () => {
      const response = await sdk.client.fetch<any>(`/admin/approvals`, {
        query,
      })
      // 后端返回的是 carts_with_approvals，需要转换为前端期望的格式
      return {
        approvals: response.carts_with_approvals || [],
        count: response.count || 0,
        offset: response.offset || 0,
        limit: response.limit || 20
      }
    },
    ...options,
  })

  return { approvals: data?.approvals, count: data?.count, ...rest }
}

// 变更操作hooks
export const useUpdateApproval = (
  approvalId: string,
  options?: UseMutationOptions<Approval, FetchError, AdminUpdateApproval>
) => {
  return useMutation({
    mutationFn: async (payload: AdminUpdateApproval) => {
      return sdk.client.fetch<Approval>(`/admin/approvals/${approvalId}`, {
        method: "POST",
        body: payload,
      })
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: approvalsQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: approvalsQueryKeys.detail(approvalId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

export const useUpdateApprovalSettings = (
  companyId: string,
  options?: UseMutationOptions<
    AdminApprovalSettings,
    FetchError,
    AdminUpdateApprovalSettings
  >
) => {
  return useMutation({
    mutationFn: async (payload: AdminUpdateApprovalSettings) => {
      return sdk.client.fetch<AdminApprovalSettings>(
        `/admin/companies/${companyId}/approval-settings`,
        {
          method: "POST",
          body: payload,
        }
      )
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ 
        queryKey: approvalSettingsQueryKeys.detail(companyId) 
      })
      // 同时更新公司详情缓存，因为审批设置是公司的一部分
      queryClient.invalidateQueries({ 
        queryKey: companiesQueryKeys.detail(companyId) 
      })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}// 审批设
export const useApprovalSettings = (
  companyId: string,
  options?: Omit<
    UseQueryOptions<
      AdminApprovalSettings,
      FetchError,
      AdminApprovalSettings,
      QueryKey
    >,
    "queryFn" | "queryKey"
  >
) => {
  const { data, ...rest } = useQuery({
    queryKey: approvalSettingsQueryKeys.detail(companyId),
    queryFn: async () => {
      return sdk.client.fetch<AdminApprovalSettings>(
        `/admin/companies/${companyId}/approval-settings`
      )
    },
    ...options,
  })

  return { approvalSettings: data, ...rest }
}