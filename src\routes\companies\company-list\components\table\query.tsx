import { useQueryParams } from "../../../../../hooks/use-query-params"

type UseCompaniesTableQueryProps = {
  pageSize: number
  prefix?: string
}

export const useCompaniesTableQuery = ({
  pageSize,
  prefix,
}: UseCompaniesTableQueryProps) => {
  const queryObject = useQueryParams(
    ["offset", "q", "order", "created_at", "updated_at", "customer_group"],
    prefix
  )

  const { offset, q, order, created_at, updated_at, customer_group } = queryObject

  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    q,
    fields: "*employees,*customer_group,*approval_settings",
    created_at: created_at ? JSON.parse(created_at) : undefined,
    updated_at: updated_at ? JSON.parse(updated_at) : undefined,
    order: order ? order : "-created_at",
  }

  return {
    searchParams,
    raw: queryObject,
    // 返回客户群组筛选参数供前端使用
    customerGroupFilter: customer_group || undefined,
  }
}
