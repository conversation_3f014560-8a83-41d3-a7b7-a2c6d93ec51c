{"$schema": "./$schema.json", "general": {"ascending": "<PERSON><PERSON>", "descending": "Decrescente", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON>", "end": "Fine", "open": "<PERSON>i", "close": "<PERSON><PERSON>", "apply": "Applica", "range": "Intervallo", "search": "Cerca", "of": "di", "results": "risultati", "pages": "pagine", "next": "Successivo", "prev": "Precedente", "is": "è", "timeline": "Cronologia", "success": "Successo", "warning": "Avviso", "tip": "Suggerimento", "error": "Errore", "select": "Seleziona", "selected": "Selezionato", "enabled": "Abilitato", "disabled": "Disabilitato", "expired": "Scaduto", "active": "Attivo", "revoked": "Revocato", "new": "Nuovo", "modified": "Modificato", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "<PERSON><PERSON><PERSON>", "admin": "Admin", "store": "Negozio", "details": "<PERSON><PERSON><PERSON>", "items_one": "{{count}} elemento", "items_other": "{{count}} elementi", "countSelected": "{{count}} selezionati", "countOfTotalSelected": "{{count}} di {{total}} selezionati", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} di più", "areYouSure": "Sei sicuro?", "noRecordsFound": "Nessun record trovato", "typeToConfirm": "Si prega di digitare {val} per confermare:", "noResultsTitle": "<PERSON><PERSON><PERSON> r<PERSON>", "noResultsMessage": "Prova a cambiare i filtri o la query di ricerca", "noSearchResults": "<PERSON>essun risultato di ricerca", "noSearchResultsFor": "<PERSON><PERSON>un risultato di ricerca per <0>'{{query}}'</0>", "noRecordsTitle": "Nessun record", "noRecordsMessage": "Non ci sono record da mostrare", "unsavedChangesTitle": "Sei sicuro di voler lasciare questo modulo?", "unsavedChangesDescription": "Hai modifiche non salvate che andranno perse se esci da questo modulo.", "includesTaxTooltip": "I prezzi in questa colonna sono comprensivi di tasse.", "excludesTaxTooltip": "I prezzi in questa colonna sono esclusi di tasse.", "noMoreData": "<PERSON><PERSON><PERSON> al<PERSON> dato"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} chiave", "numberOfKeys_other": "{{count}} chiavi", "drawer": {"header_one": "JSON <0>· {{count}} chiave</0>", "header_other": "JSON <0>· {{count}} chiavi</0>", "description": "Visualizza i dati JSON per questo oggetto."}}, "metadata": {"header": "<PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} chiave", "numberOfKeys_other": "{{count}} chiavi", "edit": {"header": "Modifica Metadati", "description": "Modifica i metadati per questo oggetto.", "successToast": "I metadati sono stati aggiornati con successo.", "actions": {"insertRowAbove": "Inserisci riga sopra", "insertRowBelow": "Inser<PERSON>ci riga sotto", "deleteRow": "Elimina riga"}, "labels": {"key": "Chiave", "value": "Valore"}, "complexRow": {"label": "Alcune righe sono disabilitate", "description": "Questo oggetto contiene metadati non primitivi, come array o oggetti, che non possono essere modificati qui. Per modificare le righe disabilitate, utilizza direttamente l'API.", "tooltip": "Questa riga è disabilitata perché contiene dati non primitivi."}}}, "validation": {"mustBeInt": "Il valore deve essere un numero intero.", "mustBePositive": "Il valore deve essere un numero positivo."}, "actions": {"save": "<PERSON><PERSON>", "saveAsDraft": "<PERSON>va come bozza", "copy": "Copia", "copied": "Copiato", "duplicate": "Duplica", "publish": "Pubblica", "create": "<PERSON><PERSON>", "delete": "Elimina", "remove": "<PERSON><PERSON><PERSON><PERSON>", "revoke": "Revoca", "cancel": "<PERSON><PERSON><PERSON>", "forceConfirm": "Conferma forzata", "continueEdit": "Continua modifica", "enable": "Abilita", "disable": "Disabilita", "undo": "<PERSON><PERSON><PERSON>", "complete": "Completa", "viewDetails": "Visualizza <PERSON>", "back": "Indietro", "close": "<PERSON><PERSON>", "showMore": "Mostra di più", "continue": "Continua", "continueWithEmail": "Continua con Email", "idCopiedToClipboard": "ID copiato negli appunti", "addReason": "Aggiungi motivo", "addNote": "Aggiu<PERSON>i nota", "reset": "R<PERSON><PERSON><PERSON>", "confirm": "Conferma", "edit": "Modifica", "addItems": "Aggiungi elementi", "download": "Scarica", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON> tutto", "apply": "Applica", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "Seleziona", "browse": "Sfoglia", "logout": "<PERSON><PERSON><PERSON>", "hide": "Nascondi", "export": "Esporta", "import": "Importa", "cannotUndo": "Questa azione non può essere annullata"}, "operators": {"in": "In"}, "app": {"search": {"label": "Cerca", "title": "Cerca", "description": "Cerca nel tuo intero negozio, inclusi ordini, prodotti, clienti e altro.", "allAreas": "<PERSON><PERSON> le aree", "navigation": "Navigazione", "openResult": "<PERSON><PERSON> ris<PERSON>ato", "showMore": "Mostra di più", "placeholder": "Salta a o trova qualsiasi cosa...", "noResultsTitle": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "noResultsMessage": "Non siamo riusciti a trovare nulla che corrispondesse alla tua ricerca.", "emptySearchTitle": "Digita per cercare", "emptySearchMessage": "Inserisci una parola chiave o una frase per esplorare.", "loadMore": "Carica {{count}} di più", "groups": {"all": "<PERSON><PERSON> le aree", "customer": "Clienti", "customerGroup": "Gruppi di clienti", "product": "<PERSON><PERSON>tti", "productVariant": "<PERSON><PERSON><PERSON> di prodotto", "inventory": "Inventario", "reservation": "Prenotazioni", "category": "Categorie", "collection": "Collezioni", "order": "<PERSON><PERSON><PERSON>", "promotion": "Promozioni", "campaign": "Campagne", "priceList": "Liste dei prezzi", "user": "<PERSON><PERSON><PERSON>", "region": "Regioni", "taxRegion": "Regioni fiscali", "returnReason": "Motivi di reso", "salesChannel": "Canali di vendita", "productType": "Tipi di prodotto", "productTag": "Tag di prodotto", "location": "Posizioni", "shippingProfile": "Profili di spedizione", "publishableApiKey": "Chiavi API pubblicabili", "secretApiKey": "Chiavi <PERSON> segrete", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navigazione"}}, "keyboardShortcuts": {"pageShortcut": "Salta a", "settingShortcut": "Impostazioni", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "poi", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON>", "goToProducts": "<PERSON><PERSON>tti", "goToCollections": "Collezioni", "goToCategories": "Categorie", "goToCustomers": "Clienti", "goToCustomerGroups": "Gruppi di clienti", "goToInventory": "Inventario", "goToReservations": "Prenotazioni", "goToPriceLists": "Liste dei prezzi", "goToPromotions": "Promozioni", "goToCampaigns": "Campagne"}, "settings": {"goToSettings": "Impostazioni", "goToStore": "Negozio", "goToUsers": "<PERSON><PERSON><PERSON>", "goToRegions": "Regioni", "goToTaxRegions": "Regioni fiscali", "goToSalesChannels": "Canali di vendita", "goToProductTypes": "Tipi di prodotto", "goToLocations": "Posizioni", "goToPublishableApiKeys": "Chiavi API pubblicabili", "goToSecretApiKeys": "Chiavi <PERSON> segrete", "goToWorkflows": "Flussi di lavoro", "goToProfile": "<PERSON>ilo", "goToReturnReasons": "Motivi di reso"}}, "menus": {"user": {"documentation": "Documentazione", "changelog": "Registro delle modifiche", "shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profileSettings": "Impostazioni profilo", "theme": {"label": "<PERSON><PERSON>", "dark": "<PERSON><PERSON>", "light": "Chiaro", "system": "Sistema"}}, "store": {"label": "Negozio", "storeSettings": "Impostazioni negozio"}, "actions": {"logout": "<PERSON><PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "Navigazione", "description": "Menu di navigazione per la dashboard."}, "common": {"extensions": "Estensioni"}, "main": {"store": "Negozio", "storeSettings": "Impostazioni negozio"}, "settings": {"header": "Impostazioni", "general": "Generale", "developer": "Sviluppatore", "myAccount": "Il mio account"}}}, "dataGrid": {"columns": {"view": "Visualizza", "resetToDefault": "Ripristina predefiniti", "disabled": "Modificare quali colonne sono visibili è disabilitato."}, "shortcuts": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commands": {"undo": "<PERSON><PERSON><PERSON>", "redo": "R<PERSON><PERSON>", "copy": "Copia", "paste": "<PERSON><PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina", "clear": "<PERSON><PERSON><PERSON><PERSON>", "moveUp": "Sposta su", "moveDown": "Sposta giù", "moveLeft": "Sposta a sinistra", "moveRight": "Sposta a destra", "moveTop": "<PERSON><PERSON><PERSON> in alto", "moveBottom": "<PERSON><PERSON><PERSON> in basso", "selectDown": "Seleziona giù", "selectUp": "Seleziona su", "selectColumnDown": "Seleziona colonna giù", "selectColumnUp": "Seleziona colonna su", "focusToolbar": "Focalizza barra degli strumenti", "focusCancel": "<PERSON><PERSON><PERSON><PERSON> annulla"}}, "errors": {"fixError": "<PERSON><PERSON><PERSON><PERSON> errore", "count_one": "{{count}} errore", "count_other": "{{count}} errori"}}, "filters": {"sortLabel": "Ordina", "filterLabel": "Filtra", "searchLabel": "Cerca", "date": {"today": "<PERSON><PERSON><PERSON>", "lastSevenDays": "Ultimi 7 giorni", "lastThirtyDays": "Ultimi 30 giorni", "lastNinetyDays": "Ultimi 90 giorni", "lastTwelveMonths": "Ultimi 12 mesi", "custom": "<PERSON><PERSON><PERSON><PERSON>", "from": "Da", "to": "A", "starting": "<PERSON><PERSON><PERSON>", "ending": "Fine"}, "compare": {"lessThan": "Meno di", "greaterThan": "Maggiore di", "exact": "Esatto", "range": "Intervallo", "lessThanLabel": "meno di {{value}}", "greaterThanLabel": "maggiore di {{value}}", "andLabel": "e"}, "sorting": {"alphabeticallyAsc": "A - Z", "alphabeticallyDesc": "Z - A", "dateAsc": "<PERSON><PERSON> recente", "dateDesc": "<PERSON>o recente"}, "radio": {"yes": "Sì", "no": "No", "true": "Vero", "false": "<PERSON><PERSON><PERSON>"}, "addFilter": "Aggiungi filtro"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON> non valida", "badRequestMessage": "La richiesta non può essere compresa dal server a causa di una sintassi non valida.", "notFoundTitle": "404 - Non c'è nessuna pagina a questo indirizzo", "notFoundMessage": "Controlla l'URL e riprova, oppure usa la barra di ricerca per trovare ciò che stai cercando.", "internalServerErrorTitle": "500 - Errore interno del server", "internalServerErrorMessage": "Si è verificato un errore imprevisto sul server. Riprova più tardi.", "defaultTitle": "Si è verificato un errore", "defaultMessage": "Si è verificato un errore imprevisto durante il rendering di questa pagina.", "noMatchMessage": "La pagina che stai cercando non esiste.", "backToDashboard": "Torna alla dashboard"}, "addresses": {"title": "<PERSON>diriz<PERSON>", "shippingAddress": {"header": "Indirizzo di Spedizione", "editHeader": "Modifica Indirizzo di Spedizione", "editLabel": "Indirizzo di spedizione", "label": "Indirizzo di spedizione"}, "billingAddress": {"header": "Indirizzo di Fatturazione", "editHeader": "Modifica Indirizzo di Fatturazione", "editLabel": "Indirizzo di fatturazione", "label": "Indirizzo di fatturazione", "sameAsShipping": "Stesso indirizzo di spedizione"}, "contactHeading": "Contat<PERSON>", "locationHeading": "Posizione"}, "email": {"editHeader": "Modifica Email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Trasferisci Proprietà", "label": "Trasferisci proprietà", "details": {"order": "Dettagli ordine", "draft": "<PERSON><PERSON><PERSON> bozza"}, "currentOwner": {"label": "Proprietario attuale", "hint": "L'attuale proprietario dell'ordine."}, "newOwner": {"label": "Nuovo proprietario", "hint": "Il nuovo proprietario a cui trasferire l'ordine."}, "validation": {"mustBeDifferent": "Il nuovo proprietario deve essere diverso dal proprietario attuale.", "required": "Il nuovo proprietario è obbligatorio."}}, "sales_channels": {"availableIn": "Disponibile in <0>{{x}}</0> di <1>{{y}}</1> canali di vendita"}, "products": {"domain": "<PERSON><PERSON>tti", "list": {"noRecordsMessage": "Crea il tuo primo prodotto per iniziare a vendere."}, "edit": {"header": "Modifica Prodotto", "description": "Modifica i dettagli del prodotto.", "successToast": "Il prodotto {{title}} è stato aggiornato con successo."}, "create": {"title": "<PERSON><PERSON>", "description": "Crea un nuovo prodotto.", "header": "Generale", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizza", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Kit di inventario"}, "errors": {"variants": "Seleziona almeno una variante.", "options": "Crea almeno un'opzione.", "uniqueSku": "SKU deve essere unico."}, "inventory": {"heading": "Kit di inventario", "label": "Aggiungi articoli di inventario al kit della variante.", "itemPlaceholder": "Seleziona articolo di inventario", "quantityPlaceholder": "Quanti di questi sono necessari per il kit?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON><PERSON>, questo è un prodotto con varianti", "subHeadingDescription": "Quando deselezionato, creeremo una variante predefinita per te", "optionTitle": {"placeholder": "Taglia"}, "optionValues": {"placeholder": "Piccolo, Medio, Grande"}, "productVariants": {"label": "<PERSON><PERSON><PERSON> di prodotto", "hint": "Questo ordinamento influenzerà l'ordine delle varianti nel tuo negozio.", "alert": "Aggiungi opzioni per creare varianti.", "tip": "Le varianti lasciate deselezionate non verranno create. Puoi sempre creare e modificare varianti in seguito, ma questo elenco si adatta alle variazioni nelle opzioni del tuo prodotto."}, "productOptions": {"label": "Opzioni di prodotto", "hint": "Definisci le opzioni per il prodotto, ad esempio colore, dimensione, ecc."}}, "successToast": "Il prodotto {{title}} è stato creato con successo."}, "export": {"header": "Esporta Elenco Prodotti", "description": "Esporta l'elenco dei prodotti in un file CSV.", "success": {"title": "Stiamo elaborando la tua esportazione", "description": "L'esportazione dei dati potrebbe richiedere alcuni minuti. Ti notificheremo quando saremo pronti."}, "filters": {"title": "<PERSON><PERSON><PERSON>", "description": "Applica filtri nella panoramica della tabella per regolare questa vista"}, "columns": {"title": "Colonne", "description": "Personalizza i dati esportati per soddisfare esigenze specifiche"}}, "import": {"header": "Importa Elenco <PERSON>", "uploadLabel": "<PERSON><PERSON><PERSON>", "uploadHint": "Trascina e rilascia un file CSV o fai clic per caricare", "description": "Importa prodotti fornendo un file CSV in un formato predefinito", "template": {"title": "Non sei sicuro di come disporre la tua lista?", "description": "Scarica il modello qui sotto per assicurarti di seguire il formato corretto."}, "upload": {"title": "Carica un file CSV", "description": "Attraverso le importazioni puoi aggiungere o aggiornare prodotti. Per aggiornare prodotti esistenti devi utilizzare l'handle e l'ID esistenti, per aggiornare varianti esistenti devi utilizzare l'ID esistente. Ti verrà chiesta conferma prima di importare i prodotti.", "preprocessing": "Preelaborazione...", "productsToCreate": "I prodotti saranno creati", "productsToUpdate": "I prodotti saranno aggiornati"}, "success": {"title": "Stiamo elaborando la tua importazione", "description": "L'importazione dei dati potrebbe richiedere un po' di tempo. Ti notificheremo quando saremo pronti."}}, "deleteWarning": "Stai per eliminare il prodotto {{title}}. Questa azione non può essere annullata.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "Nessuna variante", "description": "Non ci sono varianti da visualizzare."}, "filtered": {"heading": "<PERSON><PERSON><PERSON> r<PERSON>", "description": "Nessuna variante corrisponde ai criteri di filtro correnti."}}, "attributes": "Attributi", "editAttributes": "Modifica Attributi", "editOptions": "Modifica Opzioni", "editPrices": "Modifica prezzi", "media": {"label": "Media", "editHint": "Aggiungi media al prodotto per mostrarlo nel tuo negozio.", "makeThumbnail": "Crea miniatura", "uploadImagesLabel": "Carica immagini", "uploadImagesHint": "Trascina e rilascia le immagini qui o fai clic per caricare.", "invalidFileType": "'{{name}}' non è un tipo di file supportato. I tipi di file supportati sono: {{types}}.", "failedToUpload": "Impossibile caricare il media aggiunto. Riprova.", "deleteWarning_one": "Stai per eliminare {{count}} immagine. Questa azione non può essere annullata.", "deleteWarning_other": "Stai per eliminare {{count}} immagini. Questa azione non può essere annullata.", "deleteWarningWithThumbnail_one": "Stai per eliminare {{count}} immagine inclusa la miniatura. Questa azione non può essere annullata.", "deleteWarningWithThumbnail_other": "Stai per eliminare {{count}} immagini incluse le miniature. Questa azione non può essere annullata.", "thumbnailTooltip": "Miniatura", "galleryLabel": "Galleria", "downloadImageLabel": "Scarica l'immagine attuale", "deleteImageLabel": "Elimina l'immagine attuale", "emptyState": {"header": "Nessun media ancora", "description": "Aggiungi media al prodotto per mostrarlo nel tuo negozio.", "action": "Aggiungi media"}, "successToast": "Il media è stato aggiornato con successo."}, "discountableHint": "Quando deselezionato, gli sconti non verranno applicati a questo prodotto.", "noSalesChannels": "Non disponibile in nessun canale di vendita", "variantCount_one": "{{count}} variante", "variantCount_other": "{{count}} varianti", "deleteVariantWarning": "Stai per eliminare la variante {{title}}. Questa azione non può essere annullata.", "productStatus": {"draft": "<PERSON><PERSON>", "published": "Pubblicato", "proposed": "Proposto", "rejected": "Rifiutato"}, "fields": {"title": {"label": "<PERSON><PERSON>", "hint": "Dai al tuo prodotto un titolo breve e chiaro.<0/>50-60 caratteri è la lunghezza consigliata per i motori di ricerca.", "placeholder": "Giacca invernale"}, "subtitle": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "Caldo e accogliente"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "L'handle è usato per fare riferimento al prodotto nel tuo negozio. Se non specificato, l'handle sarà generato dal titolo del prodotto.", "placeholder": "giacca-invernale"}, "description": {"label": "Descrizione", "hint": "Dai al tuo prodotto una descrizione breve e chiara.<0/>120-160 caratteri è la lunghezza consigliata per i motori di ricerca.", "placeholder": "Una giacca calda e accogliente"}, "discountable": {"label": "Scontabile", "hint": "Quando deselezionato, gli sconti non verranno applicati a questo prodotto"}, "type": {"label": "Tipo"}, "collection": {"label": "Collezione"}, "categories": {"label": "Categorie"}, "tags": {"label": "Tag"}, "sales_channels": {"label": "Canali di vendita", "hint": "Questo prodotto sarà disponibile solo nel canale di vendita predefinito se lasciato intatto."}, "countryOrigin": {"label": "Paese di origine"}, "material": {"label": "Materiale"}, "width": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "height": {"label": "Altezza"}, "weight": {"label": "Peso"}, "options": {"label": "Opzioni di prodotto", "hint": "Le opzioni sono usate per definire il colore, la dimensione, ecc. del prodotto", "add": "Aggiungi opzione", "optionTitle": "<PERSON>lo <PERSON>", "optionTitlePlaceholder": "Colore", "variations": "Variazioni (separate da virgola)", "variantionsPlaceholder": "Rosso, Blu, Verde"}, "variants": {"label": "<PERSON><PERSON><PERSON> di prodotto", "hint": "Le varianti lasciate deselezionate non verranno create, questo ordinamento influenzerà come le varianti sono classificate nel tuo frontend."}, "mid_code": {"label": "Codice MID"}, "hs_code": {"label": "Codice HS"}}, "variant": {"edit": {"header": "Modifica Variante", "success": "La variante del prodotto è stata modificata con successo"}, "create": {"header": "<PERSON><PERSON><PERSON>"}, "deleteWarning": "Sei sicuro di voler eliminare questa variante?", "pricesPagination": "1 - {{current}} di {{total}} prezzi", "tableItemAvailable": "{{availableCount}} disponibile", "tableItem_one": "{{availableCount}} disponibile in {{locationCount}} posizione", "tableItem_other": "{{availableCount}} disponibile in {{locationCount}} posizioni", "inventory": {"notManaged": "Non gestito", "manageItems": "Gestisci articoli di inventario", "notManagedDesc": "L'inventario non è gestito per questa variante. Attiva 'Gestisci Inventario' per tenere traccia dell'inventario della variante .", "manageKit": "Gestisci kit di inventario", "navigateToItem": "Vai all'articolo di inventario", "actions": {"inventoryItems": "Vai all'articolo di inventario", "inventoryKit": "Mostra articoli di inventario"}, "inventoryKit": "<PERSON> di Inventario", "inventoryKitHint": "Questa variante consiste in diversi articoli di inventario?", "validation": {"itemId": "Seleziona articolo di inventario.", "quantity": "La quantità è obbligatoria. Si prega di inserire un numero positivo."}, "header": "Stock & Inventario", "editItemDetails": "Modifica dettagli articolo", "manageInventoryLabel": "Gestisci inventario", "manageInventoryHint": "Quando abilitato, cambieremo la quantità di inventario per te quando vengono creati ordini e resi.", "allowBackordersLabel": "Consenti ordini arretrati", "allowBackordersHint": "<PERSON>uando abilita<PERSON>, i clienti possono acquistare la variante anche se non c'è quantità disponibile.", "toast": {"levelsBatch": "Livelli di inventario aggiornati.", "update": "Articolo di inventario aggiornato con successo.", "updateLevel": "Livello di inventario aggiornato con successo.", "itemsManageSuccess": "Articoli di inventario aggiornati con successo."}}}, "options": {"header": "Opzioni", "edit": {"header": "Modifica Opzione", "successToast": "Opzione {{title}} aggiornata con successo."}, "create": {"header": "Crea Opzione", "successToast": "Opzione {{title}} creata con successo."}, "deleteWarning": "Stai per eliminare l'opzione di prodotto: {{title}}. Questa azione non può essere annullata."}, "organization": {"header": "Organizza", "edit": {"header": "Modifica Organizzazione", "toasts": {"success": "Organizzazione di {{title}} aggiornata con successo."}}}, "toasts": {"delete": {"success": {"header": "<PERSON><PERSON><PERSON> eliminato", "description": "{{title}} è stato eliminato con successo."}, "error": {"header": "Impossibile eliminare il prodotto"}}}}, "collections": {"domain": "Collezioni", "subtitle": "Organizza i prodotti in collezioni.", "createCollection": "<PERSON><PERSON>", "createCollectionHint": "Crea una nuova collezione per organizzare i tuoi prodotti.", "createSuccess": "Collezione creata con successo.", "editCollection": "Modifica Collezione", "handleTooltip": "L'handle è usato per fare riferimento alla collezione nel tuo negozio. Se non specificato, l'handle sarà generato dal titolo della collezione.", "deleteWarning": "Stai per eliminare la collezione {{title}}. Questa azione non può essere annullata.", "removeSingleProductWarning": "Stai per rimuovere il prodotto {{title}} dalla collezione. Questa azione non può essere annullata.", "removeProductsWarning_one": "Stai per rimuovere {{count}} prodotto dalla collezione. Questa azione non può essere annullata.", "removeProductsWarning_other": "Stai per rimuovere {{count}} prodotti dalla collezione. Questa azione non può essere annullata.", "products": {"list": {"noRecordsMessage": "Non ci sono prodotti nella collezione."}, "add": {"successToast_one": "<PERSON>dotto aggiunto con successo alla collezione.", "successToast_other": "Prodotti aggiunti con successo alla collezione."}, "remove": {"successToast_one": "<PERSON><PERSON><PERSON> rim<PERSON>o con successo dalla collezione.", "successToast_other": "Prodotti rimossi con successo dalla collezione."}}}, "categories": {"domain": "Categorie", "subtitle": "Organizza i prodotti in categorie e gestisci il ranking e la gerarchia di queste categorie.", "create": {"header": "<PERSON>rea Categoria", "hint": "Crea una nuova categoria per organizzare i tuoi prodotti.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "organize": "Organizza Ranking"}, "successToast": "Categoria {{name}} creata con successo."}, "edit": {"header": "Modifica Categoria", "description": "Modifica la categoria per aggiornare i suoi dettagli.", "successToast": "Categoria aggiornata con successo."}, "delete": {"confirmation": "Stai per eliminare la categoria {{name}}. Questa azione non può essere annullata.", "successToast": "Categoria {{name}} eliminata con successo."}, "products": {"add": {"disabledTooltip": "Il prodotto è già in questa categoria.", "successToast_one": "Aggiunto {{count}} prodotto alla categoria.", "successToast_other": "Aggiunti {{count}} prodotti alla categoria."}, "remove": {"confirmation_one": "Stai per rimuovere {{count}} prodotto dalla categoria. Questa azione non può essere annullata.", "confirmation_other": "Stai per rimuovere {{count}} prodotti dalla categoria. Questa azione non può essere annullata.", "successToast_one": "<PERSON><PERSON><PERSON> {{count}} prodotto dalla categoria.", "successToast_other": "<PERSON><PERSON><PERSON> {{count}} prodotti dalla categoria."}, "list": {"noRecordsMessage": "Non ci sono prodotti nella categoria."}}, "organize": {"header": "Organizza", "action": "Modifica ranking"}, "fields": {"visibility": {"label": "Visibilità", "internal": "Interno", "public": "Pubblico"}, "status": {"label": "Stato", "active": "Attivo", "inactive": "Inattivo"}, "path": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Mostra il percorso completo della categoria."}, "children": {"label": "Bambini"}, "new": {"label": "Nuovo"}}}, "inventory": {"domain": "Inventario", "subtitle": "Gestisci i tuoi articoli di inventario", "reserved": "Riservato", "available": "Disponibile", "locationLevels": "Posizioni", "associatedVariants": "<PERSON><PERSON><PERSON> associate", "manageLocations": "Gestisci posizioni", "deleteWarning": "Stai per eliminare un articolo di inventario. Questa azione non può essere annullata.", "editItemDetails": "Modifica dettagli articolo", "create": {"title": "Crea Articolo di Inventario", "details": "<PERSON><PERSON><PERSON>", "availability": "Disponibilità", "locations": "Posizioni", "attributes": "Attributi", "requiresShipping": "Richiede spedizione", "requiresShippingHint": "L'articolo di inventario richiede spedizione?", "successToast": "Articolo di inventario creato con successo."}, "reservation": {"header": "Prenotazione di {{itemName}}", "editItemDetails": "Modifica prenotazione", "lineItemId": "ID articolo", "orderID": "ID ordine", "description": "Descrizione", "location": "Posizione", "inStockAtLocation": "In stock in questa posizione", "availableAtLocation": "Disponibile in questa posizione", "reservedAtLocation": "Riservato in questa posizione", "reservedAmount": "Quantità riservata", "create": "Crea prenotazione", "itemToReserve": "Articolo da riservare", "quantityPlaceholder": "Quanta vuoi riservare?", "descriptionPlaceholder": "Che tipo di prenotazione è questa?", "successToast": "Prenotazione creata con successo.", "updateSuccessToast": "Prenotazione aggiornata con successo.", "deleteSuccessToast": "Prenotazione eliminata con successo.", "errors": {"noAvaliableQuantity": "La posizione di stock non ha quantità disponibile.", "quantityOutOfRange": "La quantità minima è 1 e la quantità massima è {{max}}"}}, "toast": {"updateLocations": "Posizioni aggiornate con successo.", "updateLevel": "Livello di inventario aggiornato con successo.", "updateItem": "Articolo di inventario aggiornato con successo."}}, "giftCards": {"domain": "Gift Cards", "editGiftCard": "Modifica Gift Card", "createGiftCard": "Crea Gift Card", "createGiftCardHint": "Crea manualmente una gift card che può essere utilizzata come metodo di pagamento nel tuo negozio.", "selectRegionFirst": "Seleziona prima una regione", "deleteGiftCardWarning": "Stai per eliminare la gift card {{code}}. Questa azione non può essere annullata.", "balanceHigherThanValue": "Il saldo non può essere superiore all'importo originale.", "balanceLowerThanZero": "Il saldo non può essere negativo.", "expiryDateHint": "I paesi hanno leggi diverse riguardo le date di scadenza delle gift card. Assicurati di controllare le normative locali prima di impostare una data di scadenza.", "regionHint": "Cambiare la regione della gift card cambierà anche la sua valuta, influenzando potenzialmente il suo valore monetario.", "enabledHint": "Specifica se la gift card è abilitata o disabilitata.", "balance": "<PERSON><PERSON>", "currentBalance": "<PERSON><PERSON> at<PERSON>", "initialBalance": "<PERSON>do <PERSON>", "personalMessage": "Messaggio personale", "recipient": "Destinatario"}, "customers": {"domain": "Clienti", "list": {"noRecordsMessage": "I tuoi clienti appariranno qui."}, "create": {"header": "<PERSON><PERSON>", "hint": "Crea un nuovo cliente e gestisci i suoi dettagli.", "successToast": "Cliente {{email}} creato con successo."}, "groups": {"label": "Gruppi di clienti", "remove": "Sei sicuro di voler rimuovere il cliente dal gruppo \"{{name}}\"?", "removeMany": "Sei sicuro di voler rimuovere il cliente dai seguenti gruppi di clienti: {{groups}}?", "alreadyAddedTooltip": "Il cliente è già in questo gruppo di clienti.", "list": {"noRecordsMessage": "Questo cliente non appartiene a nessun gruppo."}, "add": {"success": "Cliente aggiunto a: {{groups}}.", "list": {"noRecordsMessage": "Crea prima un gruppo di clienti."}}, "removed": {"success": "<PERSON><PERSON><PERSON> rim<PERSON> da: {{groups}}.", "list": {"noRecordsMessage": "Crea prima un gruppo di clienti."}}}, "edit": {"header": "Modifica Cliente", "emailDisabledTooltip": "L'indirizzo email non può essere cambiato per i clienti registrati.", "successToast": "Cliente {{email}} aggiornato con successo."}, "delete": {"title": "Elimina Cliente", "description": "Stai per eliminare il cliente {{email}}. Questa azione non può essere annullata.", "successToast": "Cliente {{email}} eliminato con successo."}, "fields": {"guest": "Ospite", "registered": "Registrato", "groups": "Gruppi"}, "registered": "Registrato", "guest": "Ospite", "hasAccount": "Ha un account"}, "customerGroups": {"domain": "Gruppi di Clienti", "subtitle": "Organizza i clienti in gruppi. I gruppi possono avere promozioni e prezzi diversi.", "create": {"header": "Crea Gruppo di Clienti", "hint": "Crea un nuovo gruppo di clienti per segmentare i tuoi clienti.", "successToast": "Gruppo di clienti {{name}} creato con successo."}, "edit": {"header": "Modifica Gruppo di Clienti", "successToast": "Gruppo di clienti {{name}} aggiornato con successo."}, "delete": {"title": "Elimina Gruppo di Clienti", "description": "Stai per eliminare il gruppo di clienti {{name}}. Questa azione non può essere annullata.", "successToast": "Gruppo di clienti {{name}} eliminato con successo."}, "customers": {"alreadyAddedTooltip": "Il cliente è già stato aggiunto al gruppo.", "add": {"successToast_one": "Cliente aggiunto con successo al gruppo.", "successToast_other": "Clienti aggiunti con successo al gruppo.", "list": {"noRecordsMessage": "Crea prima un cliente."}}, "remove": {"title_one": "Rimuovi cliente", "title_other": "Rimuovi clienti", "description_one": "Stai per rimuovere {{count}} cliente dal gruppo di clienti. Questa azione non può essere annullata.", "description_other": "Stai per rimuovere {{count}} clienti dal gruppo di clienti. Questa azione non può essere annullata."}, "list": {"noRecordsMessage": "Questo gruppo non ha clienti."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON>", "claim": "<PERSON><PERSON>", "exchange": "Scambio", "return": "<PERSON><PERSON>", "cancelWarning": "Stai per annullare l'ordine {{id}}. Questa azione non può essere annullata.", "onDateFromSalesChannel": "{{date}} da {{salesChannel}}", "list": {"noRecordsMessage": "I tuoi ordini appariranno qui."}, "summary": {"requestReturn": "<PERSON><PERSON> re<PERSON>", "allocateItems": "Alloca articoli", "editOrder": "Modifica ordine", "editOrderContinue": "Continua modifica ordine", "inventoryKit": "Consiste di {{count}}x articoli di inventario", "itemTotal": "Totale articoli", "shippingTotal": "Totale spedizione", "discountTotal": "Totale sconto", "taxTotalIncl": "Totale tasse (incluso)", "itemSubtotal": "Subtotale articoli", "shippingSubtotal": "Subtotale spedizione", "discountSubtotal": "Subtotale sconto", "taxTotal": "Totale tasse"}, "transfer": {"title": "Trasferisci proprietà", "requestSuccess": "Richiesta di trasferimento ordine inviata a: {{email}}.", "currentOwner": "Proprietario attuale", "newOwner": "Nuovo proprietario", "currentOwnerDescription": "Il cliente attualmente associato a questo ordine.", "newOwnerDescription": "Il cliente a cui trasferire questo ordine."}, "payment": {"title": "<PERSON><PERSON><PERSON>", "isReadyToBeCaptured": "Pagamento <0/> è pronto per essere catturato.", "totalPaidByCustomer": "Totale pagato dal cliente", "capture": "Cattura pagamento", "capture_short": "Cattura", "refund": "<PERSON><PERSON><PERSON><PERSON>", "markAsPaid": "Segna come pagato", "statusLabel": "Stato pagamento", "statusTitle": "Stato Pagamento", "status": {"notPaid": "Non pagato", "authorized": "Autorizzato", "partiallyAuthorized": "Parzialmente autorizzato", "awaiting": "In attesa", "captured": "Cat<PERSON><PERSON>", "partiallyRefunded": "Parzialmente rimborsato", "partiallyCaptured": "Parzialmente catturato", "refunded": "Rimborsato", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Richiede azione"}, "capturePayment": "Il pagamento di {{amount}} sarà catturato.", "capturePaymentSuccess": "Pagamento di {{amount}} catturato con successo", "markAsPaidPayment": "Il pagamento di {{amount}} sarà segnato come pagato.", "markAsPaidPaymentSuccess": "Pagamento di {{amount}} segnato come pagato con successo", "createRefund": "<PERSON><PERSON>", "refundPaymentSuccess": "Rimborso dell'importo {{amount}} r<PERSON>cito", "createRefundWrongQuantity": "La quantità deve essere un numero compreso tra 1 e {{number}}", "refundAmount": "<PERSON><PERSON><PERSON><PERSON> {{ amount }}", "paymentLink": "Copia link di pagamento per {{ amount }}", "selectPaymentToRefund": "Seleziona pagamento da rimborsare"}, "edits": {"title": "Modifica ordine", "confirm": "Conferma Modifica", "confirmText": "Stai per confermare una Modifica Ordine. Questa azione non può essere annullata.", "cancel": "Annulla Modifica", "currentItems": "Articoli attuali", "currentItemsDescription": "Regola la quantità degli articoli o rimuovi.", "addItemsDescription": "Puoi aggiungere nuovi articoli all'ordine.", "addItems": "Aggiungi articoli", "amountPaid": "Importo pagato", "newTotal": "Nuovo totale", "differenceDue": "<PERSON>ffer<PERSON><PERSON> dovuta", "create": "Modifica Ordine", "currentTotal": "Totale attuale", "noteHint": "Aggiungi una nota interna per la modifica", "cancelSuccessToast": "Modifica ordine annullata", "createSuccessToast": "Richiesta di modifica ordine creata", "activeChangeError": "C'è già un cambiamento attivo sull'ordine (reso, richiesta, scambio, ecc.). Completa o annulla il cambiamento prima di modificare l'ordine.", "panel": {"title": "Modifica ordine richiesta", "titlePending": "Modifica ordine in attesa"}, "toast": {"canceledSuccessfully": "Modifica ordine annullata", "confirmedSuccessfully": "Modifica ordine confermata"}, "validation": {"quantityLowerThanFulfillment": "Non è possibile impostare la quantità a meno di quella già soddisfatta"}}, "returns": {"create": "<PERSON><PERSON>", "confirm": "Conferma Reso", "confirmText": "Stai per confermare un Reso. Questa azione non può essere annullata.", "inbound": "In entrata", "outbound": "In uscita", "sendNotification": "Invia notifica", "sendNotificationHint": "Notifica il cliente riguardo al reso.", "returnTotal": "Totale reso", "inboundTotal": "Totale in entrata", "refundAmount": "Importo <PERSON>", "outstandingAmount": "Importo in sospeso", "reason": "Motivo", "reasonHint": "Scegli perché il cliente vuole restituire gli articoli.", "note": "<PERSON>a", "noInventoryLevel": "<PERSON><PERSON><PERSON>llo di inventario", "noInventoryLevelDesc": "La posizione selezionata non ha un livello di inventario per gli articoli selezionati. Il reso può essere richiesto ma non può essere ricevuto fino a quando non viene creato un livello di inventario per la posizione selezionata.", "noteHint": "Puoi digitare liberamente se vuoi specificare qualcosa.", "location": "Posizione", "locationHint": "Scegli quale posizione vuoi restituire gli articoli.", "inboundShipping": "Spedizione di ritorno", "inboundShippingHint": "<PERSON><PERSON><PERSON> quale metodo vuoi utilizzare.", "returnableQuantityLabel": "Quantità restituibile", "refundableAmountLabel": "Importo <PERSON>", "returnRequestedInfo": "{{requestedItemsCount}}x articoli reso richiesto", "returnReceivedInfo": "{{requestedItemsCount}}x articoli reso ricevuti", "itemReceived": "Articoli ricevuti", "returnRequested": "<PERSON><PERSON> rich<PERSON>o", "damagedItemReceived": "Articoli danneggiati ricevuti", "damagedItemsReturned": "{{quantity}}x articoli danneggiati restituiti", "activeChangeError": "C'è un cambiamento attivo in corso su questo ordine. Completa o scarta il cambiamento prima.", "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Sei sicuro di voler annullare la richiesta di reso?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nessuna opzione di spedizione di ritorno trovata", "hint": "Nessuna opzione di spedizione di ritorno è stata creata per la posizione. Puoi crearne una in <LinkComponent>Posizione & Spedizione</LinkComponent>."}, "outboundShippingOptions": {"title": "Nessuna opzione di spedizione in uscita trovata", "hint": "Nessuna opzione di spedizione in uscita è stata creata per la posizione. Puoi crearne una in <LinkComponent>Posizione & Spedizione</LinkComponent>."}}, "receive": {"action": "<PERSON><PERSON> articoli", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "R<PERSON><PERSON><PERSON> tutti gli articoli", "itemsLabel": "Articoli ricevuti", "title": "Ricevi articoli per #{{returnId}}", "sendNotificationHint": "Notifica il cliente riguardo al reso ricevuto.", "inventoryWarning": "Si prega di notare che regoleremo automaticamente i livelli di inventario in base al tuo input sopra.", "writeOffInputLabel": "Quanti degli articoli sono danneggiati?", "toast": {"success": "<PERSON>so ricevuto con successo.", "errorLargeValue": "La quantità è maggiore della quantità di articoli richiesti.", "errorNegativeValue": "La quantità non può essere un valore negativo.", "errorLargeDamagedValue": "La quantità di articoli danneggiati + la quantità non danneggiata ricevuta supera la quantità totale di articoli sul reso. Si prega di ridurre la quantità di articoli non danneggiati."}}, "toast": {"canceledSuccessfully": "<PERSON>so annullato con successo", "confirmedSuccessfully": "<PERSON>so confermato con successo"}, "panel": {"title": "Reso avviato", "description": "C'è una richiesta di reso aperta da completare"}}, "claims": {"create": "<PERSON><PERSON>", "confirm": "Confer<PERSON>", "confirmText": "Stai per confermare una Richiesta. Questa azione non può essere annullata.", "manage": "<PERSON><PERSON><PERSON><PERSON>", "outbound": "In uscita", "outboundItemAdded": "{{itemsCount}}x aggiunti tramite richiesta", "outboundTotal": "Totale in uscita", "outboundShipping": "Spedizione in uscita", "outboundShippingHint": "<PERSON><PERSON><PERSON> quale metodo vuoi utilizzare.", "refundAmount": "Differenza stimata", "activeChangeError": "C'è un cambiamento attivo sull'ordine. Completa o scarta il cambiamento precedente.", "actions": {"cancelClaim": {"successToast": "<PERSON><PERSON> annullata con successo."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Sei sicuro di voler annullare la richiesta?"}, "tooltips": {"onlyReturnShippingOptions": "Questa lista consisterà solo di opzioni di spedizione di ritorno."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON> annullata con successo", "confirmedSuccessfully": "Richiesta confermata con successo"}, "panel": {"title": "Richiesta avviata", "description": "C'è una richiesta di richiesta aperta da completare"}}, "exchanges": {"create": "<PERSON><PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Conferma Scambio", "confirmText": "Stai per confermare uno Scambio. Questa azione non può essere annullata.", "outbound": "In uscita", "outboundItemAdded": "{{itemsCount}}x aggiunti tramite scambio", "outboundTotal": "Totale in uscita", "outboundShipping": "Spedizione in uscita", "outboundShippingHint": "<PERSON><PERSON><PERSON> quale metodo vuoi utilizzare.", "refundAmount": "Differenza stimata", "activeChangeError": "C'è un cambiamento attivo sull'ordine. Completa o scarta il cambiamento precedente.", "actions": {"cancelExchange": {"successToast": "<PERSON>ambio annullato con successo."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Sei sicuro di voler annullare lo scambio?"}, "tooltips": {"onlyReturnShippingOptions": "Questa lista consisterà solo di opzioni di spedizione di ritorno."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> annullato con <PERSON>o", "confirmedSuccessfully": "Scambio confermato con successo"}, "panel": {"title": "Scambio avviato", "description": "C'è una richiesta di scambio aperta da completare"}}, "reservations": {"allocatedLabel": "Allocato", "notAllocatedLabel": "Non allocato"}, "allocateItems": {"action": "Allocare articoli", "title": "Allocare articoli dell'ordine", "locationDescription": "Scegli quale posizione vuoi allocare.", "itemsToAllocate": "Articoli da allocare", "itemsToAllocateDesc": "Seleziona il numero di articoli che desideri allocare", "search": "Cerca articoli", "consistsOf": "Consiste di {{num}}x articoli di inventario", "requires": "Richiede {{num}} per variante", "toast": {"created": "Articoli allocati con successo"}, "error": {"quantityNotAllocated": "Ci sono articoli non allocati."}}, "shipment": {"title": "Segna adempimento spedito", "trackingNumber": "Numero di tracciamento", "addTracking": "Aggiungi numero di tracciamento", "sendNotification": "Invia notifica", "sendNotificationHint": "Notifica il cliente riguardo a questa spedizione.", "toastCreated": "Spedizione creata con successo."}, "fulfillment": {"cancelWarning": "Stai per annullare un adempimento. Questa azione non può essere annullata.", "markAsDeliveredWarning": "Stai per segnare l'adempimento come consegnato. Questa azione non può essere annullata.", "unfulfilledItems": "Articoli non adempiuti", "statusLabel": "Stato adempimento", "statusTitle": "Stato Adempimento", "fulfillItems": "<PERSON><PERSON><PERSON> articoli", "awaitingFulfillmentBadge": "In attesa di adempimento", "requiresShipping": "Richiede spedizione", "number": "Adempimento #{{number}}", "itemsToFulfill": "Articoli da adempiere", "create": "<PERSON><PERSON>", "available": "Disponibile", "inStock": "In stock", "markAsShipped": "<PERSON><PERSON> come spedito", "markAsDelivered": "<PERSON>gna come consegnato", "itemsToFulfillDesc": "Scegli articoli e quantità da adempiere", "locationDescription": "Scegli quale posizione vuoi adempiere articoli da.", "sendNotificationHint": "Notifica i clienti riguardo all'adempimento creato.", "methodDescription": "Scegli un metodo di spedizione diverso da quello selezionato dal cliente", "error": {"wrongQuantity": "Solo un articolo è disponibile per l'adempimento", "wrongQuantity_other": "La quantità deve essere un numero compreso tra 1 e {{number}}", "noItems": "<PERSON><PERSON><PERSON> articolo da adempiere."}, "status": {"notFulfilled": "Non adempiuto", "partiallyFulfilled": "Parzialmente adempiuto", "fulfilled": "<PERSON><PERSON><PERSON><PERSON>", "partiallyShipped": "Parzialmente spedito", "shipped": "Spedito", "delivered": "<PERSON>segna<PERSON>", "partiallyDelivered": "Parzialmente consegnato", "partiallyReturned": "Parzialmente restituito", "returned": "Restituito", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Richiede azione"}, "toast": {"created": "Adempimento creato con successo", "canceled": "Adempimento annullato con successo", "fulfillmentShipped": "Impossibile annullare un adempimento già spedito", "fulfillmentDelivered": "<PERSON><PERSON><PERSON><PERSON> segnato come consegnato con successo"}, "trackingLabel": "Tracciamento", "shippingFromLabel": "Spedizione da", "itemsLabel": "Articoli"}, "refund": {"title": "<PERSON><PERSON>", "sendNotificationHint": "Notifica i clienti riguardo al rimborso creato.", "systemPayment": "Pagamento di sistema", "systemPaymentDesc": "Uno o più dei tuoi pagamenti è un pagamento di sistema. Tieni presente che le catt ure e i rimborsi non sono gestiti da Medusa per tali pagamenti.", "error": {"amountToLarge": "Non è possibile rimborsare più dell'importo originale dell'ordine.", "amountNegative": "L'importo del rimborso deve essere un numero positivo.", "reasonRequired": "Seleziona un motivo per il rimborso."}}, "customer": {"contactLabel": "Contat<PERSON>", "editEmail": "Modifica email", "transferOwnership": "Trasferisci proprietà", "editBillingAddress": "Modifica indirizzo di fatturazione", "editShippingAddress": "Modifica indirizzo di spedizione"}, "activity": {"header": "Attività", "showMoreActivities_one": "Mostra {{count}} attività in più", "showMoreActivities_other": "Mostra {{count}} attività in più", "comment": {"label": "Commento", "placeholder": "Lascia un commento", "addButtonText": "Aggiungi commento", "deleteButtonText": "Elimina commento"}, "from": "Da", "to": "A", "events": {"common": {"toReturn": "Da restituire", "toSend": "Da inviare"}, "placed": {"title": "Ordine effettuato", "fromSalesChannel": "da {{salesChannel}}"}, "canceled": {"title": "<PERSON><PERSON> annullato"}, "payment": {"awaiting": "In attesa di pagamento", "captured": "Pagamento catturato", "canceled": "<PERSON><PERSON><PERSON> annullato", "refunded": "Pagamento rimborsato"}, "fulfillment": {"created": "Articoli adempiuti", "canceled": "Adempimento annullato", "shipped": "Articoli spediti", "delivered": "Articoli consegnati", "items_one": "{{count}} articolo", "items_other": "{{count}} articoli"}, "return": {"created": "Reso #{{returnId}} richiesto", "canceled": "Reso #{{returnId}} annullato", "received": "Reso #{{returnId}} ricevuto", "items_one": "{{count}} articolo restituito", "items_other": "{{count}} articoli restituiti"}, "note": {"comment": "Commento", "byLine": "da {{author}}"}, "claim": {"created": "Richiesta #{{claimId}} richiesta", "canceled": "Richie<PERSON> #{{claimId}} annullata", "itemsInbound": "{{count}} art<PERSON><PERSON> da restituire", "itemsOutbound": "{{count}} articolo da inviare"}, "exchange": {"created": "Scambio #{{exchangeId}} richiesto", "canceled": "Scambio #{{exchangeId}} annullato", "itemsInbound": "{{count}} art<PERSON><PERSON> da restituire", "itemsOutbound": "{{count}} articolo da inviare"}, "edit": {"requested": "Modifica ordine #{{editId}} richiesta", "confirmed": "Modifica ordine #{{editId}} confermata"}, "transfer": {"requested": "Richiesta di trasferimento ordine #{{transferId}} richiesta", "confirmed": "Richiesta di trasferimento ordine #{{transferId}} confermata", "declined": "Richiesta di trasferimento ordine #{{transferId}} rifiutata"}}}, "fields": {"displayId": "ID di visualizzazione", "refundableAmount": "Importo <PERSON>", "returnableQuantity": "Quantità restituibile"}}, "draftOrders": {"domain": "<PERSON><PERSON><PERSON>", "deleteWarning": "Stai per eliminare l'ordine bozza {{id}}. Questa azione non può essere annullata.", "paymentLinkLabel": "Link di pagamento", "cartIdLabel": "<PERSON> carrello", "markAsPaid": {"label": "Segna come pagato", "warningTitle": "Se<PERSON> come Pagato", "warningDescription": "Stai per segnare l'ordine bozza come pagato. Questa azione non può essere annullata e la raccolta del pagamento non sarà possibile in seguito."}, "status": {"open": "Aperto", "completed": "Completato"}, "create": {"createDraftOrder": "<PERSON><PERSON>", "createDraftOrderHint": "Crea un nuovo ordine bozza per gestire i dettagli di un ordine prima che venga effettuato.", "chooseRegionHint": "Scegli regione", "existingItemsLabel": "Articoli esistenti", "existingItemsHint": "Aggiungi prodotti esistenti all'ordine bozza.", "customItemsLabel": "Articoli personalizzati", "customItemsHint": "Aggiungi articoli personalizzati all'ordine bozza.", "addExistingItemsAction": "Aggiungi articoli esistenti", "addCustomItemAction": "Aggiungi articolo personalizzato", "noCustomItemsAddedLabel": "Nessun articolo personaliz<PERSON>to aggiunto ancora", "noExistingItemsAddedLabel": "Nessun articolo esistente aggiunto ancora", "chooseRegionTooltip": "Scegli prima una regione", "useExistingCustomerLabel": "Usa cliente esistente", "addShippingMethodsAction": "Aggiungi metodi di spedizione", "unitPriceOverrideLabel": "Sovrascrittura prezzo unitario", "shippingOptionLabel": "Opzione di spedizione", "shippingOptionHint": "Scegli l'opzione di spedizione per l'ordine bozza.", "shippingPriceOverrideLabel": "Sovrascrittura prezzo di spedizione", "shippingPriceOverrideHint": "Sovrascrivi il prezzo di spedizione per l'ordine bozza.", "sendNotificationLabel": "Invia notifica", "sendNotificationHint": "Invia una notifica al cliente quando l'ordine bozza viene creato."}, "validation": {"requiredEmailOrCustomer": "Email o cliente è obbligatorio.", "requiredItems": "Almeno un articolo è obbligatorio.", "invalidEmail": "L'email deve essere un indirizzo email valido."}}, "stockLocations": {"domain": "Posizioni & Spedizione", "list": {"description": "Gestisci le posizioni di stock del tuo negozio e le opzioni di spedizione."}, "create": {"header": "Crea Posizione di Stock", "hint": "Una posizione di stock è un sito fisico dove i prodotti sono immagazzinati e spediti.", "successToast": "Posizione {{name}} creata con successo."}, "edit": {"header": "Modifica Posizione di Stock", "viewInventory": "Visualizza inventario", "successToast": "Posizione {{name}} aggiornata con successo."}, "delete": {"confirmation": "Stai per eliminare la posizione di stock {{name}}. Questa azione non può essere annullata."}, "fulfillmentProviders": {"header": "Fornitori di Adempimento", "shippingOptionsTooltip": "Questo menu a discesa conterrà solo fornitori abilitati per questa posizione. Aggiungili alla posizione se il menu a discesa è disabilitato.", "label": "Fornitori di adempimento connessi", "connectedTo": "Connesso a {{count}} di {{total}} fornitori di adempimento", "noProviders": "Questa posizione di stock non è connessa a nessun fornitore di adempimento.", "action": "Collega Fornitori", "successToast": "Fornitori di adempimento per la posizione di stock aggiornati con successo."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON>"}, "shipping": {"header": "Spedizione"}, "disable": {"confirmation": "Sei sicuro di voler disabilitare {{name}}? Questo eliminerà tutte le zone di servizio associate e le opzioni di spedizione, e non può essere annullato.", "pickup": "Ritiro disabilitato con successo.", "shipping": "Spedizione disabilitata con successo."}, "enable": {"pickup": "Ritiro abilitato con successo.", "shipping": "Spedizione abilitata con successo."}}, "sidebar": {"header": "Configurazione Spedizione", "shippingProfiles": {"label": "Profili di Spedizione", "description": "Raggruppa i prodotti in base ai requisiti di spedizione"}}, "salesChannels": {"header": "Canali di Vendita", "label": "Canali di vendita connessi", "connectedTo": "Connesso a {{count}} di {{total}} canali di vendita", "noChannels": "La posizione non è connessa a nessun canale di vendita.", "action": "Collega canali di vendita", "successToast": "I canali di vendita sono stati aggiornati con successo."}, "shippingOptions": {"create": {"shipping": {"header": "Crea Opzione di Spedizione per {{zone}}", "hint": "Crea una nuova opzione di spedizione per definire come i prodotti vengono spediti da questa posizione.", "label": "Opzioni di spedizione", "successToast": "Opzione di spedizione {{name}} creata con successo."}, "returns": {"header": "Crea Opzione di Reso per {{zone}}", "hint": "Crea una nuova opzione di reso per definire come i prodotti vengono restituiti a questa posizione.", "label": "Opzioni di reso", "successToast": "Opzione di reso {{name}} creata con successo."}, "tabs": {"details": "<PERSON><PERSON><PERSON>", "prices": "<PERSON><PERSON>"}, "action": "Crea opzione"}, "delete": {"confirmation": "Stai per eliminare l'opzione di spedizione {{name}}. Questa azione non può essere annullata.", "successToast": "Opzione di spedizione {{name}} eliminata con successo."}, "edit": {"header": "Modifica Opzione di Spedizione", "action": "Modifica opzione", "successToast": "Opzione di spedizione {{name}} aggiornata con successo."}, "pricing": {"action": "Modifica prezzi"}, "fields": {"count": {"shipping_one": "{{count}} opzione di spedizione", "shipping_other": "{{count}} opzioni di spedizione", "returns_one": "{{count}} opzione di reso", "returns_other": "{{count}} opzioni di reso"}, "priceType": {"label": "Tipo di prezzo", "options": {"fixed": {"label": "<PERSON><PERSON>", "hint": "Il prezzo dell'opzione di spedizione è fisso e non cambia in base al contenuto dell'ordine."}, "calculated": {"label": "Calcolato", "hint": "Il prezzo dell'opzione di spedizione è calcolato dal fornitore di adempimento durante il checkout."}}}, "enableInStore": {"label": "Abilita nel negozio", "hint": "Se i clienti possono utilizzare questa opzione durante il checkout."}, "provider": "Fornitore di adempimento", "profile": "Profilo di spedizione"}}, "serviceZones": {"create": {"headerPickup": "Crea Zona di Servizio per Ritiro da {{location}}", "headerShipping": "Crea Zona di Servizio per Spedizione da {{location}}", "action": "Crea zona di servizio", "successToast": "Zona di servizio {{name}} creata con successo."}, "edit": {"header": "Modifica Zona di Servizio", "successToast": "Zona di servizio {{name}} aggiornata con successo."}, "delete": {"confirmation": "Stai per eliminare la zona di servizio {{name}}. Questa azione non può essere annullata.", "successToast": "Zona di servizio {{name}} eliminata con successo."}, "manageAreas": {"header": "Gest<PERSON>ci Aree per {{name}}", "action": "Gest<PERSON><PERSON> aree", "label": "<PERSON><PERSON>", "hint": "Seleziona le aree geografiche che la zona di servizio copre.", "successToast": "<PERSON><PERSON> per {{name}} aggiornate con successo."}, "fields": {"noRecords": "Non ci sono zone di servizio a cui aggiungere opzioni di spedizione.", "tip": "Una zona di servizio è una raccolta di zone o aree geografiche. Viene utilizzata per limitare le opzioni di spedizione disponibili a un insieme definito di posizioni."}}}, "shippingProfile": {"domain": "Profili di Spedizione", "subtitle": "Raggruppa i prodotti con requisiti di spedizione simili in profili.", "create": {"header": "Crea Profilo di Spedizione", "hint": "Crea un nuovo profilo di spedizione per raggruppare i prodotti con requisiti di spedizione simili.", "successToast": "Profilo di spedizione {{name}} creato con successo."}, "delete": {"title": "Elimina Profilo di Spedizione", "description": "Stai per eliminare il profilo di spedizione {{name}}. Questa azione non può essere annullata.", "successToast": "Profilo di spedizione {{name}} eliminato con successo."}, "tooltip": {"type": "Inserisci il tipo di profilo di spedizione, ad esempio: Pesante, Ingombrante, Solo merci, ecc."}}, "taxRegions": {"domain": "Regioni Fiscali", "list": {"hint": "Gestisci cosa addebiti ai tuoi clienti quando acquistano da diversi paesi e regioni."}, "delete": {"confirmation": "Stai per eliminare una regione fiscale. Questa azione non può essere annullata.", "successToast": "La regione fiscale è stata eliminata con successo."}, "create": {"header": "Crea Regione Fiscale", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un paese specifico.", "errors": {"rateIsRequired": "L'aliquota fiscale è obbligatoria quando si crea un'aliquota fiscale predefinita.", "nameIsRequired": "Il nome è obbligatorio quando si crea un'aliquota fiscale predefinita."}, "successToast": "La regione fiscale è stata creata con successo."}, "province": {"header": "Province", "create": {"header": "Crea Regione Fiscale Provinciale", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per una provincia specifica."}}, "state": {"header": "Stati", "create": {"header": "Crea Regione Fiscale Statale", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per uno stato specifico."}}, "stateOrTerritory": {"header": "Stati o Territori", "create": {"header": "Crea Regione Fiscale Stato/Territorio", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per uno stato/territorio specifico."}}, "county": {"header": "Contee", "create": {"header": "Crea Regione Fiscale della Contea", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per una contea specifica."}}, "region": {"header": "Regioni", "create": {"header": "Crea Regione Fiscale", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per una regione specifica."}}, "department": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Crea Regione Fiscale del Dipartimento", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un dipartimento specifico."}}, "territory": {"header": "Te<PERSON><PERSON><PERSON>", "create": {"header": "Crea Regione Fiscale del Territorio", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un territorio specifico."}}, "prefecture": {"header": "Prefetture", "create": {"header": "Crea Regione Fiscale della Prefettura", "hint": "Crea una nuova regione fiscale per definire le aliqu ote fiscali per una prefettura specifica."}}, "district": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Crea Regione Fiscale del Distretto", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un distretto specifico."}}, "governorate": {"header": "Governatorati", "create": {"header": "Crea Regione Fiscale del Governatorato", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un governatorato specifico."}}, "canton": {"header": "Cantoni", "create": {"header": "Crea Regione Fiscale del Cantone", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un cantone specifico."}}, "emirate": {"header": "Emirati", "create": {"header": "Crea Regione Fiscale degli Emirati", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un emirato specifico."}}, "sublevel": {"header": "So<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Crea Regione Fiscale di Sottolivello", "hint": "Crea una nuova regione fiscale per definire le aliquote fiscali per un sottolivello specifico."}}, "taxOverrides": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"header": "<PERSON><PERSON>", "hint": "Crea un'aliquota fiscale che sovrascrive le aliquote fiscali predefinite per condizioni selezionate."}, "edit": {"header": "Modifica Sovrascrittura", "hint": "Modifica l'aliquota fiscale che sovrascrive le aliquote fiscali predefinite per condizioni selezionate."}}, "taxRates": {"create": {"header": "<PERSON><PERSON>", "hint": "Crea una nuova aliquota fiscale per definire l'aliquota fiscale per una regione.", "successToast": "L'aliquota fiscale è stata creata con successo."}, "edit": {"header": "Modifica Aliquota Fiscale", "hint": "Modifica l'aliquota fiscale per definire l'aliquota fiscale per una regione.", "successToast": "L'aliquota fiscale è stata aggiornata con successo."}, "delete": {"confirmation": "Stai per eliminare l'aliquota fiscale {{name}}. Questa azione non può essere annullata.", "successToast": "L'aliquota fiscale è stata eliminata con successo."}}, "fields": {"isCombinable": {"label": "Combinabile", "hint": "Se questa aliquota fiscale può essere combinata con l'aliquota predefinita della regione.", "true": "Combinabile", "false": "Non combinabile"}, "defaultTaxRate": {"label": "Aliquota fiscale predefinita", "tooltip": "L'aliquota fiscale predefinita per questa regione. Un esempio è l'aliquota IVA standard per un paese o una regione.", "action": "Crea aliquota fiscale predefinita"}, "taxRate": "Aliquota fiscale", "taxCode": "Codice fiscale", "targets": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Seleziona gli obiettivi a cui si applicherà questa aliquota fiscale.", "options": {"product": "<PERSON><PERSON>tti", "productCollection": "Collezioni di prodotti", "productTag": "Tag di prodotto", "productType": "Tipi di prodotto", "customerGroup": "Gruppi di clienti"}, "operators": {"in": "in", "on": "su", "and": "e"}, "placeholders": {"product": "Cerca prodotti", "productCollection": "Cerca collezioni di prodotti", "productTag": "Cerca tag di prodotto", "productType": "Cerca tipi di prodotto", "customerGroup": "Cerca gruppo di clienti"}, "tags": {"product": "<PERSON><PERSON><PERSON>", "productCollection": "Collezione di prodotto", "productTag": "Tag di prodotto", "productType": "Tipo di prodotto", "customerGroup": "Gruppo di clienti"}, "modal": {"header": "Aggiungi obiettivi"}, "values_one": "{{count}} valore", "values_other": "{{count}} valori", "numberOfTargets_one": "{{count}} obiettivo", "numberOfTargets_other": "{{count}} obie<PERSON><PERSON>", "additionalValues_one": "e {{count}} più valore", "additionalValues_other": "e {{count}} più valori", "action": "Aggiungi obiettivo"}, "sublevels": {"labels": {"province": "Provincia", "state": "Stato", "region": "Regione", "stateOrTerritory": "Stato/Territorio", "department": "Dipartimento", "county": "Contea", "territory": "Territorio", "prefecture": "Prefettura", "district": "<PERSON><PERSON><PERSON><PERSON>", "governorate": "Governatorato", "emirate": "Emirato", "canton": "Cantone", "sublevel": "<PERSON><PERSON> sotto<PERSON>"}, "placeholders": {"province": "Seleziona provincia", "state": "Seleziona stato", "region": "Seleziona regione", "stateOrTerritory": "Seleziona stato/territorio", "department": "Seleziona dipartimento", "county": "Seleziona contea", "territory": "Seleziona territorio", "prefecture": "Seleziona prefettura", "district": "Seleziona distretto", "governorate": "Seleziona governatorato", "emirate": "Seleziona emirato", "canton": "Seleziona cantone"}, "tooltips": {"sublevel": "Inserisci il codice ISO 3166-2 per la regione fiscale di sottolivello.", "notPartOfCountry": "{{province}} non sembra far parte di {{country}}. Controlla se questo è corretto."}, "alert": {"header": "Le regioni di sottolivello sono disabilitate per questa regione fiscale", "description": "Le regioni di sottolivello sono disabilitate per questa regione per impostazione predefinita. Puoi abilitarle per creare regioni di sottolivello come province, stati o territori.", "action": "Abilita regioni di sottolivello"}}, "noDefaultRate": {"label": "<PERSON>essuna aliquota predefinita", "tooltip": "Questa regione fiscale non ha un'aliquota fiscale predefinita. Se c'è un'aliquota standard, come l'IVA di un paese, aggiungila a questa regione."}}}, "promotions": {"domain": "Promozioni", "sections": {"details": "Dettagli Promozione"}, "tabs": {"template": "Tipo", "details": "<PERSON><PERSON><PERSON>", "campaign": "Campagna"}, "fields": {"type": "Tipo", "value_type": "Tipo di Valore", "value": "Valore", "campaign": "Campagna", "method": "<PERSON><PERSON><PERSON>", "allocation": "Allocazione", "addCondition": "Aggiungi condizione", "clearAll": "<PERSON><PERSON>a tutto", "amount": {"tooltip": "Seleziona il codice valuta per abilitare l'impostazione dell'importo"}, "conditions": {"rules": {"title": "Chi può utilizzare questo codice?", "description": "Quale cliente è autorizzato a utilizzare il codice promozionale? Il codice promozionale può essere utilizzato da tutti i clienti se lasciato intatto."}, "target-rules": {"title": "A quali articoli sarà applicata la promozione?", "description": "La promozione sarà applicata agli articoli che soddisfano le seguenti condizioni."}, "buy-rules": {"title": "Cosa deve essere nel carrello per sbloccare la promozione?", "description": "Se queste condizioni corrispondono, abilitiamo la promozione sugli articoli target."}}}, "tooltips": {"campaignType": "Il codice valuta deve essere selezionato nella promozione per impostare un budget di spesa."}, "errors": {"requiredField": "Campo obbligatorio", "promotionTabError": "Correggi gli errori nella scheda Promozione prima di procedere"}, "toasts": {"promotionCreateSuccess": "Promozione ({{code}}) creata con successo."}, "create": {}, "edit": {"title": "Modifica Dettagli Promozione", "rules": {"title": "Modifica condizioni d'uso"}, "target-rules": {"title": "Modifica condizioni articolo"}, "buy-rules": {"title": "Modifica regole di acquisto"}}, "campaign": {"header": "Campagna", "edit": {"header": "Modifica Campagna", "successToast": "Campagna della promozione aggiornata con successo."}, "actions": {"goToCampaign": "Vai alla campagna"}}, "campaign_currency": {"tooltip": "Questa è la valuta della promozione. Cambiala dalla scheda Dettagli."}, "form": {"required": "Obbligatorio", "and": "E", "selectAttribute": "Seleziona Attributo", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON>", "description": "Aggiungi promozione a una campagna esistente.", "placeholder": {"title": "Nessuna campagna esistente", "desc": "Puoi crearne una per monitorare più promozioni e impostare limiti di budget."}}, "new": {"title": "Nuova Campagna", "description": "Crea una nuova campagna per questa promozione."}, "none": {"title": "Senza Campagna", "description": "Procedi senza associare la promozione a una campagna"}}, "status": {"title": "Stato"}, "method": {"label": "<PERSON><PERSON><PERSON>", "code": {"title": "Codice promozionale", "description": "I clienti devono inserire questo codice al checkout"}, "automatic": {"title": "Automatico", "description": "I clienti vedranno questa promozione al checkout"}}, "max_quantity": {"title": "Quantità Massima", "description": "Quantità massima di articoli a cui si applica questa promozione."}, "type": {"standard": {"title": "Standard", "description": "Una promozione standard"}, "buyget": {"title": "Comp<PERSON>", "description": "Compra X ottieni Y promozione"}}, "allocation": {"each": {"title": "<PERSON><PERSON><PERSON>", "description": "Applica valore su ogni articolo"}, "across": {"title": "Insieme", "description": "Applica valore su articoli"}}, "code": {"title": "Codice", "description": "Il codice che i tuoi clienti inseriscono durante il checkout."}, "value": {"title": "Valore Promozionale"}, "value_type": {"fixed": {"title": "Valore Promozionale", "description": "L'importo da scontare. es. 100"}, "percentage": {"title": "Valore Promozionale", "description": "La percentuale da scontare sull'importo. es. 8%"}}}, "deleteWarning": "Stai per eliminare la promozione {{code}}. Questa azione non può essere annullata.", "createPromotionTitle": "Crea Promozione", "type": "Tipo di Promozione", "conditions": {"add": "Aggiungi condizione", "list": {"noRecordsMessage": "Aggiungi una condizione per limitare a quali articoli si applica la promozione."}}}, "campaigns": {"domain": "Campagne", "details": "Dettagli della campagna", "status": {"active": "Attivo", "expired": "Scaduto", "scheduled": "Pianificato"}, "delete": {"title": "Sei sicuro?", "description": "Stai per eliminare la campagna '{{name}}'. Questa azione non può essere annullata.", "successToast": "Campagna '{{name}}' creata con successo."}, "edit": {"header": "Modifica Campagna", "description": "Modifica i dettagli della campagna.", "successToast": "Campagna '{{name}}' aggiornata con successo."}, "configuration": {"header": "Configurazione", "edit": {"header": "Modifica Configurazione Campagna", "description": "Modifica la configurazione della campagna.", "successToast": "Configurazione campagna aggiornata con successo."}}, "create": {"title": "<PERSON><PERSON>", "description": "Crea una campagna promozionale.", "hint": "Crea una campagna promozionale.", "header": "<PERSON><PERSON>", "successToast": "Campagna '{{name}}' creata con successo."}, "fields": {"name": "Nome", "identifier": "Identificatore", "start_date": "Data di inizio", "end_date": "Data di fine", "total_spend": "Budget speso", "total_used": "Budget utilizzato", "budget_limit": "Limite di budget", "campaign_id": {"hint": "Solo le campagne con lo stesso codice valuta della promozione sono mostrate in questo elenco."}}, "budget": {"create": {"hint": "Crea un budget per la campagna.", "header": "Budget Campagna"}, "details": "Budget della campagna", "fields": {"type": "Tipo", "currency": "Valuta", "limit": "Limite", "used": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Spesa", "description": "Imposta un limite sull'importo totale scontato di tutti gli utilizzi della promozione."}, "usage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Imposta un limite su quante volte la promozione può essere utilizzata."}}, "edit": {"header": "Modifica Budget Campagna"}}, "promotions": {"remove": {"title": "Rimuovi promozione dalla campagna", "description": "Stai per rimuovere {{count}} promozione(e) dalla campagna. Questa azione non può essere annullata."}, "alreadyAdded": "Questa promozione è già stata aggiunta alla campagna.", "alreadyAddedDiffCampaign": "Questa promozione è già stata aggiunta a un'altra campagna ({{name}}).", "currencyMismatch": "La valuta della promozione e della campagna non corrisponde", "toast": {"success": "Aggiunti {{count}} promozione(e) alla campagna con successo"}, "add": {"list": {"noRecordsMessage": "Crea prima una promozione."}}, "list": {"noRecordsMessage": "Non ci sono promozioni nella campagna."}}, "deleteCampaignWarning": "Stai per eliminare la campagna {{name}}. Questa azione non può essere annullata.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "<PERSON><PERSON>", "subtitle": "Crea vendite o sovrascrivi i prezzi per condizioni specifiche.", "delete": {"confirmation": "Stai per eliminare la lista prezzi {{title}}. Questa azione non può essere annullata.", "successToast": "<PERSON><PERSON> prezzi {{title}} eliminata con successo."}, "create": {"header": "<PERSON><PERSON>", "subheader": "Crea una nuova lista prezzi per gestire i prezzi dei tuoi prodotti.", "tabs": {"details": "<PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON>tti", "prices": "<PERSON><PERSON>"}, "successToast": "<PERSON><PERSON> prezzi {{title}} creata con successo.", "products": {"list": {"noRecordsMessage": "Crea prima un prodotto."}}}, "edit": {"header": "Modifica Lista Prezzi", "successToast": "<PERSON><PERSON> prezzi {{title}} aggiornata con successo."}, "configuration": {"header": "Configurazione", "edit": {"header": "Modifica Configurazione Lista Prezzi", "description": "Modifica la configurazione della lista prezzi.", "successToast": "Configurazione lista prezzi aggiornata con successo."}}, "products": {"header": "<PERSON><PERSON>tti", "actions": {"addProducts": "Aggiungi prodotti", "editPrices": "Modifica prezzi"}, "delete": {"confirmation_one": "Stai per eliminare i prezzi per {{count}} prodotto nella lista prezzi. Questa azione non può essere annullata.", "confirmation_other": "Stai per eliminare i prezzi per {{count}} prodotti nella lista prezzi. Questa azione non può essere annullata.", "successToast_one": "<PERSON>zzi per {{count}} prodotto eliminati con successo.", "successToast_other": "<PERSON>zzi per {{count}} prodotti eliminati con successo."}, "add": {"successToast": "<PERSON>zzi aggiunti con successo alla lista prezzi."}, "edit": {"successToast": "<PERSON>zzi aggiornati con successo."}}, "fields": {"priceOverrides": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"label": "Stato", "options": {"active": "Attivo", "draft": "<PERSON><PERSON>", "expired": "Scaduto", "scheduled": "Pianificato"}}, "type": {"label": "Tipo", "hint": "<PERSON>egli il tipo di lista prezzi che desideri creare.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON>", "description": "I prezzi in vendita sono cambiamenti temporanei dei prezzi per i prodotti."}, "override": {"label": "Sovrascrittura", "description": "Le sovrascritture sono solitamente utilizzate per creare prezzi specifici per i clienti."}}}, "startsAt": {"label": "La lista prezzi ha una data di inizio?", "hint": "Pianifica la lista prezzi per attivarsi in futuro."}, "endsAt": {"label": "La lista prezzi ha una data di scadenza?", "hint": "Pianifica la lista prezzi per disattivarsi in futuro."}, "customerAvailability": {"header": "Scegli gruppi di clienti", "label": "Disponibilità cliente", "hint": "Scegli quali gruppi di clienti la lista prezzi dovrebbe essere applicata.", "placeholder": "Cerca gruppi di clienti", "attribute": "Gruppi di clienti"}}}, "profile": {"domain": "<PERSON>ilo", "manageYourProfileDetails": "Gestisci i dettagli del tuo profilo.", "fields": {"languageLabel": "<PERSON><PERSON>", "usageInsightsLabel": "<PERSON><PERSON><PERSON>"}, "edit": {"header": "Modifica Profilo", "languageHint": "La lingua che desideri utilizzare nel pannello di amministrazione. Questo non cambia la lingua del tuo negozio.", "languagePlaceholder": "Seleziona lingua", "usageInsightsHint": "Condividi analisi sull'utilizzo e aiutaci a migliorare Medusa. Puoi leggere di più su cosa raccogliamo e come lo utilizziamo nella <0>documentazione</0>."}, "toast": {"edit": "Modifiche al profilo salvate"}}, "users": {"domain": "<PERSON><PERSON><PERSON>", "editUser": "Modifica Utente", "inviteUser": "Invita <PERSON>", "inviteUserHint": "Invita un nuovo utente al tuo negozio.", "sendInvite": "Invia invito", "pendingInvites": "<PERSON><PERSON>ti in attesa", "deleteInviteWarning": "Stai per eliminare l'invito per {{email}}. Questa azione non può essere annullata.", "resendInvite": "Reinvia invito", "copyInviteLink": "Copia link di invito", "expiredOnDate": "<PERSON><PERSON>uto il {{date}}", "validFromUntil": "Valido da <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Accettato il {{date}}", "inviteStatus": {"accepted": "Accettato", "pending": "In attesa", "expired": "Scaduto"}, "roles": {"admin": "Amministratore", "developer": "Sviluppatore", "member": "Membro"}, "deleteUserWarning": "Stai per eliminare l'utente {{name}}. Questa azione non può essere annullata.", "invite": "Invi<PERSON>"}, "store": {"domain": "Negozio", "manageYourStoresDetails": "Gestisci i dettagli del tuo negozio", "editStore": "Modifica negozio", "defaultCurrency": "Valuta predefinita", "defaultRegion": "Regione predefinita", "swapLinkTemplate": "Modifica modello di link", "paymentLinkTemplate": "Modifica modello di link di pagamento", "inviteLinkTemplate": "Modifica modello di link di invito", "currencies": "Valute", "addCurrencies": "Aggiungi valute", "enableTaxInclusivePricing": "<PERSON><PERSON><PERSON>zzi inclusivi di tasse", "disableTaxInclusivePricing": "<PERSON><PERSON><PERSON><PERSON> prezzi inclusivi di tasse", "removeCurrencyWarning_one": "Stai per rimuovere {{count}} valuta dal tuo negozio. Assicurati di aver rimosso tutti i prezzi che utilizzano la valuta prima di procedere.", "removeCurrencyWarning_other": "Stai per rimuovere {{count}} valute dal tuo negozio. Assicurati di aver rimosso tutti i prezzi che utilizzano le valute prima di procedere.", "currencyAlreadyAdded": "La valuta è già stata aggiunta al tuo negozio.", "edit": {"header": "Modifica Negozio"}, "toast": {"update": "Negozio aggiornato con successo", "currenciesUpdated": "Valute aggiornate con successo", "currenciesRemoved": "Valute rimosse dal negozio con successo", "updatedTaxInclusivitySuccessfully": "Inclusività fiscale aggiornata con successo"}}, "regions": {"domain": "Regioni", "subtitle": "Una regione è un'area in cui vendi prodotti. Può coprire più paesi e ha diverse aliquote fiscali, fornitori e valute.", "createRegion": "Crea Regione", "createRegionHint": "Gest isci le aliquote fiscali e i fornitori per un insieme di paesi.", "addCountries": "<PERSON><PERSON><PERSON><PERSON><PERSON> paesi", "editRegion": "Modifica Regione", "countriesHint": "Aggiungi i paesi inclusi in questa regione.", "deleteRegionWarning": "Stai per eliminare la regione {{name}}. Questa azione non può essere annullata.", "removeCountriesWarning_one": "Stai per rimuovere {{count}} paese dalla regione. Questa azione non può essere annullata.", "removeCountriesWarning_other": "Stai per rimuovere {{count}} paesi dalla regione. Questa azione non può essere annullata.", "removeCountryWarning": "Stai per rimuovere il paese {{name}} dalla regione. Questa azione non può essere annullata.", "automaticTaxesHint": "<PERSON>uan<PERSON> a<PERSON>, le tasse saranno calcolate solo al checkout in base all'indirizzo di spedizione.", "taxInclusiveHint": "<PERSON>uan<PERSON> a<PERSON>, i prezzi nella regione saranno inclusivi di tasse.", "providersHint": "Aggiungi quali fornitori di pagamento sono disponibili in questa regione.", "shippingOptions": "Opzioni di Spedizione", "deleteShippingOptionWarning": "Stai per eliminare l'opzione di spedizione {{name}}. Questa azione non può essere annullata.", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outbound": "In uscita", "priceType": "Tipo di Prezzo", "flatRate": "<PERSON><PERSON><PERSON>", "calculated": "Calcolato", "list": {"noRecordsMessage": "Crea una regione per le aree in cui vendi."}, "toast": {"delete": "Regione eliminata con successo", "edit": "Modifica regione salvata", "create": "Regione creata con successo", "countries": "Paesi della regione aggiornati con successo"}, "shippingOption": {"createShippingOption": "Crea Opzione di Spedizione", "createShippingOptionHint": "Crea una nuova opzione di spedizione per la regione.", "editShippingOption": "Modifica Opzione di Spedizione", "fulfillmentMethod": "Metodo di Evasione", "type": {"outbound": "In uscita", "outboundHint": "Usa questo se stai creando un'opzione di spedizione per inviare prodotti al cliente.", "return": "Restituzione", "returnHint": "Usa questo se stai creando un'opzione di spedizione per il cliente per restituire prodotti a te."}, "priceType": {"label": "Tipo di Prezzo", "flatRate": "Tariffa fissa", "calculated": "Calcolato"}, "availability": {"adminOnly": "Solo per amministratori", "adminOnlyHint": "Quando abilitato, l'opzione di spedizione sarà disponibile solo nel pannello di amministrazione e non nel negozio online."}, "taxInclusiveHint": "Quando abilitato, il prezzo dell'opzione di spedizione sarà inclusivo di tasse.", "requirements": {"label": "Requisiti", "hint": "Specifica i requisiti per l'opzione di spedizione."}}}, "taxes": {"domain": "Regioni Fiscali", "domainDescription": "Gestisci la tua regione fiscale", "countries": {"taxCountriesHint": "Le impostazioni fiscali si applicano ai paesi elencati."}, "settings": {"editTaxSettings": "Modifica Impostazioni Fiscali", "taxProviderLabel": "<PERSON><PERSON><PERSON>", "systemTaxProviderLabel": "Fornitore Fiscale di Sistema", "calculateTaxesAutomaticallyLabel": "Calcola le tasse automaticamente", "calculateTaxesAutomaticallyHint": "<PERSON>uando abilitato, le aliquote fiscali saranno calcolate automaticamente e applicate ai carrelli. Quando disabilitato, le tasse devono essere calcolate manualmente al checkout. Le tasse manuali sono raccomandate per l'uso con fornitori fiscali di terze parti.", "applyTaxesOnGiftCardsLabel": "Applica tasse sulle carte regalo", "applyTaxesOnGiftCardsHint": "<PERSON>uan<PERSON> abilita<PERSON>, le tasse saranno applicate alle carte regalo al checkout. In alcuni paesi, le normative fiscali richiedono l'applic azione delle tasse sulle carte regalo al momento dell'acquisto.", "defaultTaxRateLabel": "Aliquota fiscale predefinita", "defaultTaxCodeLabel": "Codice fiscale predefinito"}, "defaultRate": {"sectionTitle": "Aliquota Fiscale Predefinita"}, "taxRate": {"sectionTitle": "<PERSON><PERSON><PERSON><PERSON>", "createTaxRate": "<PERSON><PERSON>", "createTaxRateHint": "Crea una nuova aliquota fiscale per la regione.", "deleteRateDescription": "Stai per eliminare l'aliquota fiscale {{name}}. Questa azione non può essere annullata.", "editTaxRate": "Modifica Aliquota Fiscale", "editRateAction": "Modifica aliquota", "editOverridesAction": "Modifica sovrascritture", "editOverridesTitle": "Modifica Sovrascritture Aliquota Fiscale", "editOverridesHint": "Specifica le sovrascritture per l'aliquota fiscale.", "deleteTaxRateWarning": "Stai per eliminare l'aliquota fiscale {{name}}. Questa azione non può essere annullata.", "productOverridesLabel": "Sovras<PERSON>rit<PERSON> prodotto", "productOverridesHint": "Specifica le sovrascritture del prodotto per l'aliquota fiscale.", "addProductOverridesAction": "Aggiungi sovrascritture prodotto", "productTypeOverridesLabel": "Sovrascritture tipo di prodotto", "productTypeOverridesHint": "Specifica le sovrascritture del tipo di prodotto per l'aliquota fiscale.", "addProductTypeOverridesAction": "Aggiungi sovrascritture tipo di prodotto", "shippingOptionOverridesLabel": "Sovrascritture opzione di spedizione", "shippingOptionOverridesHint": "Specifica le sovrascritture per l'aliquota fiscale.", "addShippingOptionOverridesAction": "Aggiungi sovrascritture opzione di spedizione", "productOverridesHeader": "<PERSON><PERSON>tti", "productTypeOverridesHeader": "Tipi di Prodotto", "shippingOptionOverridesHeader": "Opzioni di Spedizione"}}, "locations": {"domain": "Posizioni", "editLocation": "Modifica posizione", "addSalesChannels": "Aggiungi canali di vendita", "noLocationsFound": "Nessuna posizione trovata", "selectLocations": "Seleziona le posizioni che hanno l'articolo in magazzino.", "deleteLocationWarning": "Stai per eliminare la posizione {{name}}. Questa azione non può essere annullata.", "removeSalesChannelsWarning_one": "Stai per rimuovere {{count}} canale di vendita dalla posizione.", "removeSalesChannelsWarning_other": "Stai per rimuovere {{count}} canali di vendita dalla posizione.", "toast": {"create": "Posizione creata con successo", "update": "Posizione aggiornata con successo", "removeChannel": "Canale di vendita rimosso con successo"}}, "reservations": {"domain": "Prenotazioni", "subtitle": "Gestisci la quantità riservata degli articoli di inventario.", "deleteWarning": "Stai per eliminare una prenotazione. Questa azione non può essere annullata."}, "salesChannels": {"domain": "Canali di Vendita", "subtitle": "Gestisci i canali online e offline su cui vendi prodotti.", "createSalesChannel": "Crea Canale di Vendita", "createSalesChannelHint": "Crea un nuovo canale di vendita per vendere i tuoi prodotti.", "enabledHint": "Specifica se il canale di vendita è abilitato.", "removeProductsWarning_one": "Stai per rimuovere {{count}} prodotto da {{sales_channel}}.", "removeProductsWarning_other": "Stai per rimuovere {{count}} prodotti da {{sales_channel}}.", "addProducts": "Aggiungi Prodotti", "editSalesChannel": "Modifica canale di vendita", "productAlreadyAdded": "Il prodotto è già stato aggiunto al canale di vendita.", "deleteSalesChannelWarning": "Stai per eliminare il canale di vendita {{name}}. Questa azione non può essere annullata.", "toast": {"create": "Canale di vendita creat o con successo", "update": "Canale di vendita aggiornato con successo", "delete": "Canale di vendita eliminato con successo"}, "products": {"list": {"noRecordsMessage": "Non ci sono prodotti nel canale di vendita."}, "add": {"list": {"noRecordsMessage": "Crea prima un prodotto."}}}}, "apiKeyManagement": {"domain": {"publishable": "Chiavi API Pubblicabili", "secret": "Chiavi <PERSON> Segrete"}, "subtitle": {"publishable": "Gestisci le chiavi API utilizzate nel negozio per limitare l'ambito delle richieste a specifici canali di vendita.", "secret": "Gestisci le chiavi API utilizzate per autenticare gli utenti amministratori nelle applicazioni di amministrazione."}, "status": {"active": "Attivo", "revoked": "Revocato"}, "type": {"publishable": "Pubblicabile", "secret": "Segret<PERSON>"}, "create": {"createPublishableHeader": "Crea Chiave API Pubblicabile", "createPublishableHint": "Crea una nuova chiave API pubblicabile per limitare l'ambito delle richieste a specifici canali di vendita.", "createSecretHeader": "Crea Chiave API Segreta", "createSecretHint": "Crea una nuova chiave API segreta per accedere all'API Medusa come utente amministratore autenticato.", "secretKeyCreatedHeader": "Chiave Se<PERSON>", "secretKeyCreatedHint": "La tua nuova chiave segreta è stata generata. Copiala e conservala in modo sicuro. Questa è l'unica volta che verrà visualizzata.", "copySecretTokenSuccess": "Chiave segreta copiata negli appunti.", "copySecretTokenFailure": "Impossibile copiare la chiave segreta negli appunti.", "successToast": "Chiave API creata con successo."}, "edit": {"header": "Modifica Chiave API", "description": "Modifica il titolo della chiave API.", "successToast": "Chiave API {{title}} aggiornata con successo."}, "salesChannels": {"title": "Aggiungi Canali di Vendita", "description": "Aggiungi i canali di vendita a cui la chiave API dovrebbe essere limitata.", "successToast_one": "{{count}} canale di vendita aggiunto con successo alla chiave API.", "successToast_other": "{{count}} canali di vendita aggiunti con successo alla chiave API.", "alreadyAddedTooltip": "Il canale di vendita è già stato aggiunto alla chiave API.", "list": {"noRecordsMessage": "Non ci sono canali di vendita nell'ambito della chiave API pubblicabile."}}, "delete": {"warning": "Stai per eliminare la chiave API {{title}}. Questa azione non può essere annullata.", "successToast": "Chiave API {{title}} eliminata con successo."}, "revoke": {"warning": "Stai per revocare la chiave API {{title}}. Questa azione non può essere annullata.", "successToast": "Chiave API {{title}} revocata con successo."}, "addSalesChannels": {"list": {"noRecordsMessage": "Crea prima un canale di vendita."}}, "removeSalesChannel": {"warning": "Stai per rimuovere il canale di vendita {{name}} dalla chiave API. Questa azione non può essere annullata.", "warningBatch_one": "Stai per rimuovere {{count}} canale di vendita dalla chiave API. Questa azione non può essere annullata.", "warningBatch_other": "Stai per rimuovere {{count}} canali di vendita dalla chiave API. Questa azione non può essere annullata.", "successToast": "Canale di vendita rimosso con successo dalla chiave API.", "successToastBatch_one": "{{count}} canale di vendita rimosso con successo dalla chiave API.", "successToastBatch_other": "{{count}} canali di vendita rimossi con successo dalla chiave API."}, "actions": {"revoke": "Revoca chiave API", "copy": " Copia chiave API", "copySuccessToast": "Chiave API copiata negli appunti."}, "table": {"lastUsedAtHeader": "<PERSON><PERSON><PERSON>", "createdAtHeader": "Creato Il"}, "fields": {"lastUsedAtLabel": "Ultimo utiliz<PERSON> il", "revokedByLabel": "Revocato <PERSON>", "revokedAtLabel": "Revocato il", "createdByLabel": "<PERSON><PERSON><PERSON> <PERSON>"}}, "returnReasons": {"domain": "Motivi di Restituzione", "subtitle": "Gestisci i motivi per gli articoli restituiti.", "calloutHint": "Gestisci i motivi per categorizzare i resi.", "editReason": "Modifica Motivo di Restituzione", "create": {"header": "Aggiungi Motivo di Restituzione", "subtitle": "Specifica i motivi più comuni per i resi.", "hint": "Crea un nuovo motivo di restituzione per categorizzare i resi.", "successToast": "Motivo di restituzione {{label}} creato con successo."}, "edit": {"header": "Modifica Motivo di Restituzione", "subtitle": "Modifica il valore del motivo di restituzione.", "successToast": "Motivo di restituzione {{label}} aggiornato con successo."}, "delete": {"confirmation": "Stai per eliminare il motivo di restituzione {{label}}. Questa azione non può essere annullata.", "successToast": "Motivo di restituzione {{label}} eliminato con successo."}, "fields": {"value": {"label": "Valore", "placeholder": "wrong_size", "tooltip": "Il valore deve essere un identificatore unico per il motivo di restituzione."}, "label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Taglia errata"}, "description": {"label": "Descrizione", "placeholder": "Il cliente ha ricevuto la taglia sbagliata"}}}, "login": {"forgotPassword": "Hai dimenticato la password? - <0><PERSON><PERSON><PERSON><PERSON></0>", "title": "Benvenuto in Medusa", "hint": "Accedi per accedere all'area account"}, "invite": {"title": "Benvenuto in Medusa", "hint": "Crea il tuo account qui sotto", "backToLogin": "Torna al login", "createAccount": "Crea account", "alreadyHaveAccount": "Hai già un account? - <0>Accedi</0>", "emailTooltip": "La tua email non può essere cambiata. Se desideri utilizzare un'altra email, deve essere inviato un nuovo invito.", "invalidInvite": "L'invito non è valido o è scaduto.", "successTitle": "Il tuo account è stato registrato", "successHint": "Inizia subito con Medusa Admin.", "successAction": "Inizia Medusa Admin", "invalidTokenTitle": "Il tuo token di invito non è valido", "invalidTokenHint": "Prova a richiedere un nuovo link di invito.", "passwordMismatch": "Le password non corrispondono", "toast": {"accepted": "In<PERSON>to acc<PERSON>to con successo"}}, "resetPassword": {"title": "Reim<PERSON>a password", "hint": "Inserisci la tua email qui sotto e ti invieremo istruzioni su come reimpostare la tua password.", "email": "Email", "sendResetInstructions": "Invia istruzioni di reimpostazione", "backToLogin": "<0><PERSON>na al login</0>", "newPasswordHint": "<PERSON><PERSON><PERSON> una nuova password qui sotto.", "invalidTokenTitle": "Il tuo token di reimpostazione non è valido", "invalidTokenHint": "Prova a richiedere un nuovo link di reimpostazione.", "expiredTokenTitle": "Il tuo token di reimpostazione è scaduto", "goToResetPassword": "Vai a Reimposta Password", "resetPassword": "Reim<PERSON>a password", "newPassword": "Nuova password", "repeatNewPassword": "R<PERSON>eti nuova password", "tokenExpiresIn": "Il token scade in <0>{{time}}</0> minuti", "successfulRequestTitle": "Email inviata con successo", "successfulRequest": "Ti abbiamo inviato un'email che puoi utilizzare per reimpostare la tua password. Controlla la tua cartella spam se non l'hai ricevuta dopo pochi minuti.", "successfulResetTitle": "Reimpostazione password riuscita", "successfulReset": "Accedi alla pagina di login.", "passwordMismatch": "Le password non corrispondono", "invalidLinkTitle": "Il tuo link di reimpostazione non è valido", "invalidLinkHint": "Prova a reimpostare la tua password di nuovo."}, "workflowExecutions": {"domain": "Flussi di lavoro", "subtitle": "Visualizza e tieni traccia delle esecuzioni dei flussi di lavoro nella tua applicazione Medusa.", "transactionIdLabel": "ID Transazione", "workflowIdLabel": "ID Flusso di lavoro", "progressLabel": "Progresso", "stepsCompletedLabel_one": "{{completed}} di {{count}} passaggio", "stepsCompletedLabel_other": "{{completed}} di {{count}} passaggi", "list": {"noRecordsMessage": "Nessun flusso di lavoro è stato eseguito, ancora."}, "history": {"sectionTitle": "Cronologia", "runningState": "In esecuzione...", "awaitingState": "In attesa", "failedState": "Fallito", "skippedState": "Saltato", "skippedFailureState": "Saltato (Fallito)", "definitionLabel": "Definizione", "outputLabel": "Output", "compensateInputLabel": "Compensare input", "revertedLabel": "<PERSON><PERSON><PERSON>", "errorLabel": "Errore"}, "state": {"done": "<PERSON><PERSON>", "failed": "Fallito", "reverted": "<PERSON><PERSON><PERSON>", "invoking": "Invocando", "compensating": "Compensando", "notStarted": "Non iniziato"}, "transaction": {"state": {"waitingToCompensate": "In attesa di compensazione"}}, "step": {"state": {"skipped": "Saltato", "skippedFailure": "Saltato (Fallito)", "dormant": "Dormiente", "timeout": "Timeout"}}}, "productTypes": {"domain": "Tipi di prodotto", "subtitle": "Organizza i tuoi prodotti in tipi.", "create": {"header": "<PERSON><PERSON> di Prodotto", "hint": "Crea un nuovo tipo di prodotto per categorizzare i tuoi prodotti.", "successToast": "Tipo di prodotto {{value}} creato con successo."}, "edit": {"header": "Modifica Tipo di Prodotto", "successToast": "Tipo di prodotto {{value}} aggiornato con successo."}, "delete": {"confirmation": "Stai per eliminare il tipo di prodotto {{value}}. Questa azione non può essere annullata.", "successToast": "Tipo di prodotto {{value}} eliminato con successo."}, "fields": {"value": "Valore"}}, "productTags": {"domain": "Tag di prodotto", "create": {"header": "Crea Tag di Prodotto", "subtitle": "Crea un nuovo tag di prodotto per categorizzare i tuoi prodotti.", "successToast": "Tag di prodotto {{value}} creato con successo."}, "edit": {"header": "Modifica Tag di Prodotto", "subtitle": "Modifica il valore del tag di prodotto.", "successToast": "Tag di prodotto {{value}} aggiornato con successo."}, "delete": {"confirmation": "Stai per eliminare il tag di prodotto {{value}}. Questa azione non può essere annullata.", "successToast": "Tag di prodotto {{value}} eliminato con successo."}, "fields": {"value": "Valore"}}, "notifications": {"domain": "Notifiche", "emptyState": {"title": "Nessuna notifica", "description": "Non hai notifiche al momento, ma una volta che ne avrai, saranno qui."}, "accessibility": {"description": "Le notifiche sulle attività di Medusa saranno elencate qui."}}, "errors": {"serverError": "Errore del server - Riprova più tardi.", "invalidCredentials": "<PERSON>ail o <PERSON> errati"}, "statuses": {"scheduled": "Pianificato", "expired": "Scaduto", "active": "Attivo", "enabled": "Abilitato", "disabled": "Disabilitato"}, "labels": {"productVariant": "<PERSON><PERSON><PERSON> di Prodotto", "prices": "<PERSON><PERSON>", "available": "Disponibile", "inStock": "In magazzino", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "<PERSON><PERSON><PERSON>"}, "fields": {"amount": "Importo", "refundAmount": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nome", "default": "Predefinito", "lastName": "Cognome", "firstName": "Nome", "title": "<PERSON><PERSON>", "customTitle": "<PERSON><PERSON>", "manageInventory": "Gestisci inventario", "inventoryKit": "Ha kit di inventario", "inventoryItems": "Articoli di inventario", "inventoryItem": "Articolo di inventario", "requiredQuantity": "Quantità richiesta", "description": "Descrizione", "email": "Email", "password": "Password", "repeatPassword": "Ripeti Password", "confirmPassword": "Conferma Password", "newPassword": "Nuova Password", "repeatNewPassword": "Ripeti nuova Password", "categories": "Categorie", "shippingMethod": "Metodo di spedizione", "configurations": "Configurazioni", "conditions": "Condizioni", "category": "Categoria", "collection": "Collezione", "discountable": "Scontabile", "handle": "Maniglia", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item": "Articolo", "qty": "qty.", "limit": "Limite", "tags": "Tag", "type": "Tipo", "reason": "Motivo", "none": "nessuno", "all": "tutti", "search": "Cerca", "percentage": "Percent<PERSON><PERSON>", "sales_channels": "Canali di vendita", "customer_groups": "Gruppi di clienti", "product_tags": "Tag di prodotto", "product_types": "Tipi di prodotto", "product_collections": "Collezioni di prodotto", "status": "Stato", "code": "Codice", "value": "Valore", "disabled": "Disabilitato", "dynamic": "Dinamico", "normal": "Normale", "years": "<PERSON><PERSON>", "months": "Me<PERSON>", "days": "<PERSON><PERSON><PERSON>", "hours": "Ore", "minutes": "Minuti", "totalRedemptions": "Riscatti totali", "countries": "<PERSON><PERSON>", "paymentProviders": "Fornitori di pagamento", "refundReason": "Motivo del rimborso", "fulfillmentProviders": "Fornitori di evasione", "fulfillmentProvider": "Fornitore di evasione", "providers": "Fornitori", "availability": "Disponibilità", "inventory": "Inventario", "optional": "Opzionale", "note": "<PERSON>a", "automaticTaxes": "Tasse automatiche", "taxInclusivePricing": "<PERSON>zzi inclusivi di tasse", "currency": "Valuta", "address": "<PERSON><PERSON><PERSON><PERSON>", "address2": "Appartamento, suite, ecc.", "city": "Città", "postalCode": "Codice postale", "country": "<PERSON><PERSON>", "state": "Stato", "province": "Provincia", "company": "Azienda", "phone": "Telefono", "metadata": "<PERSON><PERSON><PERSON>", "selectCountry": "Seleziona paese", "products": "<PERSON><PERSON>tti", "variants": "<PERSON><PERSON><PERSON>", "orders": "<PERSON><PERSON><PERSON>", "account": "Account", "total": "Totale ordine", "paidTotal": "Totale pagato", "totalExclTax": "Totale escl. tasse", "subtotal": "Subtotale", "shipping": "Spedizione", "outboundShipping": "Spedizione in uscita", "returnShipping": "Spedizione di ritorno", "tax": "Tassa", "created": "<PERSON><PERSON><PERSON>", "key": "Chiave", "customer": "Cliente", "date": "Data", "order": "Ordine", "fulfillment": "Evasione", "provider": "Fornitore", "payment": "Pagamento", "items": "Articoli", "salesChannel": "Canale di vendita", "region": "Regione", "discount": "Sconto", "role": "<PERSON><PERSON><PERSON>", "sent": "Inviato", "salesChannels": "Canali di vendita", "product": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Aggiornato", "revokedAt": "Revocato il", "true": "Vero", "false": "<PERSON><PERSON><PERSON>", "giftCard": "<PERSON>ta regalo", "tag": "Tag", "dateIssued": "Data di emissione", "issuedDate": "Data di emissione", "expiryDate": "Data di scadenza", "price": "Prezzo", "priceTemplate": "Prezzo {{regionOrCurrency}}", "height": "Altezza", "width": "<PERSON><PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON><PERSON>", "weight": "Peso", "midCode": "Codice MID", "hsCode": "Codice HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Quantità in inventario", "barcode": "Codice a barre", "countryOfOrigin": "Paese di origine", "material": "Materiale", "thumbnail": "Miniatura", "sku": "SKU", "managedInventory": "Inventario gestito", "allowBackorder": "Consenti ordine arretrato", "inStock": "In magazzino", "location": "Posizione", "quantity": "Quantità", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "Genitore", "minSubtotal": "<PERSON><PERSON>", "maxSubtotal": "<PERSON><PERSON>", "shippingProfile": "Profilo di spedizione", "summary": "Riepilogo", "details": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "rate": "<PERSON>riff<PERSON>", "requiresShipping": "Richiede spedizione", "unitPrice": "Prezzo unitario", "startDate": "Data di inizio", "endDate": "Data di fine", "draft": "<PERSON><PERSON>", "values": "Valori"}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON>", "months_one": "Mese", "months_other": "Me<PERSON>", "weeks_one": "Set<PERSON><PERSON>", "weeks_other": "<PERSON><PERSON><PERSON><PERSON>", "days_one": "<PERSON><PERSON><PERSON>", "days_other": "<PERSON><PERSON><PERSON>", "hours_one": "<PERSON>a", "hours_other": "Ore", "minutes_one": "Min<PERSON>", "minutes_other": "Minuti", "seconds_one": "Secondo", "seconds_other": "Secondi"}}