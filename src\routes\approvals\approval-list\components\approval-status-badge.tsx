import { Badge } from "@medusajs/ui"
import { useTranslation } from "react-i18next"

interface ApprovalStatusBadgeProps {
  status: string
}

export const ApprovalStatusBadge = ({ status }: ApprovalStatusBadgeProps) => {
  const { t } = useTranslation()

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "orange"
      case "approved":
        return "green"
      case "rejected":
        return "red"
      case "expired":
        return "grey"
      default:
        return "grey"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return t("approvals.status.pending", "Pending")
      case "approved":
        return t("approvals.status.approved", "Approved")
      case "rejected":
        return t("approvals.status.rejected", "Rejected")
      case "expired":
        return t("approvals.status.expired", "Expired")
      default:
        return status
    }
  }

  return (
    <Badge color={getStatusColor(status)} size="2xsmall">
      {getStatusLabel(status)}
    </Badge>
  )
}