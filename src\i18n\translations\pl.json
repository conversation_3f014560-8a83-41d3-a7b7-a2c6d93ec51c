{"$schema": "./$schema.json", "general": {"ascending": "Rosnąco", "descending": "Malejąco", "add": "<PERSON><PERSON><PERSON>", "start": "Początek", "end": "Koniec", "open": "Otwórz", "close": "Zamknij", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "range": "<PERSON><PERSON><PERSON>", "search": "Szukaj", "of": "z", "results": "wyników", "pages": "strony", "next": "<PERSON><PERSON>", "prev": "Poprzedni", "is": "jest", "timeline": "<PERSON><PERSON>", "success": "Sukces", "warning": "Ostrzeżenie", "tip": "Wskazówka", "error": "Błąd", "select": "<PERSON><PERSON><PERSON><PERSON>", "selected": "Wybrany", "enabled": "Włączony", "disabled": "Wyłączony", "expired": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktywny", "revoked": "Odebrany", "new": "Nowy", "modified": "Zmodyfikowany", "added": "Dodano", "removed": "<PERSON><PERSON><PERSON><PERSON>", "admin": "Administrator", "store": "<PERSON>kle<PERSON>", "details": "Szczegóły", "items_one": "{{count}} p<PERSON><PERSON><PERSON>t", "items_few": "{{count}} p<PERSON><PERSON><PERSON><PERSON>", "items_many": "{{count}} p<PERSON><PERSON><PERSON><PERSON>", "items_other": "{{count}} przedmiotów", "countSelected": "{{count}} w<PERSON><PERSON><PERSON>ch", "countOfTotalSelected": "{{count}} z {{total}} wybranych", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} wi<PERSON><PERSON>j", "areYouSure": "<PERSON><PERSON> j<PERSON> p<PERSON>?", "noRecordsFound": "Nie znaleziono rekordów", "typeToConfirm": "Wpisz {val} aby <PERSON>:", "noResultsTitle": "Brak wyników", "noResultsMessage": "Spróbuj zmienić filtry lub zapytanie", "noSearchResults": "Brak wyników wyszukiwania", "noSearchResultsFor": "<PERSON>rak wyników dla <0>'{{query}}'</0>", "noRecordsTitle": "Brak rekordów", "noRecordsMessage": "Brak rekordów do wyświetlenia", "unsavedChangesTitle": "Czy na pewno chcesz opuścić ten formularz?", "unsavedChangesDescription": "<PERSON><PERSON> niezap<PERSON>e zmiany, które zostaną utracone po wyjściu.", "includesTaxTooltip": "Ceny w tej kolumnie zawierają podatek.", "excludesTaxTooltip": "Ceny w tej kolumnie są bez podatku.", "noMoreData": "Brak dodatkowych danych"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} klucz", "numberOfKeys_few": "{{count}} klucze", "numberOfKeys_many": "{{count}} k<PERSON><PERSON>y", "numberOfKeys_other": "{{count}} k<PERSON><PERSON>y", "drawer": {"header_one": "JSON <0>· {{count}} klucz</0>", "header_few": "JSON <0>· {{count}} klucze</0>", "header_many": "JSON <0>· {{count}} kluczy</0>", "header_other": "JSON <0>· {{count}} kluczy</0>", "description": "<PERSON><PERSON><PERSON><PERSON> dane JSON dla tego obiektu."}}, "metadata": {"header": "Metadane", "numberOfKeys_one": "{{count}} klucz", "numberOfKeys_few": "{{count}} klucze", "numberOfKeys_many": "{{count}} k<PERSON><PERSON>y", "numberOfKeys_other": "{{count}} k<PERSON><PERSON>y", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> metadane dla tego obiektu.", "successToast": "Metadane zostały pomyślnie zaktualizowane.", "actions": {"insertRowAbove": "Wstaw wiersz powyżej", "insertRowBelow": "Wstaw wiersz poniżej", "deleteRow": "<PERSON><PERSON><PERSON> wiersz"}, "labels": {"key": "<PERSON><PERSON>cz", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "complexRow": {"label": "Niektóre wiersze są wyłączone", "description": "Ten obiekt zawiera złożone metadane, takie jak tablice lub obiekty, kt<PERSON><PERSON>ch nie można edytować tutaj. Aby edytowa<PERSON> wyłączone wiersze, użyj bezpośrednio API.", "tooltip": "Ten wiersz jest wył<PERSON><PERSON>ony, ponieważ zawiera dane złożone."}}}, "validation": {"mustBeInt": "<PERSON><PERSON>ść musi być licz<PERSON>ą całkowitą.", "mustBePositive": "War<PERSON>ść musi być licz<PERSON>ą dodatnią."}, "actions": {"save": "<PERSON><PERSON><PERSON><PERSON>", "saveAsDraft": "Zap<PERSON>z jako wersję roboczą", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copied": "Skopiowano", "duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publish": "Opublikuj", "create": "Utwórz", "delete": "Usuń", "remove": "Usuń", "revoke": "Cof<PERSON>j", "cancel": "<PERSON><PERSON><PERSON>", "forceConfirm": "<PERSON><PERSON><PERSON><PERSON>zen<PERSON>", "continueEdit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enable": "Włącz", "disable": "Wyłącz", "undo": "Cof<PERSON>j", "complete": "Ukończ", "viewDetails": "Zobacz szczegóły", "back": "Wstecz", "close": "Zamknij", "showMore": "Pokaż więcej", "continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>ez <PERSON>", "idCopiedToClipboard": "ID skopiowane do schowka", "addReason": "<PERSON><PERSON><PERSON>", "addNote": "<PERSON><PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Potwierdź", "edit": "<PERSON><PERSON><PERSON><PERSON>", "addItems": "Dodaj przedmio<PERSON>", "download": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "Przeglądaj", "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>", "export": "Eksportuj", "import": "Import<PERSON>j", "cannotUndo": "Tego działania nie można cofn<PERSON>"}, "operators": {"in": "W"}, "app": {"search": {"label": "Szukaj", "title": "Szukaj", "description": "Przeszukaj cały sklep, w tym zamówienia, produkty, klientów i inne.", "allAreas": "Wszystkie obszary", "navigation": "<PERSON><PERSON><PERSON><PERSON>", "openResult": "Otwórz wynik", "showMore": "Pokaż więcej", "placeholder": "Przejdź do lub znajdź cokolwiek...", "noResultsTitle": "Nie znaleziono wyników", "noResultsMessage": "Nie udało się znaleźć niczego pasującego do twojego wyszukiwania.", "emptySearchTitle": "<PERSON><PERSON><PERSON>, aby s<PERSON>", "emptySearchMessage": "Wprowadź słowo kluczowe lub frazę, aby eksp<PERSON>.", "loadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} wi<PERSON><PERSON>j", "groups": {"all": "Wszystkie obszary", "customer": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Grupy Klientów", "product": "Produkty", "productVariant": "Warianty Produktów", "inventory": "Zapas", "reservation": "Rezerwacje", "category": "<PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "Zamówienia", "promotion": "<PERSON><PERSON><PERSON><PERSON>", "campaign": "<PERSON><PERSON>ani<PERSON>", "priceList": "Cenniki", "user": "Użytkownicy", "region": "Regiony", "taxRegion": "Regiony Podatkowe", "returnReason": "Powody Zwrotów", "salesChannel": "Kanały Sprzedaży", "productType": "Typy Produktów", "productTag": "Tagi Produktów", "location": "Lokalizacje", "shippingProfile": "Profile Wysyłki", "publishableApiKey": "Publiczne Klucze API", "secretApiKey": "Tajne Klucze API", "command": "Polecenia", "navigation": "<PERSON><PERSON><PERSON><PERSON>"}}, "keyboardShortcuts": {"pageShortcut": "Przejdź do", "settingShortcut": "Ustawienia", "commandShortcut": "Polecenia", "then": "następnie", "navigation": {"goToOrders": "Zamówienia", "goToProducts": "Produkty", "goToCollections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCategories": "<PERSON><PERSON><PERSON>", "goToCustomers": "<PERSON><PERSON><PERSON><PERSON>", "goToCustomerGroups": "Grupy Klientów", "goToInventory": "Zapas", "goToReservations": "Rezerwacje", "goToPriceLists": "Cenniki", "goToPromotions": "<PERSON><PERSON><PERSON><PERSON>", "goToCampaigns": "<PERSON><PERSON>ani<PERSON>"}, "settings": {"goToSettings": "Ustawienia", "goToStore": "<PERSON>kle<PERSON>", "goToUsers": "Użytkownicy", "goToRegions": "Regiony", "goToTaxRegions": "Regiony Podatkowe", "goToSalesChannels": "Kanały Sprzedaży", "goToProductTypes": "Typy Produktów", "goToLocations": "Lokalizacje", "goToPublishableApiKeys": "Publiczne Klucze API", "goToSecretApiKeys": "Tajne Klucze API", "goToWorkflows": "Przepływy pracy", "goToProfile": "Profil", "goToReturnReasons": "Powody Zwrotów"}}, "menus": {"user": {"documentation": "Dokumentacja", "changelog": "Dziennik zmian", "shortcuts": "Skróty", "profileSettings": "Ustawienia profilu", "theme": {"label": "Motyw", "dark": "Ciemny", "light": "<PERSON><PERSON><PERSON>", "system": "Systemowy"}}, "store": {"label": "<PERSON>kle<PERSON>", "storeSettings": "Ustawi<PERSON> sklepu"}, "actions": {"logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> d<PERSON>."}, "common": {"extensions": "Rozszerzenia"}, "main": {"store": "<PERSON>kle<PERSON>", "storeSettings": "Ustawi<PERSON> sklepu"}, "settings": {"header": "Ustawienia", "general": "Ogólne", "developer": "<PERSON><PERSON><PERSON><PERSON>", "myAccount": "<PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "Widok", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "Zmiana widocznych kolumn jest wyłączona."}, "shortcuts": {"label": "Skróty", "commands": {"undo": "Cof<PERSON>j", "redo": "Ponów", "copy": "<PERSON><PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Usuń", "clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "moveUp": "Przesuń w górę", "moveDown": "Przesuń w dół", "moveLeft": "Przesuń w lewo", "moveRight": "Przesuń w prawo", "moveTop": "Przesuń na górę", "moveBottom": "Przesuń na dół", "selectDown": "Wybierz w dół", "selectUp": "Wybierz w górę", "selectColumnDown": "Wybierz kolumnę w dół", "selectColumnUp": "Wybierz kolumnę w górę", "focusToolbar": "Przejdź do paska narzędzi", "focusCancel": "<PERSON><PERSON><PERSON> fokus"}}, "errors": {"fixError": "Napraw błąd", "count_one": "{{count}} błąd", "numberOfKeys_few": "{{count}} b<PERSON><PERSON><PERSON>", "numberOfKeys_many": "{{count}} błędów", "count_other": "{{count}} błędów"}}, "filters": {"sortLabel": "<PERSON><PERSON><PERSON><PERSON>", "filterLabel": "Filtruj", "searchLabel": "Szukaj", "date": {"today": "<PERSON><PERSON><PERSON><PERSON>", "lastSevenDays": "Ostatnie 7 dni", "lastThirtyDays": "Ostatnie 30 dni", "lastNinetyDays": "Ostatnie 90 dni", "lastTwelveMonths": "Ostatnie 12 miesięcy", "custom": "Niestandardowy", "from": "Od", "to": "Do", "starting": "Początek", "ending": "Koniec"}, "compare": {"lessThan": "Mniej niż", "greaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "exact": "Dokładnie", "range": "<PERSON><PERSON><PERSON>", "lessThanLabel": "mniej niż {{value}}", "greaterThanLabel": "wi<PERSON><PERSON><PERSON> niż {{value}}", "andLabel": "i"}, "sorting": {"alphabeticallyAsc": "od A do Z", "alphabeticallyDesc": "od Z do A", "dateAsc": "<PERSON><PERSON>", "dateDesc": "<PERSON><PERSON> <PERSON>"}, "radio": {"yes": "Tak", "no": "<PERSON><PERSON>", "true": "Prawda", "false": "Fałsz"}, "addFilter": "<PERSON><PERSON><PERSON> filtr"}, "errorBoundary": {"badRequestTitle": "400 - Nieprawidłowe żądanie", "badRequestMessage": "Serwer nie mógł zrozumieć żądania z powodu błędnej składni.", "notFoundTitle": "404 - <PERSON><PERSON> ma strony pod tym adresem", "notFoundMessage": "Sprawdź adres URL i spróbuj ponownie lub użyj paska wyszukiwania, aby <PERSON> to, czego szukasz.", "internalServerErrorTitle": "500 - <PERSON><PERSON><PERSON>d wewnętrzny serwera", "internalServerErrorMessage": "Wystąpił nieoczekiwany błąd na serwerze. Spróbuj ponownie później.", "defaultTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd", "defaultMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieoczekiwany błąd podczas renderowania tej strony.", "noMatchMessage": "St<PERSON>, kt<PERSON><PERSON><PERSON>, nie istnie<PERSON>.", "backToDashboard": "Powrót do pulpitu"}, "addresses": {"title": "Ad<PERSON>y", "shippingAddress": {"header": "<PERSON><PERSON>", "editHeader": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "editLabel": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, "billingAddress": {"header": "<PERSON><PERSON>", "editHeader": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "editLabel": "<PERSON><PERSON>", "label": "<PERSON><PERSON>", "sameAsShipping": "<PERSON>ki sam jak adres dos<PERSON>"}, "contactHeading": "Kontakt", "locationHeading": "Lokalizacja"}, "email": {"editHeader": "Edytuj email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Przenie<PERSON> w<PERSON><PERSON><PERSON>ć", "label": "Przenie<PERSON> w<PERSON><PERSON><PERSON>ć", "details": {"order": "Szczegóły zamówienia", "draft": "Szczegóły szkicu"}, "currentOwner": {"label": "Obecny właściciel", "hint": "Obecny właściciel zamówienia."}, "newOwner": {"label": "Nowy właściciel", "hint": "Nowy właściciel, na którego zostanie przeniesione zamówienie."}, "validation": {"mustBeDifferent": "Nowy właściciel musi się różnić od obecnego właściciela.", "required": "Nowy właściciel jest wymagany."}}, "sales_channels": {"availableIn": "Dostępne w <0>{{x}}</0> z <1>{{y}}</1> kanałów sprzedaży"}, "products": {"domain": "Produkty", "list": {"noRecordsMessage": "Utwórz swój pierwszy produkt, aby r<PERSON><PERSON> sprzedaż."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> produkt", "description": "Edytuj szczegóły produktu.", "successToast": "Produkt {{title}} został pomyślnie zaktualizowany."}, "create": {"title": "Utwórz produkt", "description": "Utwórz nowy produkt.", "header": "Ogólne", "tabs": {"details": "Szczegóły", "organize": "Organizacja", "variants": "Warianty", "inventory": "Zestawy magazynowe"}, "errors": {"variants": "Wybierz co najmniej jeden wariant.", "options": "Utwórz co najmniej jedną opcję.", "uniqueSku": "SKU musi być unikalny."}, "inventory": {"heading": "Zestawy magazynowe", "label": "Dodaj pozycje magazynowe do zestawu wariantu.", "itemPlaceholder": "Wybierz pozycję magazynową", "quantityPlaceholder": "Ile z tych elementów jest potrzebnych do zestawu?"}, "variants": {"header": "Warianty", "subHeadingTitle": "Ta<PERSON>, to produkt z <PERSON><PERSON>ami", "subHeadingDescription": "<PERSON><PERSON><PERSON>, stworzymy dla Ciebie domyślny wariant", "optionTitle": {"placeholder": "Rozmiar"}, "optionValues": {"placeholder": "Mały, Średni, Duży"}, "productVariants": {"label": "Warianty produktu", "hint": "Ta kolejność wpłynie na porządek wariantów w <PERSON>im skle<PERSON>.", "alert": "<PERSON><PERSON><PERSON>, aby utworzyć warianty.", "tip": "Warianty, które nie zostały wybrane, nie zostaną utworzone. Możesz zawsze później dodać i edytować warianty."}, "productOptions": {"label": "Op<PERSON>je produktu", "hint": "Określ opcje dla produktu, np. kolor, rozmiar, itp."}}, "successToast": "Produkt {{title}} został pomyślnie utworzony."}, "export": {"header": "Eksportuj listę produktów", "description": "Eksportuj listę produktów do pliku CSV.", "success": {"title": "Przetwarzamy Twój eksport", "description": "Eksport danych może zająć kilka minut. Powiadomimy <PERSON>ę, gdy b<PERSON><PERSON><PERSON> gotowy."}, "filters": {"title": "Filtry", "description": "Zastosuj filtry w podglą<PERSON>zie tabeli, a<PERSON> <PERSON> ten widok"}, "columns": {"title": "<PERSON><PERSON><PERSON>", "description": "Dostosuj eksportowane dane do określonych potrzeb"}}, "import": {"header": "Importuj listę produktów", "uploadLabel": "Importuj produkty", "uploadHint": "Przeciągnij i upuść plik CSV lub kliknij, aby przes<PERSON>", "description": "Importuj produkty, dostarczając plik CSV w ustalonym formacie", "template": {"title": "<PERSON><PERSON><PERSON><PERSON>, jak uł<PERSON>ż<PERSON>ę?", "description": "Pobierz p<PERSON> s<PERSON>, aby <PERSON><PERSON><PERSON>, że postępujesz zgodnie z właściwym formatem."}, "upload": {"title": "Prześlij plik CSV", "description": "Dzięki importom możesz dodawać lub aktualizować produkty. Aby zaktualizować istniejące produkty, musisz użyć istniejącego identyfikatora i uchwytu. Przed importem zostaniesz poproszony o potwierdzenie.", "preprocessing": "Przetwarzanie wstępne...", "productsToCreate": "Produkty zostaną utworzone", "productsToUpdate": "Produkty zostaną zaktualizowane"}, "success": {"title": "Przetwarzamy Twój import", "description": "Import danych może potrwać chwilę. Powi<PERSON>mi<PERSON>, gdy b<PERSON><PERSON><PERSON> gotowy."}}, "deleteWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> usunąć produkt {{title}}. Ta akcja nie może zostać cofnięta.", "variants": {"header": "Warianty", "empty": {"heading": "Brak wariantów", "description": "Nie ma wariantów do wyświetlenia."}, "filtered": {"heading": "Brak wariantów", "description": "<PERSON>e ma wariantów, które pasują do aktualnych kryteriów filtrów."}}, "attributes": "Atrybuty", "editAttributes": "<PERSON><PERSON><PERSON><PERSON>", "editOptions": "<PERSON><PERSON><PERSON><PERSON>", "editPrices": "<PERSON><PERSON><PERSON><PERSON> ceny", "media": {"label": "Media", "editHint": "Dodaj media do produktu, aby z<PERSON><PERSON><PERSON> go w swoim sklepie.", "makeThumbnail": "Ustaw jako miniaturę", "uploadImagesLabel": "Prześ<PERSON><PERSON> o<PERSON>zy", "uploadImagesHint": "Przeciągnij i upuść obrazy tutaj lub kliknij, aby prz<PERSON>.", "invalidFileType": "'{{name}}' nie jest obsługiwanym typem pliku. Obsługiwane typy plików to: {{types}}.", "failedToUpload": "Nie udało się przesłać dodanych mediów. Spróbuj ponownie.", "deleteWarning_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obraz. Ta akcja nie może zostać cofnięta.", "deleteWarning_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obrazy. Ta akcja nie może zostać cofnięta.", "deleteWarning_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obrazów. Ta akcja nie może zostać cofnięta.", "deleteWarning_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obrazów. Ta akcja nie może zostać cofnięta.", "deleteWarningWithThumbnail_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} o<PERSON><PERSON>, w tym miniaturę. Ta akcja nie może zostać cofnięta.", "deleteWarningWithThumbnail_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} o<PERSON><PERSON>, w tym miniaturę. Ta akcja nie może zostać cofnięta.", "deleteWarningWithThumbnail_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obrazów, w tym miniaturę. Ta akcja nie może zostać cofnięta.", "deleteWarningWithThumbnail_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} obrazów, w tym miniaturę. Ta akcja nie może zostać cofnięta.", "thumbnailTooltip": "Miniatura", "galleryLabel": "Galeria", "downloadImageLabel": "Pobierz aktualny obraz", "deleteImageLabel": "Usuń aktualny obraz", "emptyState": {"header": "Brak mediów", "description": "Dodaj media do produktu, aby z<PERSON><PERSON><PERSON> go w swoim sklepie.", "action": "Dodaj media"}, "successToast": "Media zostały pomyślnie zaktualizowane."}, "discountableHint": "<PERSON><PERSON><PERSON> o<PERSON>z<PERSON>, rabaty nie będą stosowane do tego produktu.", "noSalesChannels": "Niedostępny w żadnych kanałach sprzedaży", "variantCount_one": "{{count}} wariant", "variantCount_few": "{{count}} warianty", "variantCount_many": "{{count}} wariantów", "variantCount_other": "{{count}} wariantów", "deleteVariantWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> wariant {{title}}. Ta akcja nie może zosta<PERSON> cofnięta.", "productStatus": {"draft": "Szkic", "published": "Opublikowany", "proposed": "Proponowany", "rejected": "Odrzucony"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON>", "hint": "Podaj krótki i jasny tytuł dla swojego produktu.<0/>Zalecana długość to 50-60 znaków dla wyszukiwarek.", "placeholder": "<PERSON><PERSON>"}, "subtitle": {"label": "Podtytuł", "placeholder": "Ciepły i przytulny"}, "handle": {"label": "Uchwyt", "tooltip": "Uchwyt jest używany do odniesienia do produktu w <PERSON>im sklepie. Je<PERSON><PERSON> nie zostanie określony, uchwyt zostanie wygenerowany z tytułu produktu.", "placeholder": "kurtka-zimowa"}, "description": {"label": "Opis", "hint": "Podaj krótki i jasny opis dla swojego produktu.<0/>Zalecana długość to 120-160 znaków dla wyszukiwarek.", "placeholder": "Ciepła i przytulna kurtka"}, "discountable": {"label": "Rabaty", "hint": "<PERSON><PERSON><PERSON> o<PERSON>z<PERSON>, rabaty nie będą stosowane do tego produktu"}, "shipping_profile": {"label": "<PERSON><PERSON>", "hint": "Połącz produkt z profilem wysyłki"}, "type": {"label": "<PERSON><PERSON>"}, "collection": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "categories": {"label": "<PERSON><PERSON><PERSON>"}, "tags": {"label": "Tagi"}, "sales_channels": {"label": "Kanały sprzedaży", "hint": "Produkt będzie dostępny tylko w domyślnym kanale sprzedaży, jeśli pozostanie nietknięty."}, "countryOrigin": {"label": "<PERSON>raj <PERSON>"}, "material": {"label": "<PERSON><PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "weight": {"label": "W<PERSON>"}, "options": {"label": "Op<PERSON>je produktu", "hint": "Opcje służą do określenia koloru, rozmiaru itp. produktu", "add": "<PERSON><PERSON><PERSON>", "optionTitle": "Nazwa opcji", "optionTitlePlaceholder": "<PERSON><PERSON>", "variations": "Warianty (oddzielone przecinkami)", "variantionsPlaceholder": "Czerwony, Niebieski, Zielony"}, "variants": {"label": "Warianty produktu", "hint": "Niezaznaczone warianty nie zostaną utworzone. Ta kolejność wpłynie na to, jak warianty będą uszeregowane w Twoim interfejsie."}, "mid_code": {"label": "Kod MID"}, "hs_code": {"label": "Kod HS"}}, "variant": {"edit": {"header": "<PERSON><PERSON><PERSON><PERSON> war<PERSON>", "success": "Wariant produktu został pomyślnie edytowany"}, "create": {"header": "Szczegóły wariantu"}, "deleteWarning": "<PERSON>zy na pewno chcesz usunąć ten wariant?", "pricesPagination": "1 - {{current}} z {{total}} cen", "tableItemAvailable": "{{availableCount}} dostępnych", "tableItem_one": "{{availableCount}} dostępny w {{locationCount}} lokalizacji", "tableItem_few": "{{availableCount}} dostępne w {{locationCount}} lokalizacji", "tableItem_many": "{{availableCount}} dostępnych w {{locationCount}} lokalizacji", "tableItem_other": "{{availableCount}} dostępnych w {{locationCount}} lokalizacji", "inventory": {"notManaged": "Nie zarządzane", "manageItems": "Zarządzaj elementami magazynowymi", "notManagedDesc": "Zapasy nie są zarządzane dla tego wariantu. Włącz 'Zarząd<PERSON>j zapasami', a<PERSON> <PERSON><PERSON><PERSON><PERSON> stan magazynowy wariantu.", "manageKit": "Zarządzaj zestawem magazynowym", "navigateToItem": "Przejdź do elementu magazynowego", "actions": {"inventoryItems": "Przejdź do elementu magazynowego", "inventoryKit": "Pokaż elementy magazynowe"}, "inventoryKit": "Zestaw magazynowy", "inventoryKitHint": "<PERSON>zy ten wariant składa się z kilku elementów magazynowych?", "validation": {"itemId": "Proszę wybrać element magazynowy.", "quantity": "<PERSON><PERSON><PERSON><PERSON> jest wymagana. Proszę wprowadzić liczbę dodatnią."}, "header": "Stan magazynowy i zapasy", "editItemDetails": "Edytuj szczegóły elementu", "manageInventoryLabel": "Zarządzaj zapasami", "manageInventoryHint": "Po włączeniu będziemy zmieniać ilość w magazynie przy tworzeniu zamówień i zwrotów.", "allowBackordersLabel": "Zezwalaj na zamówienia z opóźnioną realizacją", "allowBackordersHint": "Po włączeniu klienci mogą kupić wariant nawet jeśli nie ma dostępnej ilości.", "toast": {"levelsBatch": "Poziomy zapasów zaktualizowane.", "update": "Element magazynowy pomyślnie zaktualizowany.", "updateLevel": "Poziom zapasów pomyślnie zaktualizowany.", "itemsManageSuccess": "Elementy magazynowe pomyślnie zaktualizowane."}}}, "options": {"header": "<PERSON><PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Opcja {{title}} została pomyślnie zaktualizowana."}, "create": {"header": "U<PERSON><PERSON><PERSON><PERSON> op<PERSON>", "successToast": "Opcja {{title}} została pomyślnie utworzona."}, "deleteWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> opcję produktu: {{title}}. <PERSON><PERSON> akcji nie można co<PERSON>."}, "organization": {"header": "Organizacja", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> organiza<PERSON>", "toasts": {"success": "Pomyślnie zaktualizowano organizację {{title}}."}}}, "stock": {"heading": "Zarządzaj stanem magazynowym produktów i ich lokalizacjami", "description": "Aktualizuj poziomy zapasów dla wszystkich wariantów produktu.", "loading": "<PERSON><PERSON><PERSON>, może to ch<PERSON><PERSON><PERSON>...", "tooltips": {"alreadyManaged": "Ten element magazynowy jest już edytowalny w ramach {{title}}.", "alreadyManagedWithSku": "Ten element magazynowy jest już edytowalny w ramach {{title}} ({{sku}})."}}, "shippingProfile": {"header": "Konfiguracja wysyłki", "edit": {"header": "Edytuj konfigurację wysyłki", "toasts": {"success": "Pomyślnie zaktualizowano profil wysyłki dla {{title}}."}}, "create": {"errors": {"required": "<PERSON>il wys<PERSON>ki jest wymagany."}}}, "toasts": {"delete": {"success": {"header": "Produkt usunięty", "description": "{{title}} został pomyślnie usunięty."}, "error": {"header": "<PERSON>e udało się usunąć produktu"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Organizuj produkty w kolekcje.", "createCollection": "Ut<PERSON><PERSON><PERSON>", "createCollectionHint": "Utwórz nową kolekcję, aby uporządkować swoje produkty.", "createSuccess": "Kolekcja została pomyślnie utworzona.", "editCollection": "<PERSON><PERSON><PERSON><PERSON>", "handleTooltip": "Uchwyt jest używany do odniesienia się do kolekcji w <PERSON>im sklepie. Je<PERSON>li nie zostanie określony, zostanie wygenerowany z tytułu kolekcji.", "deleteWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kolekcję {{title}}. <PERSON>j akcji nie można co<PERSON>.", "removeSingleProductWarning": "Zamierzas<PERSON> usunąć produkt {{title}} z kolekcji. Tej akcji nie można cofnąć.", "removeProductsWarning_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produkt z kolekcji. Tej akcji nie można cofn<PERSON>ć.", "removeProductsWarning_few": "<PERSON>ami<PERSON><PERSON><PERSON> {{count}} produkty z kolekcji. Tej akcji nie można cofnąć.", "removeProductsWarning_many": "Zamier<PERSON><PERSON>unąć {{count}} produktów z kolekcji. Tej akcji nie można cofnąć.", "removeProductsWarning_other": "Zamier<PERSON><PERSON>unąć {{count}} produktów z kolekcji. Tej akcji nie można cofnąć.", "products": {"list": {"noRecordsMessage": "W tej kolekcji nie ma żadnych produktów."}, "add": {"successToast_one": "Produkt został pomyślnie dodany do kolekcji.", "successToast_few": "Produkty zostały pomyślnie dodane do kolekcji.", "successToast_many": "Produktów zostało pomyślnie dodanych do kolekcji.", "successToast_other": "Produktów zostało pomyślnie dodanych do kolekcji."}, "remove": {"successToast_one": "Produkt został pomyślnie usunięty z kolekcji.", "successToast_few": "Produkty został pomyślnie usunięte z kolekcji.", "successToast_many": "Produktów zostało pomyślnie usuniętych z kolekcji.", "successToast_other": "Produktów zostało pomyślnie usuniętych z kolekcji."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Organizuj produkty w kategorie i zarządzaj ich rankingiem oraz hierarchią.", "create": {"header": "Ut<PERSON><PERSON><PERSON> ka<PERSON>", "hint": "Utwórz nową kategorię, aby uporządkować swoje produkty.", "tabs": {"details": "Szczegóły", "organize": "Organizuj ranking"}, "successToast": "Kategoria {{name}} została pomyślnie utworzona."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, a<PERSON> zak<PERSON><PERSON><PERSON><PERSON>ć jej szczegóły.", "successToast": "Kategoria została pomyślnie zaktualizowana."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> kategorię {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "Kategoria {{name}} została pomyślnie usunięta."}, "products": {"add": {"disabledTooltip": "Produkt jest już w tej kategorii.", "successToast_one": "Dodano {{count}} produkt do kategorii.", "successToast_few": "Dodano {{count}} produkty do kategorii.", "successToast_many": "Dodano {{count}} produktów do kategorii.", "successToast_other": "Dodano {{count}} produktów do kategorii."}, "remove": {"confirmation_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produkt z kategorii. Tej akcji nie można cofn<PERSON>ć.", "confirmation_few": "<PERSON>ami<PERSON><PERSON><PERSON> {{count}} produkty z kategorii. Tej akcji nie można cofn<PERSON>ć.", "confirmation_many": "Zamier<PERSON><PERSON>unąć {{count}} produktów z kategorii. Tej akcji nie można cofnąć.", "confirmation_other": "Zamier<PERSON><PERSON>unąć {{count}} produktów z kategorii. Tej akcji nie można cofnąć.", "successToast_one": "Us<PERSON>ęto {{count}} produkt z kategorii.", "successToast_few": "Us<PERSON>ęto {{count}} produkty z kategorii.", "successToast_many": "Usunięto {{count}} produktów z kategorii.", "successToast_other": "Usunięto {{count}} produktów z kategorii."}, "list": {"noRecordsMessage": "W tej kategorii nie ma żadnych produktów."}}, "organize": {"header": "Organizuj", "action": "<PERSON><PERSON><PERSON><PERSON> ranking"}, "fields": {"visibility": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "internal": "Wewnętrzna", "public": "Publiczna"}, "status": {"label": "Status", "active": "Aktywna", "inactive": "Nieaktywna"}, "path": {"label": "Ścieżka", "tooltip": "Pokaż pełną ścieżkę kategorii."}, "children": {"label": "Podkategorie"}, "new": {"label": "Nowa"}}}, "inventory": {"domain": "Magazyn", "subtitle": "Zarządzaj elementami magazynowymi", "reserved": "Zarezerwowane", "available": "Dostępne", "locationLevels": "Lokalizacje", "associatedVariants": "Powiązane warianty", "manageLocations": "Zarządzaj lokalizacjami", "deleteWarning": "Zamierzasz usunąć element magazynowy. Tej akcji nie można cofnąć.", "editItemDetails": "Edytuj szczegóły elementu", "create": {"title": "Utwórz element magazynowy", "details": "Szczegóły", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locations": "Lokalizacje", "attributes": "Atrybuty", "requiresShipping": "Wymaga wysyłki", "requiresShippingHint": "Czy element magazynowy wymaga wysyłki?", "successToast": "Element magazynowy został pomyślnie utworzony."}, "reservation": {"header": "Rezerwacja {{itemName}}", "editItemDetails": "<PERSON><PERSON><PERSON><PERSON>", "lineItemId": "ID pozycji", "orderID": "ID zamówienia", "description": "Opis", "location": "Lokalizacja", "inStockAtLocation": "Na stanie w tej lokalizacji", "availableAtLocation": "Dostępne w tej lokalizacji", "reservedAtLocation": "Zarezerwowane w tej lokalizacji", "reservedAmount": "Ilość do zarezerwowania", "create": "Utwórz <PERSON>", "itemToReserve": "Element do zarezerwowania", "quantityPlaceholder": "Ile chcesz zarezerwować?", "descriptionPlaceholder": "<PERSON><PERSON> re<PERSON>?", "successToast": "Rezerwacja została pomyślnie utworzona.", "updateSuccessToast": "Rezerwacja została pomyślnie zaktualizowana.", "deleteSuccessToast": "Rezerwacja została pomyślnie usunięta.", "errors": {"noAvaliableQuantity": "Lokalizacja magazynowa nie ma dostępnej ilości.", "quantityOutOfRange": "<PERSON><PERSON><PERSON> ilość to 1, a maks<PERSON><PERSON><PERSON> ilość to {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "Nie można ustawić stanu magazynowego na wartość niższą niż zarezerwowana ilość ({{quantity}})."}}, "toast": {"updateLocations": "Lokalizacje zaktualizowane pomyślnie.", "updateLevel": "Poziom magazynowy zaktualizowany pomyślnie.", "updateItem": "Element magazynowy zaktualizowany pomyślnie."}, "stock": {"title": "Aktualizuj stany magazynowe", "description": "Zaktualizuj stan magazynowy dla wybranych pozycji.", "action": "Ed<PERSON><PERSON>j stany magazynowe", "placeholder": "Nieaktywne", "disablePrompt_one": "Zamierzas<PERSON> w<PERSON>łączyć {{count}} stan ma<PERSON>. Tej operacji nie można cofnąć.", "disablePrompt_few": "Zamier<PERSON><PERSON> w<PERSON>łączyć {{count}} stany magazynowe. Tej operacji nie można cofnąć.", "disablePrompt_many": "Zamierzas<PERSON> wyłączyć {{count}} stanów magazynowych. Tej operacji nie można cofnąć.", "disablePrompt_other": "Zamierzas<PERSON> wyłączyć {{count}} stanów magazynowych. Tej operacji nie można cofnąć.", "disabledToggleTooltip": "Nie można wyłączyć: najpierw wyzeruj przychodzące i/lub zarezerwowane ilości.", "successToast": "Stany magazynowe zostały pomyślnie zaktualizowane."}}, "giftCards": {"domain": "<PERSON><PERSON><PERSON>", "editGiftCard": "Edyt<PERSON>j kart<PERSON> podarunkową", "createGiftCard": "Utwórz kartę podarunkową", "createGiftCardHint": "Ręcznie utwórz kartę podarunkową, którą można wykorzystać jako metodę pła<PERSON> w <PERSON>im skle<PERSON>.", "selectRegionFirst": "Najpierw wybierz region", "deleteGiftCardWarning": "<PERSON>ami<PERSON><PERSON><PERSON>un<PERSON>ć kartę podarunkową {{code}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "balanceHigherThanValue": "Saldo nie może być wyższe niż pierwotna kwota.", "balanceLowerThanZero": "<PERSON>do nie może być uje<PERSON>.", "expiryDateHint": "Kraje mają różne przepisy dotyczące dat ważności kart podarunkowych. <PERSON><PERSON><PERSON>j si<PERSON>, że sprawdziłeś lokalne przepisy przed ustawieniem daty ważności.", "regionHint": "Zmiana regionu karty podarunkowej zmieni również jej walutę, co może wpłynąć na jej wartość pieniężną.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON>, czy karta podarunkowa jest włączona czy wyłączona.", "balance": "<PERSON><PERSON>", "currentBalance": "Aktualne saldo", "initialBalance": "Początkowe saldo", "personalMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "recipient": "Odbiorca"}, "customers": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Twoi klienci pojawią się tutaj."}, "create": {"header": "Utwórz <PERSON>", "hint": "Utwórz nowego klienta i zarządzaj jego danymi.", "successToast": "Klient {{email}} został pomyślnie utworzony."}, "groups": {"label": "Grupy klientów", "remove": "Czy na pewno chcesz usunąć klienta z grupy \"{{name}}\"?", "removeMany": "Czy na pewno chcesz usunąć klienta z następujących grup: {{groups}}?", "alreadyAddedTooltip": "K<PERSON> jest już w tej grupie.", "list": {"noRecordsMessage": "Ten klient nie należy do żadnej grupy."}, "add": {"success": "Klient dodany do: {{groups}}.", "list": {"noRecordsMessage": "Najpierw utwórz grupę klientów."}}, "removed": {"success": "Klient usunięty z: {{groups}}.", "list": {"noRecordsMessage": "Najpierw utwórz grupę klientów."}}}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "emailDisabledTooltip": "Adres e-mail nie może być zmieniony dla zarejestrowanych klientów.", "successToast": "Klient {{email}} został pomyślnie zaktualizowany."}, "delete": {"title": "<PERSON><PERSON><PERSON> klienta", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> klienta {{email}}. <PERSON>j akcji nie można cof<PERSON>.", "successToast": "Klient {{email}} został pomyślnie usunięty."}, "fields": {"guest": "<PERSON><PERSON><PERSON>", "registered": "Zarejestrowany", "groups": "Grupy"}, "registered": "Zarejestrowany", "guest": "<PERSON><PERSON><PERSON>", "hasAccount": "Posiada konto"}, "customerGroups": {"domain": "Grupy klientów", "subtitle": "Organizuj klientów w grupy. Grupy mogą mieć różne promocje i ceny.", "list": {"empty": {"heading": "Brak grup klientów", "description": "Nie ma żadnych grup klientów do wyświetlenia."}, "filtered": {"heading": "Brak wyników", "description": "Żadne grupy klientów nie pasują do bieżących kryteriów filtrowania."}}, "create": {"header": "Utwórz grupę klientów", "hint": "Utwórz nową grupę klientów, aby segmentować swoich klientów.", "successToast": "Grupa klientów {{name}} została pomyślnie utworzona."}, "edit": {"header": "Edytuj grupę k<PERSON>ów", "successToast": "Grupa klientów {{name}} została pomyślnie zaktualizowana."}, "delete": {"title": "Usuń grupę klientów", "description": "Zamier<PERSON><PERSON>unąć grupę k<PERSON>ów {{name}}. <PERSON>j akcji nie można cofn<PERSON>.", "successToast": "Grupa klientów {{name}} została pomyślnie usunięta."}, "customers": {"alreadyAddedTooltip": "Klient został już dodany do grupy.", "add": {"successToast_one": "Klient został pomyślnie dodany do grupy.", "successToast_few": "Klienci zostali pomyślnie dodani do grupy.", "successToast_many": "Klienci zostali pomyślnie dodani do grupy.", "successToast_other": "Klienci zostali pomyślnie dodani do grupy.", "list": {"noRecordsMessage": "Najpierw utwórz klienta."}}, "remove": {"title_one": "Usuń klienta z grupy", "title_few": "Usuń klientów z grupy", "title_many": "Usuń klientów z grupy", "title_other": "Usuń klientów z grupy", "description_one": "<PERSON>amier<PERSON><PERSON> {{count}} klienta z grupy klientów. Tej akcji nie można cofnąć.", "description_few": "Zamier<PERSON><PERSON>unąć {{count}} klientów z grupy klientów. Tej akcji nie można cofnąć.", "description_many": "Zamier<PERSON><PERSON>unąć {{count}} klientów z grupy klientów. Tej akcji nie można cofnąć.", "description_other": "Zamier<PERSON><PERSON>unąć {{count}} klientów z grupy klientów. Tej akcji nie można cofnąć."}, "list": {"noRecordsMessage": "Ta grupa nie ma klientów."}}}, "orders": {"domain": "Zamówienia", "claim": "Rekla<PERSON><PERSON>ja", "exchange": "<PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON>", "cancelWarning": "<PERSON><PERSON><PERSON> an<PERSON> zamówienie {{id}}. <PERSON><PERSON> akcji nie można co<PERSON>.", "orderCanceled": "Zamówienie zostało pomyślnie anulowane", "onDateFromSalesChannel": "{{date}} z {{salesChannel}}", "list": {"noRecordsMessage": "Twoje zamówienia pojawią się tutaj."}, "status": {"not_paid": "Nieopłacone", "pending": "Oczek<PERSON><PERSON><PERSON><PERSON>", "completed": "Zrealizowane", "draft": "Szkic", "archived": "Zarchiwizowane", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requires_action": "Wymaga działania"}, "summary": {"requestReturn": "Zł<PERSON>ż wniosek o zwrot", "allocateItems": "Przydziel produkty", "editOrder": "Edyt<PERSON>j zamówienie", "editOrderContinue": "Kontynuuj edycję zamówienia", "inventoryKit": "Składa się z {{count}}x produktów magazynowych", "itemTotal": "Suma produktów", "shippingTotal": "Suma w<PERSON>yłki", "discountTotal": "<PERSON><PERSON>", "taxTotalIncl": "<PERSON><PERSON> (wliczono)", "itemSubtotal": "Suma produktów", "shippingSubtotal": "Suma w<PERSON>yłki", "discountSubtotal": "<PERSON><PERSON>", "taxTotal": "<PERSON><PERSON> pod<PERSON>ku"}, "transfer": {"title": "Przeka<PERSON> włas<PERSON>ść", "requestSuccess": "Wniosek o przekazanie zamówienia do: {{email}}.", "currentOwner": "Obecny właściciel", "newOwner": "Nowy właściciel", "currentOwnerDescription": "Klient obecnie związany z tym zamówieniem.", "newOwnerDescription": "Klient, do którego chcesz przekazać to zamówienie."}, "payment": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isReadyToBeCaptured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <0/> jest gotowa do realizacji.", "totalPaidByCustomer": "Łączna kwota zapłacona przez klienta", "capture": "Zrealizuj <PERSON>", "capture_short": "Zrealizuj", "refund": "<PERSON><PERSON><PERSON>", "markAsPaid": "Oznacz jako <PERSON>", "statusLabel": "Status płatności", "statusTitle": "Status płatności", "status": {"notPaid": "Nieopłacone", "authorized": "Autoryzowane", "partiallyAuthorized": "Częściowo autoryzowane", "awaiting": "Oczek<PERSON><PERSON><PERSON><PERSON>", "captured": "Zrealizowane", "partiallyRefunded": "Częściowo zwrócone", "partiallyCaptured": "Częściowo zrealizowane", "refunded": "Zwrócone", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Wymaga działania"}, "capturePayment": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w wys<PERSON>ści {{amount}} zostanie zrealizowana.", "capturePaymentSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w wysokości {{amount}} została pomyślnie zrealizowana", "markAsPaidPayment": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć w wysokości {{amount}} zostanie oznaczona jako <PERSON>.", "markAsPaidPaymentSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w wysokości {{amount}} została pomyślnie oznaczona jako opłacona", "createRefund": "Utwórz zwrot", "refundPaymentSuccess": "Zwrot kwoty {{amount}} zakończony pomyślnie", "createRefundWrongQuantity": "<PERSON><PERSON><PERSON><PERSON> pow<PERSON>a by<PERSON> liczbą od 1 do {{number}}", "refundAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ amount }}", "paymentLink": "Kopiuj link do płatności dla {{ amount }}", "selectPaymentToRefund": "Wybierz płatność do zwrotu"}, "edits": {"title": "Edyt<PERSON>j zamówienie", "confirm": "Potwierdź edycję", "confirmText": "<PERSON><PERSON> edycję zamówienia. Tej akcji nie można cofnąć.", "cancel": "<PERSON><PERSON><PERSON>", "currentItems": "Aktualne produkty", "currentItemsDescription": "<PERSON><PERSON><PERSON><PERSON> produktu lub usuń.", "addItemsDescription": "Mo<PERSON><PERSON>z do<PERSON>ć nowe produkty do zamówienia.", "addItems": "<PERSON><PERSON>j produkty", "amountPaid": "Kwota opłacona", "newTotal": "Nowa suma", "differenceDue": "Różnica do zapłaty", "create": "Edyt<PERSON>j zamówienie", "currentTotal": "Aktualna suma", "noteHint": "Dodaj wewnętrzną notatkę do edycji", "cancelSuccessToast": "Edycja zamówienia anulowana", "createSuccessToast": "Złożono wniosek o edycję zamówienia", "activeChangeError": "Na zamówieniu jest już aktywna zmiana (zwrot, reklamacja, wymiana itp.). Zakończ lub anuluj zmianę przed edytowaniem zamówienia.", "panel": {"title": "Złożono wniosek o edycję zamówienia", "titlePending": "Edycja zamówienia oczekująca"}, "toast": {"canceledSuccessfully": "Edycja zamówienia anulowana", "confirmedSuccessfully": "Edycja zamówienia potwierdzona"}, "validation": {"quantityLowerThanFulfillment": "<PERSON>e można ustawić ilości mniejszej lub równej ilości zrealizowanej"}}, "edit": {"email": {"title": "Edyt<PERSON>j adres e-mail", "requestSuccess": "Adres e-mail zamówienia został zmieniony na {{email}}."}, "shippingAddress": {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "requestSuccess": "Adres dostawy zamówienia został zaktualizowany."}, "billingAddress": {"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "requestSuccess": "<PERSON>res rozliczeniowy zamówienia został zaktualizowany."}}, "returns": {"create": "Utwórz zwrot", "confirm": "Potwierdź zwrot", "confirmText": "<PERSON><PERSON>wi<PERSON> zwrot. Tej akcji nie można cofną<PERSON>.", "inbound": "Przychodzący", "outbound": "Wychodzący", "sendNotification": "Wyślij powiadomienie", "sendNotificationHint": "Powiadom klienta o zwrocie.", "returnTotal": "<PERSON><PERSON>", "inboundTotal": "Suma przychodz<PERSON>", "refundAmount": "Kwota zwrotu", "outstandingAmount": "Pozostało do zapłaty", "reason": "<PERSON><PERSON><PERSON><PERSON>", "reasonHint": "<PERSON><PERSON><PERSON><PERSON>, dla którego klient chce zwrócić produkty.", "note": "Notatka", "noInventoryLevel": "Brak poziomu z<PERSON>ów", "noInventoryLevelDesc": "Wybrana lokalizacja nie ma poziomu zapasów dla wybranych produktów. Wniosek o zwrot można zł<PERSON>, ale nie można go przyj<PERSON>ć, dopóki nie zostanie utworzony poziom zapasów dla wybranej lokalizacji.", "noteHint": "<PERSON><PERSON><PERSON><PERSON> wpisać dowolny tekst, je<PERSON><PERSON> ch<PERSON>z coś sprecyzo<PERSON>.", "location": "Lokalizacja", "locationHint": "<PERSON><PERSON><PERSON><PERSON>, do której chcesz zwrócić produkty.", "inboundShipping": "Wysyłka zwrotna", "inboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, której chcesz użyć.", "returnableQuantityLabel": "Ilość do zwrotu", "refundableAmountLabel": "Kwota do zwrotu", "returnRequestedInfo": "Złożono wniosek o zwrot {{requestedItemsCount}}x produktów", "returnReceivedInfo": "Otrzymano {{requestedItemsCount}}x produktów zwrotu", "itemReceived": "Produkty otrzymane", "returnRequested": "Zwrot zamówiony", "damagedItemReceived": "Otr<PERSON>mano uszkodzone produkty", "damagedItemsReturned": "Zwrócono {{quantity}}x uszkodzonych produktów", "activeChangeError": "Na tym zamówieniu jest aktywna zmiana. Zakończ lub anuluj poprzednią zmianę.", "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Czy na pewno chcesz anulować wniosek o zwrot?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nie znaleziono opcji wysyłki zwrotnej", "hint": "Nie utworzono żadnych opcji wysyłki zwrotnej dla tej lokalizacji. Możesz utworzyć jedną w <LinkComponent>Lokalizacja i wysyłka</LinkComponent>."}, "outboundShippingOptions": {"title": "Nie znaleziono opcji wysyłki wychodzącej", "hint": "Nie utworzono żadnych opcji wysyłki wychodzącej dla tej lokalizacji. Możesz utworzyć jedną w <LinkComponent>Lokalizacja i wysyłka</LinkComponent>."}}, "receive": {"action": "Odbierz produkty", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "Uzupełnij wszystkie produkty", "itemsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> produkty", "title": "Odbierz produkty dla #{{returnId}}", "sendNotificationHint": "Powiadom klienta o otrzymanym zwrocie.", "inventoryWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że automatycznie dostosujemy poziomy zapasów na podstawie powyższych danych.", "writeOffInputLabel": "Ile produktów jest uszkodzonych?", "toast": {"success": "Zwrot został pomyślnie odebrany.", "errorLargeValue": "<PERSON><PERSON><PERSON><PERSON> jest większa niż ilość zamówionych produktów.", "errorNegativeValue": "<PERSON><PERSON><PERSON><PERSON> nie może być wartością ujemną.", "errorLargeDamagedValue": "<PERSON><PERSON><PERSON>ć uszkodzonych produktów + ilo<PERSON><PERSON> otrzymanych nieuszkodzonych produktów przekracza całkowitą ilość produktów na zwrocie. Zmniejsz ilość nieuszkodzonych produktów."}}, "toast": {"canceledSuccessfully": "Zwrot został pomyślnie anulowany", "confirmedSuccessfully": "Zwrot został pomyślnie potwierdzony"}, "panel": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> zwrot", "description": "Istnieje otwarty wniosek o zwrot do realizacji"}}, "claims": {"create": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm": "Potwierdź reklamację", "confirmText": "<PERSON><PERSON> reklamację. Tej akcji nie można co<PERSON>n<PERSON>.", "manage": "Zarządzaj reklamacją", "outbound": "Wychodzący", "outboundItemAdded": "{{itemsCount}}x do<PERSON>o poprz<PERSON>", "outboundTotal": "<PERSON><PERSON> w<PERSON>", "outboundShipping": "Wysyłka wychodząca", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, której chcesz użyć.", "refundAmount": "Oszacowana różnica", "activeChangeError": "Na tym zamówieniu jest aktywna zmiana. Zakończ lub anuluj poprzednią zmianę.", "actions": {"cancelClaim": {"successToast": "Reklamacja została pomyślnie anulowana."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>zy na pewno chcesz anulować reklamację?"}, "tooltips": {"onlyReturnShippingOptions": "Ta lista będzie zawierać tylko opcje wysyłki zwrotnej."}, "toast": {"canceledSuccessfully": "Reklamacja została pomyślnie anulowana", "confirmedSuccessfully": "Reklamacja została pomyślnie potwierdzona"}, "panel": {"title": "Inicjowana reklamacja", "description": "Istnieje otwarty wniosek o reklamację do realizacji"}}, "exchanges": {"create": "Utw<PERSON>rz wym<PERSON>", "manage": "Zarządzaj wymianą", "confirm": "Potwierdź wymianę", "confirmText": "<PERSON><PERSON> wym<PERSON>ę. Tej akcji nie można cofn<PERSON>.", "outbound": "Wychodzący", "outboundItemAdded": "{{itemsCount}}x do<PERSON>o pop<PERSON> wym<PERSON>", "outboundTotal": "<PERSON><PERSON> w<PERSON>", "outboundShipping": "Wysyłka wychodząca", "outboundShippingHint": "<PERSON><PERSON><PERSON><PERSON>, której chcesz użyć.", "refundAmount": "Oszacowana różnica", "activeChangeError": "Na tym zamówieniu jest aktywna zmiana. Zakończ lub anuluj poprzednią zmianę.", "actions": {"cancelExchange": {"successToast": "Wymiana została pomyślnie anulowana."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>zy na pewno chcesz anulować wymianę?"}, "tooltips": {"onlyReturnShippingOptions": "Ta lista będzie zawierać tylko opcje wysyłki zwrotnej."}, "toast": {"canceledSuccessfully": "Wymiana została pomyślnie anulowana", "confirmedSuccessfully": "Wymiana została pomyślnie potwierdzona"}, "panel": {"title": "Inicjowana wymiana", "description": "Istnieje otwarty wniosek o wymianę do realizacji"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notAllocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "allocateItems": {"action": "Przydziel produkty", "title": "Przydziel produkty zamówienia", "locationDescription": "<PERSON><PERSON><PERSON><PERSON>, z której chcesz przydzielić.", "itemsToAllocate": "Produkty do przydzielenia", "itemsToAllocateDesc": "<PERSON><PERSON>bierz liczbę produktów, które chcesz przydzielić", "search": "Szukaj produktów", "consistsOf": "Składa się z {{num}}x produktów magazynowych", "requires": "<PERSON><PERSON><PERSON> {{num}} na wariant", "toast": {"created": "Produkty zostały pomyślnie przydzielone"}, "error": {"quantityNotAllocated": "Istnieją nieprzydzielone produkty."}}, "shipment": {"title": "Oznacz realizację jako wysłaną", "trackingNumber": "Numer do śledzenia przesyłki", "addTracking": "Dodaj numer do śledzenia przesyłki", "sendNotification": "Wyślij powiadomienie", "sendNotificationHint": "Powiadom klienta o tej wysyłce.", "toastCreated": "Wysyłka została pomyślnie utworzona."}, "fulfillment": {"cancelWarning": "<PERSON><PERSON> an<PERSON> realizację. Tej akcji nie można cofn<PERSON>.", "markAsDeliveredWarning": "<PERSON><PERSON><PERSON> realizację jako dos<PERSON>. Tej akcji nie można cofnąć.", "differentOptionSelected": "Wybrana opcja dostawy różni się od tej wybranej przez klienta.", "disabledItemTooltip": "Wybrana opcja dostawy nie pozwala na realizację tego przedmiotu", "unfulfilledItems": "Niezrealizowane produkty", "statusLabel": "Status realizacji", "statusTitle": "Status realizacji", "fulfillItems": "Zrealizuj produkty", "awaitingFulfillmentBadge": "Oczekująca na realizację", "requiresShipping": "Wymaga wysyłki", "number": "Realizacja #{{number}}", "itemsToFulfill": "Produkty do realizacji", "create": "Utwórz realizację", "available": "Dostępne", "inStock": "W magazynie", "markAsShipped": "Oznacz jako wysłaną", "markAsPickedUp": "Oznacz jako odebrane", "markAsDelivered": "Oznacz jako <PERSON>", "itemsToFulfillDesc": "Wybierz produkty i ilości do realizacji", "locationDescription": "<PERSON><PERSON><PERSON><PERSON>, z której chcesz zrealizować produkty.", "sendNotificationHint": "Powiadom klientów o utworzonej realizacji.", "methodDescription": "<PERSON><PERSON><PERSON>rz inną metodę wysyłki niż wybrana przez klienta", "error": {"wrongQuantity": "<PERSON><PERSON><PERSON> jeden produkt jest dostępny do realizacji", "wrongQuantity_other": "<PERSON><PERSON><PERSON><PERSON> pow<PERSON>a by<PERSON> liczbą od 1 do {{number}}", "noItems": "Brak produktów do realizacji.", "noShippingOption": "<PERSON><PERSON><PERSON> dos<PERSON>wy jest wymagana", "noLocation": "Lokalizacja jest wymagana"}, "status": {"notFulfilled": "Niezrealizowane", "partiallyFulfilled": "Częściowo zrealizowane", "fulfilled": "Zrealizowane", "partiallyShipped": "Częściowo wysłane", "shipped": "<PERSON><PERSON>ła<PERSON>", "delivered": "Dostarczone", "partiallyDelivered": "Częściowo dostarczone", "partiallyReturned": "Częściowo zwrócone", "returned": "Zwrócone", "canceled": "<PERSON><PERSON><PERSON><PERSON>", "requiresAction": "Wymaga działania"}, "toast": {"created": "Realizacja została pomyślnie utworzona", "canceled": "Realizacja została pomyślnie anulowana", "fulfillmentShipped": "<PERSON>e można anulować już wysłanej realizacji", "fulfillmentDelivered": "Realizacja została pomyślnie oznaczona jako dostar<PERSON>ona", "fulfillmentPickedUp": "Realizacja została pomyślnie oznaczona jako odebrana"}, "trackingLabel": "Śledzenie przesyłki", "shippingFromLabel": "Wysyłka z", "itemsLabel": "Produkty"}, "refund": {"title": "Utwórz zwrot", "sendNotificationHint": "Powiadom klientów o utworzonym zwrocie.", "systemPayment": "Płat<PERSON>ść systemowa", "systemPaymentDesc": "<PERSON><PERSON> lub wi<PERSON><PERSON><PERSON> p<PERSON> to płatność systemowa. <PERSON><PERSON>ę<PERSON>j, że realizacje i zwroty nie są obsługiwane przez Medusę dla takich płatności.", "error": {"amountToLarge": "Nie można zwrócić więcej niż oryginalną kwotę zamówienia.", "amountNegative": "Kwota zwrotu musi być liczbą dodatnią.", "reasonRequired": "Wybierz przyczynę zwrotu."}}, "customer": {"contactLabel": "Kontakt", "editEmail": "Edytuj email", "transferOwnership": "Przeka<PERSON> włas<PERSON>ść", "editBillingAddress": "<PERSON><PERSON><PERSON><PERSON> adres billingowy", "editShippingAddress": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> w<PERSON>łki"}, "activity": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showMoreActivities_one": "<PERSON><PERSON><PERSON> więcej ({{count}} aktywność)", "showMoreActivities_few": "<PERSON><PERSON><PERSON> więcej ({{count}} aktywności)", "showMoreActivities_many": "<PERSON><PERSON><PERSON> więcej ({{count}} aktywności)", "showMoreActivities_other": "<PERSON><PERSON><PERSON> więcej ({{count}} aktywności)", "comment": {"label": "Komentarz", "placeholder": "<PERSON><PERSON><PERSON>", "addButtonText": "<PERSON><PERSON><PERSON>", "deleteButtonText": "Us<PERSON>ń komentarz"}, "from": "Z", "to": "Do", "events": {"common": {"toReturn": "Do zwrotu", "toSend": "Do wysłania"}, "placed": {"title": "Zamówienie złożone", "fromSalesChannel": "z {{salesChannel}}"}, "canceled": {"title": "Zamów<PERSON><PERSON>"}, "payment": {"awaiting": "Oczekująca na płatność", "captured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zrealizowana", "canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anulowana", "refunded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zwrócona"}, "fulfillment": {"created": "Produkty zrealizowane", "canceled": "Realizacja <PERSON>", "shipped": "Produkty wysłane", "delivered": "Produkty dostarczone", "items_one": "{{count}} produkt", "items_few": "{{count}} produkty", "items_many": "{{count}} produktów", "items_other": "{{count}} produktów"}, "return": {"created": "Zwrot #{{returnId}} zamówiony", "canceled": "Zwrot #{{returnId}} anulowany", "received": "Zwrot #{{returnId}} otrzymany", "items_one": "{{count}} produkt zwrócony", "items_few": "{{count}} produkty zwrócone", "items_many": "{{count}} produktów zwróconych", "items_other": "{{count}} produktów zwróconych"}, "note": {"comment": "Komentarz", "byLine": "przez {{author}}"}, "claim": {"created": "Reklamacja #{{claimId}} złożona", "canceled": "Reklamacja #{{claimId}} anulowana", "itemsInbound": "{{count}} produkt do zwrotu", "itemsOutbound": "{{count}} produkt do wysłania"}, "exchange": {"created": "Wymiana #{{exchangeId}} złożona", "canceled": "Wym<PERSON> #{{exchangeId}} anulowana", "itemsInbound": "{{count}} produkt do zwrotu", "itemsOutbound": "{{count}} produkt do wysłania"}, "edit": {"requested": "Edycja zamówienia #{{editId}} złożona", "confirmed": "Edycja zamówienia #{{editId}} potwierdzona"}, "transfer": {"requested": "Przekazanie zamówienia #{{transferId}} zostało zgłoszone", "confirmed": "Przekazanie zamówienia #{{transferId}} zostało potwierdzone", "declined": "Przekazanie zamówienia #{{transferId}} zostało odrzucone"}, "update_order": {"shipping_address": "Adres dostawy został pomyślnie zaktualizowany", "billing_address": "<PERSON><PERSON> roz<PERSON>zeniowy został pomyślnie zaktualizowany", "email": "Adres e-mail został pomyślnie zaktualizowany"}}}, "fields": {"displayId": "Indeks zamówienia", "refundableAmount": "Kwota do zwrotu", "returnableQuantity": "Ilość do zwrotu"}}, "draftOrders": {"domain": "Wstępne zamówienia", "deleteWarning": "<PERSON><PERSON><PERSON> wstępne zamówienie {{id}}. <PERSON>j akcji nie można co<PERSON>.", "paymentLinkLabel": "Link do płatności", "cartIdLabel": "ID koszyka", "markAsPaid": {"label": "Oznacz jako <PERSON>", "warningTitle": "Oznacz jako <PERSON>", "warningDescription": "<PERSON><PERSON><PERSON> wstępne zamówienie jako op<PERSON>cone. <PERSON>j akcji nie można co<PERSON>, a późniejsze pobranie płatności nie będzie możliwe."}, "status": {"open": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Zakończone"}, "create": {"createDraftOrder": "Utwórz wstępne zamówienie", "createDraftOrderHint": "Utwórz nowe wstępne zamówienie, aby z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> szczegółami zamówienia przed jego złożeniem.", "chooseRegionHint": "Wybierz region", "existingItemsLabel": "Istniejące produkty", "existingItemsHint": "Dodaj istniejące produkty do wstępnego zamówienia.", "customItemsLabel": "Niestandardowe produkty", "customItemsHint": "Dodaj niestandardowe produkty do wstępnego zamówienia.", "addExistingItemsAction": "Dodaj is<PERSON>niejące produkty", "addCustomItemAction": "<PERSON><PERSON><PERSON>rdowy produkt", "noCustomItemsAddedLabel": "<PERSON>e dodano jeszcze żadnych niestandardowych produktów", "noExistingItemsAddedLabel": "<PERSON>e dodano jeszcze żadnych istniejących produktów", "chooseRegionTooltip": "Najpierw wybierz region", "useExistingCustomerLabel": "Użyj istniejącego klienta", "addShippingMethodsAction": "<PERSON><PERSON><PERSON> metody w<PERSON>", "unitPriceOverrideLabel": "Nadpisz cenę jednostkową", "shippingOptionLabel": "Opcja wysyłki", "shippingOptionHint": "<PERSON><PERSON><PERSON><PERSON> opcję wysyłki dla wstępnego zamówienia.", "shippingPriceOverrideLabel": "Nadpisz cenę wysyłki", "shippingPriceOverrideHint": "Nadpisz cenę wysyłki dla wstępnego zamówienia.", "sendNotificationLabel": "Wyślij powiadomienie", "sendNotificationHint": "Wyślij powiadomienie do klienta po utworzeniu wstępnego zamówienia."}, "validation": {"requiredEmailOrCustomer": "W<PERSON><PERSON>y jest adres e-mail lub klient.", "requiredItems": "W<PERSON><PERSON>y jest co najmniej jeden produkt.", "invalidEmail": "Adres e-mail musi być poprawnym adresem e-mail."}}, "stockLocations": {"domain": "Lokalizacje i wysyłka", "list": {"description": "Zarządzaj lokalizacjami magazynowymi i opcjami wysyłki swojego sklepu."}, "create": {"header": "Utwórz lokalizację magazynową", "hint": "Lokalizacja magazynowa to fizyczne miejsce, w którym produkty są przechowywane i wysyłane.", "successToast": "Lokalizacja {{name}} została pomyślnie utworzona."}, "edit": {"header": "Edytuj lokalizację magazynową", "viewInventory": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Lokalizacja {{name}} została pomyślnie zaktualizowana."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> usun<PERSON>ć lokalizację magazynową {{name}}. <PERSON>j akcji nie można co<PERSON>."}, "fulfillmentProviders": {"header": "Dostawcy realizacji", "shippingOptionsTooltip": "Ten rozwijany wykaz będzie zawierał tylko dostawców włączonych dla tej lokalizacji. Dodaj je do lokalizacji, je<PERSON>li rozwijany wykaz jest wyłączony.", "label": "Połączeni dostawcy realizacji", "connectedTo": "Połączono z {{count}} z {{total}} dostawców realizacji", "noProviders": "Ta lokalizacja magazynowa nie jest połączona z żadnymi dostawcami realizacji.", "action": "Połącz dostawców", "successToast": "Dostawcy realizacji dla lokalizacji magazynowej zostali pomyślnie zaktualizowani."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shipping": {"header": "Wysyłka"}, "disable": {"confirmation": "Czy na pewno chcesz wyłączyć {{name}}? Spowoduje to usunięcie wszystkich powiązanych stref serwisowych i opcji wysyłki i nie można tego cofnąć.", "pickup": "Odbiór osobisty został pomyślnie wyłączony.", "shipping": "Wysyłka została pomyślnie wyłączona."}, "enable": {"pickup": "Odbiór osobisty został pomyślnie włączony.", "shipping": "Wysyłka została pomyślnie włączona."}}, "sidebar": {"header": "Konfiguracja wysyłki", "shippingProfiles": {"label": "Profile wysyłki", "description": "Grupuj produkty według wymagań wysyłki"}}, "salesChannels": {"header": "Kanały sprzedaży", "hint": "Zarządzaj kanałami sprzedaży połączonymi z tą lokalizacją.", "label": "Połączone kanały sprzedaży", "connectedTo": "Połączono z {{count}} z {{total}} kanałów sprzedaży", "noChannels": "Lokalizacja nie jest połączona z żadnymi kanałami sprzedaży.", "action": "Połącz kanały sprzedaży", "successToast": "Kanały sprzedaży zostały pomyślnie zaktualizowane."}, "pickupOptions": {"edit": {"header": "Edytuj opcje odbioru osobistego"}}, "shippingOptions": {"create": {"shipping": {"header": "Utwórz opcję wysyłki dla {{zone}}", "hint": "Utwórz nową opcję wysyłki, a<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, w jaki sposób produkty są wysyłane z tej lokalizacji.", "label": "<PERSON><PERSON>je wysyłki", "successToast": "Op<PERSON>ja wysyłki {{name}} została pomyślnie utworzona."}, "pickup": {"header": "Utwórz opcję odbioru osobistego dla {{zone}}", "hint": "Utwórz nową opcję odbioru osobistego, a<PERSON> <PERSON><PERSON><PERSON><PERSON> sposób odbioru produktów z tej lokalizacji.", "label": "Op<PERSON>je odbioru", "successToast": "Opcja odbioru osobistego {{name}} została pomyślnie utworzona."}, "returns": {"header": "Utwórz opcję zwrotu dla {{zone}}", "hint": "Utwórz nową opcję zwrotu, a<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, w jaki sposób produkty są zwracane do tej lokalizacji.", "label": "Opcje zwrotu", "successToast": "Opcja zwrotu {{name}} została pomyślnie utworzona."}, "tabs": {"details": "Szczegóły", "prices": "<PERSON><PERSON>"}, "action": "U<PERSON><PERSON><PERSON><PERSON> op<PERSON>"}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> opcję wysyłki {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "<PERSON><PERSON>ja wysyłki {{name}} została pomyślnie usunięta."}, "edit": {"header": "<PERSON><PERSON><PERSON>j <PERSON>ję wysyłki", "action": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Op<PERSON>ja wysyłki {{name}} została pomyślnie zaktualizowana."}, "pricing": {"action": "<PERSON><PERSON><PERSON><PERSON> ceny"}, "conditionalPrices": {"header": "<PERSON><PERSON> warunkowe dla  {{name}}", "description": "Zarządzaj cenami warunkowymi dla tej opcji dostawy w zależności od łącznej wartości przedmiotów w koszyku.", "attributes": {"cartItemTotal": "Łączna wartość przedmiotów w koszyku"}, "summaries": {"range": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> mie<PERSON><PERSON> się w prz<PERSON><PERSON>le <1>{{gte}}</1> - <2>{{lte}}</2>", "greaterThan": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "<PERSON><PERSON><PERSON> <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON><PERSON>", "manageConditionalPrices": "Zarządzaj cenami warunkowymi"}, "rules": {"amount": "<PERSON><PERSON>", "gte": "Minimalna łączna wartość koszyka", "lte": "Maksymalna łączna wartość koszyka"}, "customRules": {"label": "Niestandardowe reguły", "tooltip": "Ta cena warunkowa zawiera reguły, którymi nie można zarządzać w panelu.", "eq": "Łączna wartość koszyka musi być równa", "gt": "Łączna wartość koszyka musi być większa niż", "lt": "Łączna wartość koszyka musi być mniejsza niż"}, "errors": {"amountRequired": "<PERSON><PERSON> op<PERSON> dos<PERSON>wy jest wymagana", "minOrMaxRequired": "Należy podać co najmniej minimalną lub maksymalną wartość koszyka", "minGreaterThanMax": "Minimalna wartość koszyka musi być mniejsza lub równa maksymalnej wartości koszyka", "duplicateAmount": "Cena opcji dostawy musi być unikalna dla każdej warunkowej reguły", "overlappingConditions": "Reguły cenowe nie mogą się powielać"}}, "fields": {"count": {"shipping_one": "{{count}} opc<PERSON> w<PERSON>yłki", "shipping_few": "{{count}} opc<PERSON> w<PERSON>łki", "shipping_many": "{{count}} opcji wysyłki", "shipping_other": "{{count}} opcji wysyłki", "pickup_one": "{{count}} op<PERSON><PERSON> o<PERSON><PERSON><PERSON>", "pickup_few": "{{count}} opc<PERSON> od<PERSON><PERSON>", "pickup_many": "{{count}} opc<PERSON> od<PERSON><PERSON>", "pickup_other": "{{count}} opc<PERSON> od<PERSON><PERSON>", "returns_one": "{{count}} opc<PERSON> zwrotu", "returns_few": "{{count}} opc<PERSON> zwrotu", "returns_many": "{{count}} opc<PERSON> zwrotu", "returns_other": "{{count}} opc<PERSON> zwrotu"}, "priceType": {"label": "<PERSON><PERSON> ceny", "options": {"fixed": {"label": "Stała", "hint": "Cena opcji wysyłki jest stała i nie zmienia się w zależności od zawartości zamówienia."}, "calculated": {"label": "O<PERSON>liczon<PERSON>", "hint": "Cena opcji wysyłki jest obliczana przez dostawcę realizacji podczas procesu płatności."}}}, "enableInStore": {"label": "Włącz w sklepie", "hint": "<PERSON><PERSON> klienci mogą używać tej opcji podczas płatności."}, "provider": "Dostawca realizacji", "profile": "<PERSON><PERSON>", "fulfillmentOption": "Opcja realizacji"}}, "serviceZones": {"create": {"headerPickup": "Utwórz strefę serwisową dla odbioru osobistego z {{location}}", "headerShipping": "Utwórz strefę serwisową dla wysyłki z {{location}}", "action": "Utwórz strefę serwisową", "successToast": "Strefa serwisowa {{name}} została pomyślnie utworzona."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> strefę serwisową", "successToast": "Strefa serwisowa {{name}} została pomyślnie zaktualizowana."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> strefę serwisową {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "Strefa serwisowa {{name}} została pomyślnie usunięta."}, "manageAreas": {"header": "Zarządzaj obszarami dla {{name}}", "action": "Zarządzaj obszarami", "label": "O<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON>rz obszary geograficzne, które obejmuje strefa serwisowa.", "successToast": "<PERSON><PERSON><PERSON>y dla {{name}} zostały pomyślnie zaktualizowane."}, "fields": {"noRecords": "<PERSON>e ma stref serwi<PERSON>, do których można dodać opcje wysyłki.", "tip": "Strefa serwisowa to zbiór stref lub obszarów geograficznych. Służy do ograniczenia dostępnych opcji wysyłki do określonego zestawu lokalizacji."}}}, "shippingProfile": {"domain": "Profile wysyłki", "subtitle": "Grupuj produkty o podobnych wymaganiach wysyłkowych w profile.", "create": {"header": "Utwórz profil wysyłki", "hint": "Utwórz nowy profil wysyłki, aby pogrupować produkty o podobnych wymaganiach wysyłkowych.", "successToast": "Profil w<PERSON> {{name}} został pomyślnie utworzony."}, "delete": {"title": "Usuń profil wysyłki", "description": "<PERSON><PERSON> profil w<PERSON> {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "Profil w<PERSON> {{name}} został pomyślnie usunięty."}, "tooltip": {"type": "Wprowadź typ profilu wysyłki, na przykład: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lk<PERSON> fracht itp."}}, "taxRegions": {"domain": "Regiony podatkowe", "list": {"hint": "Zarząd<PERSON><PERSON>, którymi obciążasz klientów, gdy robią zakupy w różnych krajach i regionach."}, "delete": {"confirmation": "Zamierzasz usunąć region podatkowy. Tej akcji nie można cofnąć.", "successToast": "Region podatkowy został pomyślnie usunięty."}, "create": {"header": "Utwórz region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla konkretnego kraju.", "errors": {"rateIsRequired": "Stawka podatku jest wymagana podczas tworzenia domyślnej stawki podatku.", "nameIsRequired": "Nazwa jest wymagana podczas tworzenia domyślnej stawki podatku."}, "successToast": "Region podatkowy został pomyślnie utworzony."}, "province": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Utwórz regionalny region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla konkretnej prowincji."}}, "state": {"header": "<PERSON><PERSON>", "create": {"header": "Utwórz stanowy region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla określonego stanu."}}, "stateOrTerritory": {"header": "<PERSON><PERSON> lub tery<PERSON>", "create": {"header": "Utwórz region podatkowy stanu/terytorium", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla określonego stanu/terytorium."}}, "county": {"header": "Hrabstwa", "create": {"header": "Utwórz regionalny region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatku dla konkretnego hrabstwa."}}, "region": {"header": "Regiony", "create": {"header": "Utwórz regionalny region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatku dla określonego regionu."}}, "department": {"header": "Działy", "create": {"header": "Utwórz departamentowy region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatku dla konkretnego działu."}}, "territory": {"header": "Terytoria", "create": {"header": "Utwórz terytorialny region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla określonego terytorium."}}, "prefecture": {"header": "Prefektury", "create": {"header": "Utwórz prefekturowy region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla określonej prefektury."}}, "district": {"header": "Dzielnice", "create": {"header": "Utwórz rejonowy region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatku dla konkretnego okręgu."}}, "governorate": {"header": "Gubernatorstwa", "create": {"header": "Utwórz gubernatorski region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla konkretnego województwa."}}, "canton": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Utwórz kantoński region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla konkretnego kantonu."}}, "emirate": {"header": "Emiraty", "create": {"header": "Utwórz region podatkowy Emiratów", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla konkretnego emiratu."}}, "sublevel": {"header": "Podpoziomy", "create": {"header": "Utwórz podpoziomowy region podatkowy", "hint": "Utwórz nowy region podatkowy, aby zdefiniować stawki podatkowe dla określonego podpoziomu."}}, "taxOverrides": {"header": "Zastępuje", "create": {"header": "Utwórz <PERSON>astą<PERSON>", "hint": "Utwórz stawkę podatku, która zastępuje domyślne stawki podatku dla wybranych warunków."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>ą<PERSON>", "hint": "Edytuj stawkę podatku, która zastępuje domyślne stawki podatku dla wybranych warunków."}}, "taxRates": {"create": {"header": "Utwórz stawkę podatku", "hint": "Utwórz nową stawkę podatku, aby zdefiniować stawkę podatku dla regionu.", "successToast": "Stawka podatku została pomyślnie utworzona."}, "edit": {"header": "Edyt<PERSON>j stawkę podatku", "hint": "Edytuj stawkę podatku, aby zdefiniować stawkę podatku dla regionu.", "successToast": "Stawka podatku została pomyślnie zaktualizowana."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> stawkę podatku {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "Stawka podatku została pomyślnie usunięta."}}, "fields": {"isCombinable": {"label": "Mo<PERSON><PERSON><PERSON>ść łączenia", "hint": "<PERSON><PERSON> tę stawkę podatku można połączyć ze stawką domyślną z regionu podatkowego.", "true": "Mo<PERSON><PERSON><PERSON>ść łączenia", "false": "<PERSON><PERSON> można <PERSON>cz<PERSON>"}, "defaultTaxRate": {"label": "Domyślna stawka podatku", "tooltip": "Domyślna stawka podatku dla tego regionu. Przykładem jest standardowa stawka VAT dla kraju lub regionu.", "action": "Utwórz domyślną stawkę podatku"}, "taxRate": "Stawka podatku", "taxCode": "<PERSON><PERSON>", "targets": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> cele, do których będzie miała zastosowanie ta stawka podatku.", "options": {"product": "Produkty", "productCollection": "Kolekcje produktów", "productTag": "Tagi produktów", "productType": "Typy produktów", "customerGroup": "Grupy klientów"}, "operators": {"in": "w", "on": "na", "and": "i"}, "placeholders": {"product": "Wyszukaj produkty", "productCollection": "Wyszukaj kolekcje produktów", "productTag": "Szukaj tagów produktów", "productType": "Wyszukaj typy produktów", "customerGroup": "Wyszukaj grupy klientów"}, "tags": {"product": "Produkt", "productCollection": "Kolekcja produktów", "productTag": "Etykieta produktu", "productType": "Typ produktu", "customerGroup": "Grupa klientów"}, "modal": {"header": "<PERSON><PERSON><PERSON> cele"}, "values_one": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "values_few": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "values_many": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "values_other": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "numberOfTargets_one": "{{count}} cel", "numberOfTargets_few": "{{count}} cele", "numberOfTargets_many": "{{count}} celów", "numberOfTargets_other": "{{count}} celów", "additionalValues_one": "i {{count}} inna war<PERSON>", "additionalValues_few": "i {{count}} inne war<PERSON>ci", "additionalValues_many": "i {{count}} inn<PERSON>", "additionalValues_other": "i {{count}} inn<PERSON>", "action": "<PERSON><PERSON><PERSON> cel"}, "sublevels": {"labels": {"province": "Województwo", "state": "Państwo", "region": "Region", "stateOrTerritory": "Stan/terytorium", "department": "<PERSON><PERSON><PERSON>", "county": "Hrabstwo", "territory": "Terytorium", "prefecture": "Prefektura", "district": "Dzielnica", "governorate": "Gubernatorstwo", "emirate": "Emirat", "canton": "<PERSON><PERSON>", "sublevel": "<PERSON><PERSON>"}, "placeholders": {"province": "<PERSON><PERSON><PERSON><PERSON>", "state": "<PERSON><PERSON><PERSON><PERSON> stan", "region": "Wybierz region", "stateOrTerritory": "Wybierz stan/terytorium", "department": "<PERSON><PERSON><PERSON><PERSON>", "county": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>wo", "territory": "Wybierz terytorium", "prefecture": "<PERSON><PERSON><PERSON>rz prefekturę", "district": "<PERSON><PERSON><PERSON><PERSON>", "governorate": "Wybierz gubernatorstwo", "emirate": "<PERSON><PERSON><PERSON>rz Em<PERSON>", "canton": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltips": {"sublevel": "Wprowadź kod ISO 3166-2 dla podrzędnego regionu podatkowego.", "notPartOfCountry": "{{province}} nie wydaje się być czę<PERSON>cią {{country}}. <PERSON><PERSON><PERSON><PERSON><PERSON> ponownie, czy jest to poprawne."}, "alert": {"header": "Regiony podrzędne są wyłączone dla tego regionu podatkowego", "description": "Regiony podpoziomu są domyślnie wyłączone dla tego regionu. <PERSON><PERSON><PERSON><PERSON>żliwić im tworzenie regionów podpoziomowych, takich jak prowin<PERSON>, stany lub tery<PERSON>.", "action": "Włącz regiony podpoziomowe"}}, "noDefaultRate": {"label": "Brak stawki domyślnej", "tooltip": "Ten region podatkowy nie ma domyślnej stawki podatku. <PERSON><PERSON><PERSON> istnieje stawka standardowa, np. podatek VAT obowiązujący w danym kraju, dodaj ją do tego regionu."}}}, "promotions": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"details": "Szczegóły promocji"}, "tabs": {"template": "<PERSON><PERSON>", "details": "Szczegóły", "campaign": "Kampania"}, "fields": {"type": "<PERSON><PERSON>", "value_type": "<PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>", "campaign": "Kampania", "method": "Metoda", "allocation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addCondition": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "amount": {"tooltip": "W<PERSON>bierz kod waluty, aby um<PERSON><PERSON><PERSON>wić ustawienie kwoty"}, "conditions": {"rules": {"title": "Kto może skorzystać z tego kodu?", "description": "Który klient może skorzystać z kodu promocyjnego? Kod promocyjny może być używany przez wszystkich klientów, jeśli pozostanie nietknięty."}, "target-rules": {"title": "Na jakie artykuły będzie miała zastosowanie promocja?", "description": "Promocja zostanie zastosowana do artykułów spełniających poniższe warunki."}, "buy-rules": {"title": "Co musi znaleźć się w koszyku, aby odblokować promocję?", "description": "Jeżeli te warunki są zgodne, włączamy promocję na wybrane pozycje."}}}, "tooltips": {"campaignType": "<PERSON><PERSON> <PERSON><PERSON><PERSON> budż<PERSON> wydatków, w promocji należy wybrać kod waluty."}, "errors": {"requiredField": "<PERSON> wymagane", "promotionTabError": "Zanim przejdziesz dalej, napraw błędy w zakładce Promocja"}, "toasts": {"promotionCreateSuccess": "Promocja ({{code}}) została pomyślnie utworzona."}, "create": {}, "edit": {"title": "Edytuj szczegóły promocji", "rules": {"title": "Edytuj warunki użytkowania"}, "target-rules": {"title": "Edyt<PERSON>j warunki przedmiotu"}, "buy-rules": {"title": "Edyt<PERSON>j zasady zakupów"}}, "campaign": {"header": "Kampania", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "Pomyślnie zaktualizowano kampanię promocyjną."}, "actions": {"goToCampaign": "Przejdź do kampanii"}}, "campaign_currency": {"tooltip": "To jest waluta promocji. Możesz ją zmienić na karcie Szczegóły."}, "form": {"required": "<PERSON><PERSON><PERSON><PERSON>", "and": "I", "selectAttribute": "<PERSON><PERSON><PERSON><PERSON>", "campaign": {"existing": {"title": "Istniejąca kampania", "description": "Dodaj promocję do istniejącej kampanii.", "placeholder": {"title": "Brak istniejących kampanii", "desc": "Możesz go utworzyć, aby <PERSON><PERSON><PERSON>ć wiele promocji i ustalać limity budżetu."}}, "new": {"title": "Nowa kampania", "description": "Utwórz nową kampanię dla tej promocji."}, "none": {"title": "Bez kampanii", "description": "Kontynuuj bez kojarzenia promocji z kampanią"}}, "status": {"label": "Status", "draft": {"title": "<PERSON><PERSON><PERSON> rob<PERSON>", "description": "Klienci nie będą w stanie użyć tego kodu promocyjnego"}, "active": {"title": "Aktywny", "description": "<PERSON><PERSON>nci mogą użyć tego kodu promocyjnego"}, "inactive": {"title": "Nieaktywny", "description": "Klienci nie mogą już użyć tego kodu promocyjnego"}}, "method": {"label": "Metoda", "code": {"title": "<PERSON><PERSON>", "description": "Klienci muszą wpisać ten kod przy finalizacji zamówienia"}, "automatic": {"title": "Automatyczny", "description": "Promocja zostanie automatycznie wyświetlona klientom przy finalizacji zamówienia."}}, "max_quantity": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Maksymalna ilość artykułów, której dotyczy ta promocja."}, "type": {"standard": {"title": "Standard", "description": "Standardowa promocja"}, "buyget": {"title": "<PERSON><PERSON>", "description": "Ku<PERSON> X, a otrzymasz promocję Y"}}, "allocation": {"each": {"title": "Ka<PERSON><PERSON>", "description": "Stosuje wartość do każdego elementu"}, "across": {"title": "<PERSON><PERSON><PERSON>", "description": "Stosuje wartość do wszystkich elementów"}}, "code": {"title": "Kod", "description": "<PERSON><PERSON>, który Twoi klienci wprowadzą podczas realizacji transakcji."}, "value": {"title": "<PERSON><PERSON><PERSON><PERSON> promocyjna"}, "value_type": {"fixed": {"title": "<PERSON><PERSON><PERSON><PERSON> stała", "description": "Stała kwota rabatu. np. 100"}, "percentage": {"title": "<PERSON><PERSON><PERSON>ć procentowa", "description": "Procent rabatu od kwoty. np. 8%"}}}, "deleteWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> promocję {{code}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "createPromotionTitle": "Utwórz promocję", "type": "<PERSON><PERSON> promocji", "conditions": {"add": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON>, a<PERSON> <PERSON><PERSON> przed<PERSON>, kt<PERSON><PERSON>ch dotyczy promocja."}}}, "campaigns": {"domain": "<PERSON><PERSON>ani<PERSON>", "details": "Szczegóły kampanii", "status": {"active": "Aktywny", "expired": "Wygasły", "scheduled": "Zaplanowany"}, "delete": {"title": "<PERSON><PERSON> j<PERSON> p<PERSON>?", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kampanię „{{name}}”. <PERSON>j akcji nie można cofn<PERSON>.", "successToast": "Kampania „{{name}}” została pomyślnie utworzona."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "description": "Edytuj szczegóły kampanii.", "successToast": "Kampania „{{name}}” została pomyślnie zaktualizowana."}, "configuration": {"header": "Konfiguracja", "edit": {"header": "Edyt<PERSON>j konfigurację kampanii", "description": "Edyt<PERSON>j konfigurację kampanii.", "successToast": "Konfiguracja kampanii została pomyślnie zaktualizowana."}}, "create": {"title": "Utw<PERSON>rz kamp<PERSON>", "description": "Stwórz kampanię promocyjną.", "hint": "Stwórz kampanię promocyjną.", "header": "Utw<PERSON>rz kamp<PERSON>", "successToast": "Kampania „{{name}}” została pomyślnie utworzona."}, "fields": {"name": "Nazwa", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start_date": "Data rozpoczęcia", "end_date": "Data końcowa", "total_spend": "Bud<PERSON><PERSON> wydany", "total_used": "Wykorzystany budżet", "budget_limit": "<PERSON>it bud<PERSON>", "campaign_id": {"hint": "Na tej liście wyświetlane są tylko kampanie z tym samym kodem waluty co promocja."}}, "budget": {"create": {"hint": "Utwórz budżet kampanii.", "header": "Bud<PERSON>et kampanii"}, "details": "Bud<PERSON>et kampanii", "fields": {"type": "<PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON>", "limit": "Limit", "used": "Używany"}, "type": {"spend": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ustaw limit łącznej kwoty objętej rabatem dla wszystkich zastosowań promocji."}, "usage": {"title": "Stosowanie", "description": "Ustaw limit ile razy można skorzystać z promocji."}}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> kampanii"}}, "promotions": {"remove": {"title": "Usuń promocję z kampanii", "description": "Zamierzasz usunąć promocje z kampanii ({{count}}). <PERSON><PERSON> akcji nie można cofn<PERSON>."}, "alreadyAdded": "Ta promocja została już dodana do kampanii.", "alreadyAddedDiffCampaign": "Ta promocja została już dodana do innej kampanii ({{name}}).", "currencyMismatch": "Waluta promocji i kampanii nie jest zgodna", "toast": {"success": "Pomyślnie dodano promocje ({{count}}) do kampanii"}, "add": {"list": {"noRecordsMessage": "Najpierw utwórz promocję."}}, "list": {"noRecordsMessage": "W kampanii nie ma żadnych promocji."}}, "deleteCampaignWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kampanię {{name}}. <PERSON><PERSON> akcji nie można cof<PERSON>.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Cenniki", "subtitle": "Twórz wyprzedaże lub zastępuj ceny dla określonych warunków.", "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> cennik {{title}}. <PERSON><PERSON> akcji nie można co<PERSON>.", "successToast": "<PERSON>nn<PERSON> {{title}} został pomyślnie usunięty."}, "create": {"header": "Utwórz <PERSON>", "subheader": "Utwórz nowy cennik, a<PERSON> z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cenami swoich produktów.", "tabs": {"details": "Bliższe dane", "products": "Produkty", "prices": "<PERSON><PERSON>"}, "successToast": "<PERSON>nn<PERSON> {{title}} został pomyślnie utworzony.", "products": {"list": {"noRecordsMessage": "Najpierw utwórz produkt."}}}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>", "successToast": "<PERSON>nn<PERSON> {{title}} został pomyślnie zaktualizowany."}, "configuration": {"header": "Konfiguracja", "edit": {"header": "Edyt<PERSON>j konfigurację cennika", "description": "Edyt<PERSON>j konfigurację cennika.", "successToast": "Konfiguracja cennika została pomyślnie zaktualizowana."}}, "products": {"header": "Produkty", "actions": {"addProducts": "<PERSON><PERSON>j produkty", "editPrices": "<PERSON><PERSON><PERSON><PERSON> ceny"}, "delete": {"confirmation_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> ceny dla {{count}} produktu z cennika. Tej akcji nie można cofn<PERSON>.", "confirmation_few": "Zamier<PERSON><PERSON>ć ceny dla {{count}} produktów z cennika. Tej akcji nie można cofn<PERSON>ć.", "confirmation_many": "Zamier<PERSON><PERSON>ć ceny dla {{count}} produktów z cennika. Tej akcji nie można cofn<PERSON>ć.", "confirmation_other": "Zamier<PERSON><PERSON>ć ceny dla {{count}} produktów z cennika. Tej akcji nie można cofn<PERSON>ć.", "successToast_one": "Pomyślnie usunięto ceny dla {{count}} produktu.", "successToast_few": "Pomyślnie usunięto ceny dla {{count}} produktów.", "successToast_many": "Pomyślnie usunięto ceny dla {{count}} produktów.", "successToast_other": "Pomyślnie usunięto ceny dla {{count}} produktów."}, "add": {"successToast": "Ceny zostały pomyślnie dodane do cennika."}, "edit": {"successToast": "Ceny zostały pomyślnie zaktualizowane."}}, "fields": {"priceOverrides": {"label": "<PERSON><PERSON><PERSON><PERSON> ceny", "header": "<PERSON><PERSON><PERSON><PERSON> ceny"}, "status": {"label": "Status", "options": {"active": "Aktywny", "draft": "Projekt", "expired": "Wygasły", "scheduled": "Zaplanowany"}}, "type": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, kt<PERSON><PERSON> chcesz utworzyć.", "options": {"sale": {"label": "Wyp<PERSON><PERSON>ż", "description": "Ceny wyprzedażowe to tymczasowe obniżki cen produktów."}, "override": {"label": "Niestandardowa", "description": "Ceny niestandardowe są stosowane dla określonych klientów."}}}, "startsAt": {"label": "Cennik ma datę rozpoczęcia?", "hint": "Zaplanuj aktywację cennika w przyszłości."}, "endsAt": {"label": "Cennik ma datę ważności?", "hint": "Zaplanuj dezaktywację cennika w przyszłości."}, "customerAvailability": {"header": "Wybierz grupy klientów", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klienta", "hint": "Wybierz do jakich grup klientów ma być zastosowany cennik.", "placeholder": "Wyszukaj grupy klientów", "attribute": "Grupy klientów"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "Zarządzaj danymi swojego profilu.", "fields": {"languageLabel": "Język", "usageInsightsLabel": "Informacje o użytkowaniu"}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> profil", "languageHint": "<PERSON><PERSON><PERSON><PERSON>, którego chcesz używać w panelu administracyjnym. Nie zmienia to języka Twojego sklepu.", "languagePlaceholder": "<PERSON><PERSON>bierz język", "usageInsightsHint": "Podziel się spostrzeżeniami dotyczącymi użytkowania i pomóż nam ulepszyć Meduzę. Więcej informacji o tym, co zbieramy i jak z tego wykorzystujemy, moż<PERSON>z przeczytać w naszej <0>dokumentacji</0>."}, "toast": {"edit": "Zmiany w profilach zostały zapisane"}}, "users": {"domain": "Użytkownicy", "editUser": "Edytuj użytkownika", "inviteUser": "Zaproś użytkownika", "inviteUserHint": "Zaproś nowego użytkownika do swojego sklepu.", "sendInvite": "<PERSON><PERSON><PERSON><PERSON><PERSON> zaproszenie", "pendingInvites": "Oczekujące zaproszenia", "deleteInviteWarning": "<PERSON>ami<PERSON><PERSON><PERSON>un<PERSON>ć zaproszenie dla użytkownika {{email}}. <PERSON>j akcji nie można co<PERSON>.", "resendInvite": "Wyślij zaproszenie ponownie", "copyInviteLink": "Skopiuj link z zaproszeniem", "expiredOnDate": "<PERSON><PERSON><PERSON>ł<PERSON> w dniu {{date}}", "validFromUntil": "Obowiązuje od <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Zaakceptowano w dniu {{date}}", "inviteStatus": {"accepted": "Przyj<PERSON><PERSON>", "pending": "Aż do", "expired": "Wygasły"}, "roles": {"admin": "Administrator", "developer": "Wywoływacz", "member": "Członek"}, "list": {"empty": {"heading": "Brak użytkowników", "description": "Gdy użytkownik zostanie zaproszony, pojawi się tutaj."}, "filtered": {"heading": "Brak wyników", "description": "Żaden użytkownik nie pasuje do bieżących kryteriów filtrowania."}}, "deleteUserWarning": "<PERSON>ami<PERSON><PERSON><PERSON> usunąć użytkownika {{name}}. <PERSON>j akcji nie można cofn<PERSON>.", "deleteUserSuccess": "Użytkownik {{name}} został pomyślnie usunięty", "invite": "Zaproszenia"}, "store": {"domain": "<PERSON>kle<PERSON>", "manageYourStoresDetails": "Zarządzaj szczegółami swojego sklepu", "editStore": "<PERSON><PERSON><PERSON><PERSON>", "defaultCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON> waluta", "defaultRegion": "Domyślny region", "defaultSalesChannel": "Domyślny kanał sprzedaży", "defaultLocation": "Domyślna lokalizacja", "swapLinkTemplate": "Zamień szablon łącza", "paymentLinkTemplate": "Szablon łącza do płatności", "inviteLinkTemplate": "Szablon linku zaproszenia", "currencies": "<PERSON><PERSON><PERSON><PERSON>", "addCurrencies": "<PERSON><PERSON><PERSON>", "enableTaxInclusivePricing": "Włącz ceny zawierające podatek", "disableTaxInclusivePricing": "Wyłącz ceny zawierające podatek", "removeCurrencyWarning_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> usun<PERSON> {{count}} walutę ze swojego sklepu. Upewnij się, że usunąłeś wszystkie ceny w wybranej walucie.", "removeCurrencyWarning_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> usun<PERSON> {{count}} waluty ze swojego sklepu. Upewnij się, że usunąłeś wszystkie ceny w wybranych walutach.", "removeCurrencyWarning_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> usun<PERSON> {{count}} walut ze swojego sklepu. Upewnij si<PERSON>, że usunąłeś wszystkie ceny w wybranych walutach.", "removeCurrencyWarning_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> usun<PERSON> {{count}} walut ze swojego sklepu. Upewnij si<PERSON>, że usunąłeś wszystkie ceny w wybranych walutach.", "currencyAlreadyAdded": "Waluta została już dodana do Twojego sklepu.", "edit": {"header": "<PERSON><PERSON><PERSON><PERSON>"}, "toast": {"update": "Sklep pomyślnie zaktualizowany", "currenciesUpdated": "Waluty zostały pomyślnie zaktualizowane", "currenciesRemoved": "Pomyślnie usunięto waluty ze sklepu", "updatedTaxInclusivitySuccessfully": "Ceny zawierające podatek zostały pomyślnie zaktualizowane"}}, "regions": {"domain": "Regiony", "subtitle": "Region to obszar, na którym sprzedajesz produkty. Może obejmować wiele krajów i ma różne stawki podatkowe, dostawców i walutę.", "createRegion": "Utwórz region", "createRegionHint": "Zarządzaj stawkami podatkowymi i dostawcami dla zestawu krajów.", "addCountries": "<PERSON><PERSON><PERSON> k<PERSON>", "editRegion": "Edytuj region", "countriesHint": "Dodaj kraje zawarte w tym regionie.", "deleteRegionWarning": "Zamierzasz usunąć region {{name}}. Tej akcji nie można cofn<PERSON>.", "removeCountriesWarning_one": "Zamierzasz usunąć z regionu {{count}} kraj. Tej akcji nie można cofn<PERSON>.", "removeCountriesWarning_few": "Zamierzasz usunąć z regionu {{count}} kraje. Tej akcji nie można cofn<PERSON>.", "removeCountriesWarning_many": "Zamierzasz usunąć z regionu {{count}} krajów. Tej akcji nie można cofnąć.", "removeCountriesWarning_other": "Zamierzasz usunąć z regionu {{count}} krajów. Tej akcji nie można cofnąć.", "removeCountryWarning": "Zamierzasz usunąć kraj {{name}} z regionu. Tej akcji nie można cofnąć.", "automaticTaxesHint": "Je<PERSON><PERSON> ta opcja jest włączona, podatki będą naliczane tylko przy kasie na podstawie adresu wysyłki.", "taxInclusiveHint": "Po włączeniu ceny w regionie będą zawierać podatek.", "providersHint": "<PERSON><PERSON><PERSON>, którzy dostawcy płatności są dostępni w tym regionie.", "shippingOptions": "<PERSON><PERSON>je wysyłki", "deleteShippingOptionWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> opcję wysyłki {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "return": "Powró<PERSON>", "outbound": "Wychodzące", "priceType": "<PERSON><PERSON> ceny", "flatRate": "<PERSON><PERSON><PERSON><PERSON> stawka", "calculated": "Obliczony", "list": {"noRecordsMessage": "Utwórz region dla obszarów, w których prowadzisz sprzedaż."}, "toast": {"delete": "Region został pomyślnie usunięty", "edit": "Edycja regionu została zapisana", "create": "Region został utworzony pomyślnie", "countries": "Kraje regionu zostały pomyślnie zaktualizowane"}, "shippingOption": {"createShippingOption": "Utwórz opcję wysyłki", "createShippingOptionHint": "Utwórz nową opcję wysyłki dla regionu.", "editShippingOption": "<PERSON><PERSON><PERSON>j <PERSON>ję wysyłki", "fulfillmentMethod": "Sposób realizacji", "type": {"outbound": "Wychodzące", "outboundHint": "Uż<PERSON>j tej opcji, jeśli tworzysz opcję wysyłki w celu wysłania produktów do klienta.", "return": "Powró<PERSON>", "returnHint": "<PERSON><PERSON><PERSON><PERSON> tej opc<PERSON>, je<PERSON><PERSON> tworz<PERSON>z opcję wysyłki, aby klient mógł zwrócić Ci produkty."}, "priceType": {"label": "<PERSON><PERSON> ceny", "flatRate": "<PERSON><PERSON><PERSON><PERSON> stawka", "calculated": "Obliczony"}, "availability": {"adminOnly": "Tylko administrator", "adminOnlyHint": "Po włączeniu opcja wysyłki będzie dostępna tylko w panelu administratora, a nie w witrynie sklepu."}, "taxInclusiveHint": "Po włączeniu cena opcji wysyłki będzie zawierała podatek.", "requirements": {"label": "Wymagania", "hint": "Określ wymagania dotyczące opcji wysyłki."}}}, "taxes": {"domain": "Regiony podatkowe", "domainDescription": "Zarządzaj swoim regionem podatkowym", "countries": {"taxCountriesHint": "Ustawienia podatków mają zastosowanie do wymienionych krajów."}, "settings": {"editTaxSettings": "<PERSON><PERSON><PERSON><PERSON> us<PERSON> podatkowe", "taxProviderLabel": "Dostawca podatków", "systemTaxProviderLabel": "Dostawca podatku systemowego", "calculateTaxesAutomaticallyLabel": "Oblicz podatki automatycznie", "calculateTaxesAutomaticallyHint": "Po włączeniu stawki podatku będą obliczane automatycznie i stosowane do koszyków. Gdy opcja ta jest wyłączona, podatki należy obliczyć ręcznie przy kasie. W przypadku zewnętrznych dostawców podatków zaleca się korzystanie z podatków ręcznych.", "applyTaxesOnGiftCardsLabel": "Zastosuj podatki od kart podarunkowych", "applyTaxesOnGiftCardsHint": "Je<PERSON><PERSON> ta opcja jest włączona, podatki zostaną naliczone na kartach podarunkowych przy kasie. W niektórych krajach przepisy podatkowe wymagają nałożenia podatku na karty upominkowe przy zakupie.", "defaultTaxRateLabel": "Domyślna stawka podatku", "defaultTaxCodeLabel": "Domyślny kod podatku"}, "defaultRate": {"sectionTitle": "Domyślna stawka podatku"}, "taxRate": {"sectionTitle": "Stawki podatkowe", "createTaxRate": "Utwórz stawkę podatku", "createTaxRateHint": "Utwórz nową stawkę podatku dla regionu.", "deleteRateDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> stawkę podatku {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "editTaxRate": "Edyt<PERSON>j stawkę podatku", "editRateAction": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "editOverridesAction": "Edytuj zastąpienia", "editOverridesTitle": "Edyt<PERSON>j zmiany stawek podatku", "editOverridesHint": "Określ zastąpienie stawki podatku.", "deleteTaxRateWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> stawkę podatku {{name}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "productOverridesLabel": "Zastąpienie produktu", "productOverridesHint": "Określ zastąpienie produktu stawką podatku.", "addProductOverridesAction": "Dodaj zastąpienia produktu", "productTypeOverridesLabel": "Zastąpienia typu produktu", "productTypeOverridesHint": "Określ zastąpienie typu produktu dla stawki podatku.", "addProductTypeOverridesAction": "Dodaj zastąpienia typu produktu", "shippingOptionOverridesLabel": "Opcja wysyłki zastępuje", "shippingOptionOverridesHint": "Określ zastąpienie opcji wysyłki dla stawki podatku.", "addShippingOptionOverridesAction": "Dodaj zastąpienia opcji wysyłki", "productOverridesHeader": "Produkty", "productTypeOverridesHeader": "Typy produktów", "shippingOptionOverridesHeader": "<PERSON><PERSON>je wysyłki"}}, "locations": {"domain": "Lokalizacje", "editLocation": "<PERSON><PERSON><PERSON><PERSON>", "addSalesChannels": "Dodaj kanały sprzedaży", "noLocationsFound": "Nie znaleziono lokalizacji", "selectLocations": "<PERSON><PERSON><PERSON><PERSON>, w których przechowywany jest dany przedmiot.", "deleteLocationWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> lokalizację {{name}}. <PERSON><PERSON> akcji nie można co<PERSON>n<PERSON>.", "removeSalesChannelsWarning_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} kanał sprzedaży z wybranej lokalizacji.", "removeSalesChannelsWarning_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} kanały sprzedaży z wybranej lokalizacji.", "removeSalesChannelsWarning_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} kanałów  sprzedaży z wybranej lokalizacji.", "removeSalesChannelsWarning_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} kanałów  sprzedaży z wybranej lokalizacji.", "toast": {"create": "Lokalizacja została utworzona pomyślnie", "update": "Lokalizacja została pomyślnie zaktualizowana", "removeChannel": "Kanał sprzedaży został pomyślnie usunięty"}}, "reservations": {"domain": "Rezerwacje", "subtitle": "Zarządzaj zarezerwowaną ilością pozycji magazynowych.", "deleteWarning": "<PERSON>ami<PERSON><PERSON><PERSON> rezerwację. Tej akcji nie można cofn<PERSON>."}, "salesChannels": {"domain": "Kanały sprzedaży", "subtitle": "Zarządzaj kanałami online i offline, w których sprzedajesz produkty.", "list": {"empty": {"heading": "Brak kanałów sprzedaży", "description": "Gdy kanał sprzedaży zostanie utworzony, pojawi sie tutaj."}, "filtered": {"heading": "Brak wyników", "description": "Żaden kanał sprzedaży nie pasuje do bieżących kryteriów filtrowania."}}, "createSalesChannel": "Utwórz kanał sprz<PERSON>ży", "createSalesChannelHint": "Utwórz nowy kanał sprzedaży, w którym będziesz mógł sprzedawać swoje produkty.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON>, czy kanał sprzedaży jest włączony.", "removeProductsWarning_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produkt z {{sales_channel}}.", "removeProductsWarning_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produkty z {{sales_channel}}.", "removeProductsWarning_many": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produktów z {{sales_channel}}.", "removeProductsWarning_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} produktów z {{sales_channel}}.", "addProducts": "<PERSON><PERSON>j produkty", "editSalesChannel": "<PERSON><PERSON><PERSON><PERSON> kanał sprzedaży", "productAlreadyAdded": "Produkt został już dodany do kanału sprzedaży.", "deleteSalesChannelWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kanał sprzeda<PERSON> {{name}}. <PERSON><PERSON> akcji nie można co<PERSON>n<PERSON>.", "toast": {"create": "Kanał sprzedaży został pomyślnie utworzony", "update": "Kanał sprzedaży został pomyślnie zaktualizowany", "delete": "Kanał sprzedaży został pomyślnie usunięty"}, "tooltip": {"cannotDeleteDefault": "<PERSON>e można usun<PERSON> domyślnego kanału sprzedaży"}, "products": {"list": {"noRecordsMessage": "Brak produktów w kanale sprzedaży."}, "add": {"list": {"noRecordsMessage": "Najpierw utwórz produkt."}}}}, "apiKeyManagement": {"domain": {"publishable": "Publikowalne klucze API", "secret": "Tajne klucze API"}, "subtitle": {"publishable": "Zarządzaj kluczami API używanymi w witrynie sklepowej, aby ograni<PERSON>yć zakres zapytań do konkretnych kanałów sprzedaży.", "secret": "Zarządzaj kluczami API używanymi do uwierzytelniania użytkowników administracyjnych w aplikacjach administracyjnych."}, "status": {"active": "Aktywny", "revoked": "Odwołany"}, "type": {"publishable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> publikacji", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Utwórz klucz API do publikacji", "createPublishableHint": "Utwórz nowy publikowany klucz API, aby ograniczyć zakres żądań do określonych kanałów sprzedaży.", "createSecretHeader": "Utwórz tajny klucz API", "createSecretHint": "Utwórz nowy tajny klucz API, aby uzyskać dostęp do API Medusa jako uwierzytelniony użytkownik administracyjny.", "secretKeyCreatedHeader": "Utworzono tajny klucz", "secretKeyCreatedHint": "Twój nowy tajny klucz został wygenerowany. Skopiuj i bezpiecznie przechowuj teraz. Jest to jedyny raz, kiedy zostanie wyświetlony.", "copySecretTokenSuccess": "Tajny klucz został skopiowany do schowka.", "copySecretTokenFailure": "Nie udało się skopiować tajnego klucza do schowka.", "successToast": "Klucz API został pomyślnie utworzony."}, "edit": {"header": "Edytuj klucz API", "description": "Edytuj tytuł klucza API.", "successToast": "Klucz API {{title}} został pomyślnie zaktualizowany."}, "salesChannels": {"title": "Dodaj kanały sprzedaży", "description": "Dodaj kanały sprzedaży, do których ma być ograniczony klucz API.", "successToast_one": "Do klucza API pomyślnie dodano {{count}} kanał sprz<PERSON>.", "successToast_few": "Do klucza API pomyślnie dodano {{count}} kanały sprzedaży.", "successToast_many": "Do klucza API pomyślnie dodano {{count}} kanałów sprzedaży.", "successToast_other": "Do klucza API pomyślnie dodano {{count}} kanałów sprzedaży.", "alreadyAddedTooltip": "Kanał sprzedaży został już dodany do klucza API.", "list": {"noRecordsMessage": "W zakresie publikowalnego klucza API nie ma kanałów sprzedaży."}}, "delete": {"warning": "Zamierzasz usunąć klucz API {{title}}. <PERSON>j akcji nie można cofn<PERSON>.", "successToast": "Klucz API {{title}} został pomyślnie usunięty."}, "revoke": {"warning": "Zamierzasz unieważnić klucz API {{title}}. <PERSON>j akcji nie można cofn<PERSON>.", "successToast": "Klucz API {{title}} został pomyślnie unieważniony."}, "addSalesChannels": {"list": {"noRecordsMessage": "Najpierw utwórz kanał sprzedaży."}}, "removeSalesChannel": {"warning": "Zamierzas<PERSON> usunąć kanał sprzedaży {{name}} z klucza API. Tej akcji nie można cofnąć.", "warningBatch_one": "Zami<PERSON><PERSON><PERSON> {{count}} kanał sprzedaży z klucza API. Tej akcji nie można cofnąć.", "warningBatch_few": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{count}} kanały sprzedaży z klucza API. Tej akcji nie można cofnąć.", "warningBatch_many": "Zami<PERSON><PERSON><PERSON> {{count}} kanałów sprzedaży z klucza API. Tej akcji nie można cofnąć.", "warningBatch_other": "Zami<PERSON><PERSON><PERSON> {{count}} kanałów sprzedaży z klucza API. Tej akcji nie można cofnąć.", "successToast": "Kanał sprzedaży został pomyślnie usunięty z klucza API.", "successToastBatch_one": "Pomyślnie usunięto z klucza API {{count}} kanał sprz<PERSON>.", "successToastBatch_few": "Pomyślnie usunięto z klucza API {{count}} kanały sprzedaży.", "successToastBatch_many": "Pomyślnie usunięto z klucza API {{count}} kanałów sprzedaży.", "successToastBatch_other": "Pomyślnie usunięto z klucza API {{count}} kanałów sprzedaży."}, "actions": {"revoke": "Unieważnij klucz API", "copy": "Skopiuj klucz API", "copySuccessToast": "Klucz API został skopiowany do schowka."}, "table": {"lastUsedAtHeader": "Ostatnio używany w", "createdAtHeader": "Odwołane o godz"}, "fields": {"lastUsedAtLabel": "Ostatnio używany o godz", "revokedByLabel": "Odwołane przez", "revokedAtLabel": "Odwołane o godz", "createdByLabel": "Stworz<PERSON> przez"}}, "returnReasons": {"domain": "Powody zwrotu", "subtitle": "Zarządzaj przyczynami zwróconych produktów.", "calloutHint": "Zarządzaj powodami kategoryzowania zwrotów.", "editReason": "<PERSON><PERSON><PERSON><PERSON> powód zwrotu", "create": {"header": "<PERSON><PERSON>j powód zwrotu", "subtitle": "Podaj najczęstsze powody zwrotów.", "hint": "Utwórz nowy powód zwrotu, aby ka<PERSON><PERSON><PERSON><PERSON><PERSON>ć zwroty.", "successToast": "Powód zwrotu {{label}} został pomyślnie utworzony."}, "edit": {"header": "<PERSON><PERSON><PERSON><PERSON> powód zwrotu", "subtitle": "<PERSON><PERSON><PERSON><PERSON> powodu zwrotu.", "successToast": "Powód zwrotu {{label}} został pomyślnie zaktualizowany."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> powód zwrotu {{label}}. <PERSON><PERSON> akcji nie można co<PERSON>.", "successToast": "Powód zwrotu {{label}} został pomyślnie usunięty."}, "fields": {"value": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "zly_roz<PERSON>r", "tooltip": "<PERSON><PERSON><PERSON><PERSON> powinna być unikalnym identyfikatorem powodu zwrotu."}, "label": {"label": "Etykieta", "placeholder": "Zły rozmiar"}, "description": {"label": "Opis", "placeholder": "Klient otrzymał zły rozmiar"}}}, "login": {"forgotPassword": "<PERSON>ap<PERSON><PERSON><PERSON><PERSON><PERSON> hasła? - <0>Reset<PERSON>j</0>", "title": "Witamy w Meduzie", "hint": "Z<PERSON><PERSON>j si<PERSON>, aby uzyskać dostęp do obszaru konta"}, "invite": {"title": "Witamy w Meduzie", "hint": "Utwórz swoje konto poniżej", "backToLogin": "Powrót do logowania", "createAccount": "Utwórz konto", "alreadyHaveAccount": "<PERSON><PERSON> ju<PERSON> konto? - <0>Z<PERSON><PERSON><PERSON> si<PERSON></0>", "emailTooltip": "Nie można zmienić Twojego adresu e-mail. <PERSON><PERSON><PERSON> s<PERSON> z innego adresu e-mail, na<PERSON><PERSON><PERSON> wysłać nowe zaproszenie.", "invalidInvite": "Zaproszenie jest nieprawidłowe lub wygasło.", "successTitle": "Twoje konto zostało zarejestrowane", "successHint": "Zacznij od razu z Medusa Admin.", "successAction": "Uruchom administratora <PERSON>", "invalidTokenTitle": "Twój token zaproszenia jest nieprawidłowy", "invalidTokenHint": "Spróbuj poprosić o nowy link z zaproszeniem.", "passwordMismatch": "<PERSON><PERSON> nie pasują", "toast": {"accepted": "Zaproszenie zostało pomyślnie zaakceptowane"}}, "resetPassword": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "W<PERSON>z poniżej swój adres e-mail, a my wyślemy Ci instrukcję resetowania hasła.", "email": "E-mail", "sendResetInstructions": "<PERSON><PERSON>ś<PERSON>j instrukcje resetowania", "backToLogin": "<0><PERSON><PERSON>óć do logowania</0>", "newPasswordHint": "Wybierz poniżej nowe hasło.", "invalidTokenTitle": "Twój token resetowania jest nieprawidłowy", "invalidTokenHint": "Spróbu<PERSON> o nowy link resetujący.", "expiredTokenTitle": "Twój token resetowania utracił ważność", "goToResetPassword": "Przejdź do Resetuj hasło", "resetPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newPassword": "Nowe hasło", "repeatNewPassword": "Powtórz nowe hasło", "tokenExpiresIn": "Token wygasa za <0>{{time}}</0> minut", "successfulRequestTitle": "Pomyślnie wysłałem Ci e-mail", "successfulRequest": "Wysłaliśmy do Ciebie e-mail, którego możesz użyć do zresetowania hasła. Sprawdź folder ze spamem, je<PERSON>li wiadomo<PERSON> nie dotarła po kilku minutach.", "successfulResetTitle": "Resetowanie hasła powiodło się", "successfulReset": "<PERSON><PERSON><PERSON> z<PERSON> się na stronie logowania.", "passwordMismatch": "<PERSON><PERSON> nie pasują", "invalidLinkTitle": "Twój link resetujący jest nieprawidłowy", "invalidLinkHint": "Spróbuj ponownie zresetować hasło."}, "workflowExecutions": {"domain": "Przepływy pracy", "subtitle": "Przeglądaj i śledź wykonania przepływu pracy w aplikacji Medusa.", "transactionIdLabel": "Identy<PERSON><PERSON><PERSON>", "workflowIdLabel": "Identyfikator przepływu pracy", "progressLabel": "Postęp", "stepsCompletedLabel_one": "{{completed}} z {{count}} kroku", "stepsCompletedLabel_few": "{{completed}} z {{count}} kroków", "stepsCompletedLabel_many": "{{completed}} z {{count}} kroków", "stepsCompletedLabel_other": "{{completed}} z {{count}} kroków", "list": {"noRecordsMessage": "Nie wykonano jeszcze żadnych przepływów pracy."}, "history": {"sectionTitle": "Historia", "runningState": "Działanie...", "awaitingState": "Oczekiwanie", "failedState": "Przegrany", "skippedState": "Pominięte", "skippedFailureState": "Pominięte (niepowodzenie)", "definitionLabel": "<PERSON>fin<PERSON><PERSON>", "outputLabel": "Wyjście", "compensateInputLabel": "Kompensuj <PERSON>", "revertedLabel": "Przywrócony", "errorLabel": "Błąd"}, "state": {"done": "Zrobione", "failed": "Przegrany", "reverted": "Przywrócony", "invoking": "Inwokowanie", "compensating": "Kompensacyjne", "notStarted": "<PERSON><PERSON>"}, "transaction": {"state": {"waitingToCompensate": "Oczekiwanie na rekompensatę"}}, "step": {"state": {"skipped": "Pominięte", "skippedFailure": "Pominięte (niepowodzenie)", "dormant": "Uśpiony", "timeout": "<PERSON><PERSON>"}}}, "productTypes": {"domain": "Typy produktów", "subtitle": "Uporządkuj swoje produkty według typów.", "create": {"header": "Utwórz typ produktu", "hint": "Utwórz nowy typ produktu, aby ka<PERSON><PERSON><PERSON><PERSON><PERSON>ć swoje produkty.", "successToast": "Typ produktu {{value}} został pomyślnie utworzony."}, "edit": {"header": "Ed<PERSON><PERSON>j typ produktu", "successToast": "Typ produktu {{value}} został pomyślnie zaktualizowany."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON> typ produktu {{value}}. <PERSON>j akcji nie można co<PERSON>.", "successToast": "Typ produktu {{value}} został pomyślnie usunięty."}, "fields": {"value": "<PERSON><PERSON><PERSON><PERSON>"}}, "productTags": {"domain": "Tagi produktów", "create": {"header": "Utwórz tag produktu", "subtitle": "Utwórz nowy tag produktu, aby kategoryzować swoje produkty.", "successToast": "Tag produktu {{value}} został pomyślnie utworzony."}, "edit": {"header": "Edytuj tag produktu", "subtitle": "<PERSON><PERSON><PERSON><PERSON> tagu produktu.", "successToast": "Tag produktu {{value}} został pomyślnie zaktualizowany."}, "delete": {"confirmation": "<PERSON>ami<PERSON><PERSON><PERSON>ć tag produktu {{value}}. <PERSON><PERSON> akcji nie można cofn<PERSON>.", "successToast": "Tag produktu {{value}} został pomyślnie usunięty."}, "fields": {"value": "<PERSON><PERSON><PERSON><PERSON>"}}, "notifications": {"domain": "Powiadomienia", "emptyState": {"title": "Brak powiadomień", "description": "W tej chwili nie masz żadnych powiadomień, ale gdy to zrobisz, będą one tutaj wyświetlane."}, "accessibility": {"description": "powiadomienia o działaniach Meduzy będą tutaj wymienione."}}, "errors": {"serverError": "Błąd serwera — spróbuj ponownie później.", "invalidCredentials": "Zły adres e-mail lub hasło"}, "statuses": {"scheduled": "Zaplanowany", "expired": "Wygasły", "active": "Aktywny", "inactive": "Nieaktywny", "draft": "Szkic", "enabled": "Włączony", "disabled": "Wyłączony"}, "labels": {"productVariant": "Wariant produktu", "prices": "<PERSON><PERSON>", "available": "Dostępny", "inStock": "W magazynie", "added": "W dodatku", "removed": "Usunięty", "from": "Od", "to": "Do", "beaware": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>", "loading": "Ładowanie"}, "fields": {"amount": "K<PERSON><PERSON>", "refundAmount": "Kwota zwrotu", "name": "Nazwa", "default": "Domyślny", "lastName": "Nazwisko", "firstName": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "customTitle": "<PERSON><PERSON><PERSON>", "manageInventory": "Zarządzaj zapasami", "inventoryKit": "Posiada zestaw inwentarza", "inventoryItems": "Elementy zapasów", "inventoryItem": "Pozycja inwentarza", "requiredQuantity": "<PERSON><PERSON><PERSON><PERSON>", "description": "Opis", "email": "E-mail", "password": "<PERSON><PERSON><PERSON>", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmPassword": "Potwierd<PERSON> hasło", "newPassword": "Nowe hasło", "repeatNewPassword": "Powtórz Nowe hasło", "categories": "<PERSON><PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON>s<PERSON>b w<PERSON>yłki", "configurations": "Konfiguracje", "conditions": "Warunki", "category": "Kategoria", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discountable": "Zniżka", "handle": "Uchwyt", "subtitle": "Podtytuł", "by": "<PERSON><PERSON><PERSON>", "item": "Przedmiot", "qty": "<PERSON><PERSON><PERSON><PERSON>", "limit": "Limit", "tags": "Tagi", "type": "<PERSON><PERSON>", "reason": "<PERSON><PERSON><PERSON><PERSON>", "none": "nic", "all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "Szukaj", "percentage": "Procent", "sales_channels": "Kanały sprzedaży", "customer_groups": "Grupy klientów", "product_tags": "Tagi produktów", "product_types": "Typy produktów", "product_collections": "Kolekcje produktów", "status": "Status", "code": "Kod", "value": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "Wyłączony", "dynamic": "Dynamiczny", "normal": "Normalna", "years": "Lata", "months": "<PERSON><PERSON><PERSON>", "days": "Dni", "hours": "<PERSON><PERSON><PERSON>", "minutes": "Protokół", "totalRedemptions": "Całkowite wykupy", "countries": "<PERSON><PERSON><PERSON>", "paymentProviders": "Dostawcy płatności", "refundReason": "Powód zwrotu", "fulfillmentProviders": "Dostawcy realizacji", "fulfillmentProvider": "Dostawca realizacji", "providers": "<PERSON><PERSON><PERSON><PERSON>", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": "<PERSON><PERSON>", "optional": "Opcjonalny", "note": "Notatka", "automaticTaxes": "Automatyczne podatki", "taxInclusivePricing": "Ceny zawierają podatek", "currency": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON>", "address2": "Mi<PERSON>zkan<PERSON>, apartament itp.", "city": "<PERSON><PERSON>", "postalCode": "<PERSON><PERSON>", "country": "<PERSON><PERSON>", "state": "Państwo", "province": "Województwo", "company": "Firma", "phone": "Telefon", "metadata": "Metadane", "selectCountry": "<PERSON><PERSON><PERSON>rz kraj", "products": "Produkty", "variants": "Warianty", "orders": "Święcenia", "account": "Ko<PERSON>", "total": "Wartość zamówienia", "paidTotal": "Zapłacono", "totalExclTax": "<PERSON><PERSON><PERSON> bez podatek", "subtotal": "Su<PERSON>", "shipping": "Wysyłka", "outboundShipping": "Wysyłka wychodząca", "returnShipping": "Wysyłka zwrotna", "tax": "Podatek", "created": "Stworzony", "key": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Klient", "date": "Data", "order": "Zamówienie", "fulfillment": "Spełnienie", "provider": "Dostawca", "payment": "Zapłata", "items": "Rzeczy", "salesChannel": "<PERSON><PERSON>ł sprzedaż<PERSON>", "region": "Region", "discount": "Rabat", "role": "Rola", "sent": "Wysłano", "salesChannels": "Kanały sprzedaży", "product": "Produkt", "createdAt": "Stworzony", "updatedAt": "Zaktualizowany", "revokedAt": "Odwołane o godz", "true": "Prawda", "false": "Fałsz", "giftCard": "<PERSON><PERSON> podaru<PERSON>", "tag": "Etykietka", "dateIssued": "Data wydania", "issuedDate": "Data wydania", "expiryDate": "<PERSON>", "price": "<PERSON><PERSON>", "priceTemplate": "Cena {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weight": "W<PERSON>", "midCode": "Kod MID", "hsCode": "Kod HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Ilość zapasów", "barcode": "<PERSON><PERSON>", "countryOfOrigin": "<PERSON>raj <PERSON>", "material": "Tworzywo", "thumbnail": "Zwięzły", "sku": "SKU", "managedInventory": "Zarządzane zasoby", "allowBackorder": "Zezwól na zamówienie oczekujące", "inStock": "W magazynie", "location": "Lokalizacja", "quantity": "<PERSON><PERSON><PERSON><PERSON>", "variant": "Wariant", "id": "ID", "parent": "<PERSON><PERSON><PERSON><PERSON>", "minSubtotal": "<PERSON><PERSON> <PERSON><PERSON>", "maxSubtotal": "Maks. <PERSON> c<PERSON>ścio<PERSON>", "shippingProfile": "<PERSON><PERSON>", "summary": "Streszczenie", "details": "Bliższe dane", "label": "Etykieta", "rate": "Wskaźnik", "requiresShipping": "Wymaga wysyłki", "unitPrice": "<PERSON><PERSON>", "startDate": "Data rozpoczęcia", "endDate": "Data końcowa", "draft": "Projekt", "values": "<PERSON><PERSON><PERSON><PERSON>"}, "dateTime": {"years_one": "Rok", "years_few": "Lata", "years_many": "Lat", "years_other": "Lat", "months_one": "<PERSON><PERSON><PERSON><PERSON>", "months_few": "<PERSON><PERSON><PERSON>", "months_many": "<PERSON><PERSON><PERSON><PERSON>", "months_other": "<PERSON><PERSON><PERSON><PERSON>", "weeks_one": "Tydzień", "weeks_few": "Tygodnie", "weeks_many": "Tygodni", "weeks_other": "Tygodni", "days_one": "Dzień", "days_few": "Dni", "days_many": "Dni", "days_other": "Dni", "hours_one": "<PERSON><PERSON><PERSON>", "hours_few": "<PERSON><PERSON><PERSON>", "hours_many": "<PERSON><PERSON>", "hours_other": "<PERSON><PERSON>", "minutes_one": "<PERSON>uta", "minutes_few": "<PERSON><PERSON><PERSON>", "minutes_many": "Minut", "minutes_other": "Minut", "seconds_one": "Sekund", "seconds_few": "Sekundy", "seconds_many": "Sekund", "seconds_other": "Sekund"}}