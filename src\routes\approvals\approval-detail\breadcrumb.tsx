import { UIMatch } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { Approval } from "../../../types"

type ApprovalDetailBreadcrumbProps = UIMatch<{ approval: Approval }>

export const Breadcrumb = (props: ApprovalDetailBreadcrumbProps) => {
  const { t } = useTranslation()
  
  const approval = props.data?.approval
  
  if (!approval) {
    return t("approvals.title", "Approvals")
  }

  return `${t("approvals.title", "Approvals")} / ${approval.id.slice(-8).toUpperCase()}`
}