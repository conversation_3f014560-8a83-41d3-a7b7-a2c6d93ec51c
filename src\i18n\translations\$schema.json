{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"$schema": {"type": "string"}, "general": {"type": "object", "properties": {"ascending": {"type": "string"}, "descending": {"type": "string"}, "add": {"type": "string"}, "start": {"type": "string"}, "end": {"type": "string"}, "open": {"type": "string"}, "close": {"type": "string"}, "apply": {"type": "string"}, "range": {"type": "string"}, "search": {"type": "string"}, "of": {"type": "string"}, "results": {"type": "string"}, "pages": {"type": "string"}, "next": {"type": "string"}, "prev": {"type": "string"}, "is": {"type": "string"}, "timeline": {"type": "string"}, "success": {"type": "string"}, "warning": {"type": "string"}, "tip": {"type": "string"}, "error": {"type": "string"}, "select": {"type": "string"}, "selected": {"type": "string"}, "enabled": {"type": "string"}, "disabled": {"type": "string"}, "expired": {"type": "string"}, "active": {"type": "string"}, "revoked": {"type": "string"}, "new": {"type": "string"}, "modified": {"type": "string"}, "added": {"type": "string"}, "removed": {"type": "string"}, "admin": {"type": "string"}, "store": {"type": "string"}, "details": {"type": "string"}, "items_one": {"type": "string"}, "items_other": {"type": "string"}, "countSelected": {"type": "string"}, "countOfTotalSelected": {"type": "string"}, "plusCount": {"type": "string"}, "plusCountMore": {"type": "string"}, "areYouSure": {"type": "string"}, "areYouSureDescription": {"type": "string"}, "noRecordsFound": {"type": "string"}, "typeToConfirm": {"type": "string"}, "noResultsTitle": {"type": "string"}, "noResultsMessage": {"type": "string"}, "noSearchResults": {"type": "string"}, "noSearchResultsFor": {"type": "string"}, "noRecordsTitle": {"type": "string"}, "noRecordsMessage": {"type": "string"}, "unsavedChangesTitle": {"type": "string"}, "unsavedChangesDescription": {"type": "string"}, "includesTaxTooltip": {"type": "string"}, "excludesTaxTooltip": {"type": "string"}, "noMoreData": {"type": "string"}}, "required": ["ascending", "descending", "add", "start", "end", "open", "close", "apply", "range", "search", "of", "results", "pages", "next", "prev", "is", "timeline", "success", "warning", "tip", "error", "select", "selected", "enabled", "disabled", "expired", "active", "revoked", "new", "modified", "added", "removed", "admin", "store", "details", "items_one", "items_other", "countSelected", "countOfTotalSelected", "plusCount", "plusCountMore", "areYouSure", "areYouSureDescription", "noRecordsFound", "typeToConfirm", "noResultsTitle", "noResultsMessage", "noSearchResults", "noSearchResultsFor", "noRecordsTitle", "noRecordsMessage", "unsavedChangesTitle", "unsavedChangesDescription", "includesTaxTooltip", "excludesTaxTooltip", "noMoreData"], "additionalProperties": false}, "json": {"type": "object", "properties": {"header": {"type": "string"}, "numberOfKeys_one": {"type": "string"}, "numberOfKeys_other": {"type": "string"}, "drawer": {"type": "object", "properties": {"header_one": {"type": "string"}, "header_other": {"type": "string"}, "description": {"type": "string"}}, "required": ["header_one", "header_other", "description"], "additionalProperties": false}}, "required": ["header", "numberOfKeys_one", "numberOfKeys_other", "drawer"], "additionalProperties": false}, "metadata": {"type": "object", "properties": {"header": {"type": "string"}, "numberOfKeys_one": {"type": "string"}, "numberOfKeys_other": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}, "actions": {"type": "object", "properties": {"insertRowAbove": {"type": "string"}, "insertRowBelow": {"type": "string"}, "deleteRow": {"type": "string"}}, "required": ["insertRowAbove", "insertRowBelow", "deleteRow"], "additionalProperties": false}, "labels": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}}, "required": ["key", "value"], "additionalProperties": false}, "complexRow": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}, "tooltip": {"type": "string"}}, "required": ["label", "description", "tooltip"], "additionalProperties": false}}, "required": ["header", "description", "successToast", "actions", "labels", "complexRow"], "additionalProperties": false}}, "required": ["header", "numberOfKeys_one", "numberOfKeys_other", "edit"], "additionalProperties": false}, "validation": {"type": "object", "properties": {"mustBeInt": {"type": "string"}, "mustBePositive": {"type": "string"}}, "required": ["mustBeInt", "mustBePositive"], "additionalProperties": false}, "actions": {"type": "object", "properties": {"save": {"type": "string"}, "saveAsDraft": {"type": "string"}, "copy": {"type": "string"}, "copied": {"type": "string"}, "duplicate": {"type": "string"}, "publish": {"type": "string"}, "create": {"type": "string"}, "delete": {"type": "string"}, "remove": {"type": "string"}, "revoke": {"type": "string"}, "cancel": {"type": "string"}, "forceConfirm": {"type": "string"}, "continueEdit": {"type": "string"}, "enable": {"type": "string"}, "disable": {"type": "string"}, "undo": {"type": "string"}, "complete": {"type": "string"}, "viewDetails": {"type": "string"}, "back": {"type": "string"}, "close": {"type": "string"}, "showMore": {"type": "string"}, "continue": {"type": "string"}, "continueWithEmail": {"type": "string"}, "idCopiedToClipboard": {"type": "string"}, "addReason": {"type": "string"}, "addNote": {"type": "string"}, "reset": {"type": "string"}, "confirm": {"type": "string"}, "edit": {"type": "string"}, "addItems": {"type": "string"}, "download": {"type": "string"}, "clear": {"type": "string"}, "clearAll": {"type": "string"}, "apply": {"type": "string"}, "add": {"type": "string"}, "select": {"type": "string"}, "browse": {"type": "string"}, "logout": {"type": "string"}, "hide": {"type": "string"}, "export": {"type": "string"}, "import": {"type": "string"}, "cannotUndo": {"type": "string"}}, "required": ["save", "saveAsDraft", "copy", "copied", "duplicate", "publish", "create", "delete", "remove", "revoke", "cancel", "forceConfirm", "continueEdit", "enable", "disable", "undo", "complete", "viewDetails", "back", "close", "showMore", "continue", "continueWithEmail", "idCopiedToClipboard", "addReason", "addNote", "reset", "confirm", "edit", "addItems", "download", "clear", "clearAll", "apply", "add", "select", "browse", "logout", "hide", "export", "import", "cannotUndo"], "additionalProperties": false}, "operators": {"type": "object", "properties": {"in": {"type": "string"}}, "required": ["in"], "additionalProperties": false}, "app": {"type": "object", "properties": {"search": {"type": "object", "properties": {"label": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "allAreas": {"type": "string"}, "navigation": {"type": "string"}, "openResult": {"type": "string"}, "showMore": {"type": "string"}, "placeholder": {"type": "string"}, "noResultsTitle": {"type": "string"}, "noResultsMessage": {"type": "string"}, "emptySearchTitle": {"type": "string"}, "emptySearchMessage": {"type": "string"}, "loadMore": {"type": "string"}, "groups": {"type": "object", "properties": {"all": {"type": "string"}, "customer": {"type": "string"}, "customerGroup": {"type": "string"}, "product": {"type": "string"}, "productVariant": {"type": "string"}, "inventory": {"type": "string"}, "reservation": {"type": "string"}, "category": {"type": "string"}, "collection": {"type": "string"}, "order": {"type": "string"}, "promotion": {"type": "string"}, "campaign": {"type": "string"}, "priceList": {"type": "string"}, "user": {"type": "string"}, "region": {"type": "string"}, "taxRegion": {"type": "string"}, "returnReason": {"type": "string"}, "salesChannel": {"type": "string"}, "productType": {"type": "string"}, "productTag": {"type": "string"}, "location": {"type": "string"}, "shippingProfile": {"type": "string"}, "publishableApiKey": {"type": "string"}, "secretApiKey": {"type": "string"}, "command": {"type": "string"}, "navigation": {"type": "string"}}, "required": ["all", "customer", "customerGroup", "product", "productVariant", "inventory", "reservation", "category", "collection", "order", "promotion", "campaign", "priceList", "user", "region", "taxRegion", "returnReason", "salesChannel", "productType", "productTag", "location", "shippingProfile", "publishableApiKey", "secretApiKey", "command", "navigation"], "additionalProperties": false}}, "required": ["label", "title", "description", "allAreas", "navigation", "openResult", "showMore", "placeholder", "noResultsTitle", "noResultsMessage", "emptySearchTitle", "emptySearchMessage", "loadMore", "groups"], "additionalProperties": false}, "keyboardShortcuts": {"type": "object", "properties": {"pageShortcut": {"type": "string"}, "settingShortcut": {"type": "string"}, "commandShortcut": {"type": "string"}, "then": {"type": "string"}, "navigation": {"type": "object", "properties": {"goToOrders": {"type": "string"}, "goToProducts": {"type": "string"}, "goToCollections": {"type": "string"}, "goToCategories": {"type": "string"}, "goToCustomers": {"type": "string"}, "goToCustomerGroups": {"type": "string"}, "goToInventory": {"type": "string"}, "goToReservations": {"type": "string"}, "goToPriceLists": {"type": "string"}, "goToPromotions": {"type": "string"}, "goToCampaigns": {"type": "string"}}, "required": ["goToOrders", "goToProducts", "goToCollections", "goToCategories", "goToCustomers", "goToCustomerGroups", "goToInventory", "goToReservations", "goToPriceLists", "goToPromotions", "goToCampaigns"], "additionalProperties": false}, "settings": {"type": "object", "properties": {"goToSettings": {"type": "string"}, "goToStore": {"type": "string"}, "goToUsers": {"type": "string"}, "goToRegions": {"type": "string"}, "goToTaxRegions": {"type": "string"}, "goToSalesChannels": {"type": "string"}, "goToProductTypes": {"type": "string"}, "goToLocations": {"type": "string"}, "goToPublishableApiKeys": {"type": "string"}, "goToSecretApiKeys": {"type": "string"}, "goToWorkflows": {"type": "string"}, "goToProfile": {"type": "string"}, "goToReturnReasons": {"type": "string"}}, "required": ["goToSettings", "goToStore", "goToUsers", "goToRegions", "goToTaxRegions", "goToSalesChannels", "goToProductTypes", "goToLocations", "goToPublishableApiKeys", "goToSecretApiKeys", "goToWorkflows", "goToProfile", "goToReturnReasons"], "additionalProperties": false}}, "required": ["pageShortcut", "settingShortcut", "commandShortcut", "then", "navigation", "settings"], "additionalProperties": false}, "menus": {"type": "object", "properties": {"user": {"type": "object", "properties": {"documentation": {"type": "string"}, "changelog": {"type": "string"}, "shortcuts": {"type": "string"}, "profileSettings": {"type": "string"}, "theme": {"type": "object", "properties": {"label": {"type": "string"}, "dark": {"type": "string"}, "light": {"type": "string"}, "system": {"type": "string"}}, "required": ["label", "dark", "light", "system"], "additionalProperties": false}}, "required": ["documentation", "changelog", "shortcuts", "profileSettings", "theme"], "additionalProperties": false}, "store": {"type": "object", "properties": {"label": {"type": "string"}, "storeSettings": {"type": "string"}}, "required": ["label", "storeSettings"], "additionalProperties": false}, "actions": {"type": "object", "properties": {"logout": {"type": "string"}}, "required": ["logout"], "additionalProperties": false}}, "required": ["user", "store", "actions"], "additionalProperties": false}, "nav": {"type": "object", "properties": {"accessibility": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "common": {"type": "object", "properties": {"extensions": {"type": "string"}}, "required": ["extensions"], "additionalProperties": false}, "main": {"type": "object", "properties": {"store": {"type": "string"}, "storeSettings": {"type": "string"}}, "required": ["store", "storeSettings"], "additionalProperties": false}, "settings": {"type": "object", "properties": {"header": {"type": "string"}, "general": {"type": "string"}, "developer": {"type": "string"}, "myAccount": {"type": "string"}}, "required": ["header", "general", "developer", "myAccount"], "additionalProperties": false}}, "required": ["accessibility", "common", "main", "settings"], "additionalProperties": false}}, "required": ["search", "keyboardShortcuts", "menus", "nav"], "additionalProperties": false}, "dataGrid": {"type": "object", "properties": {"columns": {"type": "object", "properties": {"view": {"type": "string"}, "resetToDefault": {"type": "string"}, "disabled": {"type": "string"}}, "required": ["view", "resetToDefault", "disabled"], "additionalProperties": false}, "shortcuts": {"type": "object", "properties": {"label": {"type": "string"}, "commands": {"type": "object", "properties": {"undo": {"type": "string"}, "redo": {"type": "string"}, "copy": {"type": "string"}, "paste": {"type": "string"}, "edit": {"type": "string"}, "delete": {"type": "string"}, "clear": {"type": "string"}, "moveUp": {"type": "string"}, "moveDown": {"type": "string"}, "moveLeft": {"type": "string"}, "moveRight": {"type": "string"}, "moveTop": {"type": "string"}, "moveBottom": {"type": "string"}, "selectDown": {"type": "string"}, "selectUp": {"type": "string"}, "selectColumnDown": {"type": "string"}, "selectColumnUp": {"type": "string"}, "focusToolbar": {"type": "string"}, "focusCancel": {"type": "string"}}, "required": ["undo", "redo", "copy", "paste", "edit", "delete", "clear", "moveUp", "moveDown", "moveLeft", "moveRight", "moveTop", "moveBottom", "selectDown", "selectUp", "selectColumnDown", "selectColumnUp", "focusToolbar", "focusCancel"], "additionalProperties": false}}, "required": ["label", "commands"], "additionalProperties": false}, "errors": {"type": "object", "properties": {"fixError": {"type": "string"}, "count_one": {"type": "string"}, "count_other": {"type": "string"}}, "required": ["fixError", "count_one", "count_other"], "additionalProperties": false}}, "required": ["columns", "shortcuts", "errors"], "additionalProperties": false}, "filters": {"type": "object", "properties": {"sortLabel": {"type": "string"}, "filterLabel": {"type": "string"}, "searchLabel": {"type": "string"}, "date": {"type": "object", "properties": {"today": {"type": "string"}, "lastSevenDays": {"type": "string"}, "lastThirtyDays": {"type": "string"}, "lastNinetyDays": {"type": "string"}, "lastTwelveMonths": {"type": "string"}, "custom": {"type": "string"}, "from": {"type": "string"}, "to": {"type": "string"}, "starting": {"type": "string"}, "ending": {"type": "string"}}, "required": ["today", "lastSevenDays", "lastThirtyDays", "lastNinetyDays", "lastTwelveMonths", "custom", "from", "to", "starting", "ending"], "additionalProperties": false}, "compare": {"type": "object", "properties": {"lessThan": {"type": "string"}, "greaterThan": {"type": "string"}, "exact": {"type": "string"}, "range": {"type": "string"}, "lessThanLabel": {"type": "string"}, "greaterThanLabel": {"type": "string"}, "andLabel": {"type": "string"}}, "required": ["lessThan", "greaterThan", "exact", "range", "lessThanLabel", "greaterThanLabel", "and<PERSON><PERSON><PERSON>"], "additionalProperties": false}, "sorting": {"type": "object", "properties": {"alphabeticallyAsc": {"type": "string"}, "alphabeticallyDesc": {"type": "string"}, "dateAsc": {"type": "string"}, "dateDesc": {"type": "string"}}, "required": ["alphabeticallyAsc", "alphabeticallyDesc", "dateAsc", "dateDesc"], "additionalProperties": false}, "radio": {"type": "object", "properties": {"yes": {"type": "string"}, "no": {"type": "string"}, "true": {"type": "string"}, "false": {"type": "string"}}, "required": ["yes", "no", "true", "false"], "additionalProperties": false}, "addFilter": {"type": "string"}}, "required": ["sortLabel", "filterLabel", "searchLabel", "date", "compare", "sorting", "radio", "addFilter"], "additionalProperties": false}, "errorBoundary": {"type": "object", "properties": {"badRequestTitle": {"type": "string"}, "badRequestMessage": {"type": "string"}, "notFoundTitle": {"type": "string"}, "notFoundMessage": {"type": "string"}, "internalServerErrorTitle": {"type": "string"}, "internalServerErrorMessage": {"type": "string"}, "defaultTitle": {"type": "string"}, "defaultMessage": {"type": "string"}, "noMatchMessage": {"type": "string"}, "backToDashboard": {"type": "string"}}, "required": ["badRequestTitle", "badRequestMessage", "notFoundTitle", "notFoundMessage", "internalServerErrorTitle", "internalServerErrorMessage", "defaultTitle", "defaultMessage", "noMatchMessage", "backToDashboard"], "additionalProperties": false}, "addresses": {"type": "object", "properties": {"title": {"type": "string"}, "shippingAddress": {"type": "object", "properties": {"header": {"type": "string"}, "editHeader": {"type": "string"}, "editLabel": {"type": "string"}, "label": {"type": "string"}}, "required": ["header", "<PERSON><PERSON><PERSON><PERSON>", "edit<PERSON><PERSON><PERSON>", "label"], "additionalProperties": false}, "billingAddress": {"type": "object", "properties": {"header": {"type": "string"}, "editHeader": {"type": "string"}, "editLabel": {"type": "string"}, "label": {"type": "string"}, "sameAsShipping": {"type": "string"}}, "required": ["header", "<PERSON><PERSON><PERSON><PERSON>", "edit<PERSON><PERSON><PERSON>", "label", "sameAsShipping"], "additionalProperties": false}, "contactHeading": {"type": "string"}, "locationHeading": {"type": "string"}}, "required": ["title", "shippingAddress", "billing<PERSON><PERSON>ress", "contactHeading", "locationHeading"], "additionalProperties": false}, "email": {"type": "object", "properties": {"editHeader": {"type": "string"}, "editLabel": {"type": "string"}, "label": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "edit<PERSON><PERSON><PERSON>", "label"], "additionalProperties": false}, "transferOwnership": {"type": "object", "properties": {"header": {"type": "string"}, "label": {"type": "string"}, "details": {"type": "object", "properties": {"order": {"type": "string"}, "draft": {"type": "string"}}, "required": ["order", "draft"], "additionalProperties": false}, "currentOwner": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "newOwner": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "validation": {"type": "object", "properties": {"mustBeDifferent": {"type": "string"}, "required": {"type": "string"}}, "required": ["mustBeDifferent", "required"], "additionalProperties": false}}, "required": ["header", "label", "details", "current<PERSON>wner", "new<PERSON>wner", "validation"], "additionalProperties": false}, "sales_channels": {"type": "object", "properties": {"availableIn": {"type": "string"}}, "required": ["availableIn"], "additionalProperties": false}, "products": {"type": "object", "properties": {"domain": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}, "create": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "header": {"type": "string"}, "tabs": {"type": "object", "properties": {"details": {"type": "string"}, "organize": {"type": "string"}, "variants": {"type": "string"}, "inventory": {"type": "string"}}, "required": ["details", "organize", "variants", "inventory"], "additionalProperties": false}, "errors": {"type": "object", "properties": {"variants": {"type": "string"}, "options": {"type": "string"}, "uniqueSku": {"type": "string"}}, "required": ["variants", "options", "uniqueSku"], "additionalProperties": false}, "inventory": {"type": "object", "properties": {"heading": {"type": "string"}, "label": {"type": "string"}, "itemPlaceholder": {"type": "string"}, "quantityPlaceholder": {"type": "string"}}, "required": ["heading", "label", "itemPlaceholder", "quantityPlaceholder"], "additionalProperties": false}, "variants": {"type": "object", "properties": {"header": {"type": "string"}, "subHeadingTitle": {"type": "string"}, "subHeadingDescription": {"type": "string"}, "optionTitle": {"type": "object", "properties": {"placeholder": {"type": "string"}}, "required": ["placeholder"], "additionalProperties": false}, "optionValues": {"type": "object", "properties": {"placeholder": {"type": "string"}}, "required": ["placeholder"], "additionalProperties": false}, "productVariants": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "alert": {"type": "string"}, "tip": {"type": "string"}}, "required": ["label", "hint", "alert", "tip"], "additionalProperties": false}, "productOptions": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}}, "required": ["header", "subHeadingTitle", "subHeadingDescription", "optionTitle", "optionValues", "productVariants", "productOptions"], "additionalProperties": false}, "successToast": {"type": "string"}}, "required": ["title", "description", "header", "tabs", "errors", "inventory", "variants", "successToast"], "additionalProperties": false}, "export": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "success": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "filters": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "columns": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["header", "description", "success", "filters", "columns"], "additionalProperties": false}, "import": {"type": "object", "properties": {"header": {"type": "string"}, "uploadLabel": {"type": "string"}, "uploadHint": {"type": "string"}, "description": {"type": "string"}, "template": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "upload": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "preprocessing": {"type": "string"}, "productsToCreate": {"type": "string"}, "productsToUpdate": {"type": "string"}}, "required": ["title", "description", "preprocessing", "productsToCreate", "productsToUpdate"], "additionalProperties": false}, "success": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["header", "uploadLabel", "uploadHint", "description", "template", "upload", "success"], "additionalProperties": false}, "deleteWarning": {"type": "string"}, "variants": {"type": "object", "properties": {"header": {"type": "string"}, "empty": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}, "filtered": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}}, "required": ["header", "empty", "filtered"], "additionalProperties": false}, "attributes": {"type": "string"}, "editAttributes": {"type": "string"}, "editOptions": {"type": "string"}, "editPrices": {"type": "string"}, "media": {"type": "object", "properties": {"label": {"type": "string"}, "editHint": {"type": "string"}, "makeThumbnail": {"type": "string"}, "uploadImagesLabel": {"type": "string"}, "uploadImagesHint": {"type": "string"}, "invalidFileType": {"type": "string"}, "failedToUpload": {"type": "string"}, "deleteWarning_one": {"type": "string"}, "deleteWarning_other": {"type": "string"}, "deleteWarningWithThumbnail_one": {"type": "string"}, "deleteWarningWithThumbnail_other": {"type": "string"}, "thumbnailTooltip": {"type": "string"}, "galleryLabel": {"type": "string"}, "downloadImageLabel": {"type": "string"}, "deleteImageLabel": {"type": "string"}, "emptyState": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "action": {"type": "string"}}, "required": ["header", "description", "action"], "additionalProperties": false}, "successToast": {"type": "string"}}, "required": ["label", "editHint", "makeThumbnail", "uploadImagesLabel", "uploadImagesHint", "invalidFileType", "failedToUpload", "deleteWarning_one", "deleteWarning_other", "deleteWarningWithThumbnail_one", "deleteWarningWithThumbnail_other", "thumbnailTooltip", "galleryLabel", "downloadImageLabel", "deleteImageLabel", "emptyState", "successToast"], "additionalProperties": false}, "discountableHint": {"type": "string"}, "noSalesChannels": {"type": "string"}, "variantCount_one": {"type": "string"}, "variantCount_other": {"type": "string"}, "deleteVariantWarning": {"type": "string"}, "productStatus": {"type": "object", "properties": {"draft": {"type": "string"}, "published": {"type": "string"}, "proposed": {"type": "string"}, "rejected": {"type": "string"}}, "required": ["draft", "published", "proposed", "rejected"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"title": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "hint", "placeholder"], "additionalProperties": false}, "subtitle": {"type": "object", "properties": {"label": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "placeholder"], "additionalProperties": false}, "handle": {"type": "object", "properties": {"label": {"type": "string"}, "tooltip": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "tooltip", "placeholder"], "additionalProperties": false}, "description": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "hint", "placeholder"], "additionalProperties": false}, "discountable": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "shipping_profile": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "type": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "collection": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "categories": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "tags": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "sales_channels": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "countryOrigin": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "material": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "width": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "length": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "height": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "weight": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "options": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "add": {"type": "string"}, "optionTitle": {"type": "string"}, "optionTitlePlaceholder": {"type": "string"}, "variations": {"type": "string"}, "variantionsPlaceholder": {"type": "string"}}, "required": ["label", "hint", "add", "optionTitle", "optionTitlePlaceholder", "variations", "variantionsPlaceholder"], "additionalProperties": false}, "variants": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "mid_code": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "hs_code": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}}, "required": ["title", "subtitle", "handle", "description", "discountable", "shipping_profile", "type", "collection", "categories", "tags", "sales_channels", "country<PERSON><PERSON><PERSON>", "material", "width", "length", "height", "weight", "options", "variants", "mid_code", "hs_code"], "additionalProperties": false}, "variant": {"type": "object", "properties": {"edit": {"type": "object", "properties": {"header": {"type": "string"}, "success": {"type": "string"}}, "required": ["header", "success"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "deleteWarning": {"type": "string"}, "pricesPagination": {"type": "string"}, "tableItemAvailable": {"type": "string"}, "tableItem_one": {"type": "string"}, "tableItem_other": {"type": "string"}, "inventory": {"type": "object", "properties": {"notManaged": {"type": "string"}, "manageItems": {"type": "string"}, "notManagedDesc": {"type": "string"}, "manageKit": {"type": "string"}, "navigateToItem": {"type": "string"}, "actions": {"type": "object", "properties": {"inventoryItems": {"type": "string"}, "inventoryKit": {"type": "string"}}, "required": ["inventoryItems", "inventoryKit"], "additionalProperties": false}, "inventoryKit": {"type": "string"}, "inventoryKitHint": {"type": "string"}, "validation": {"type": "object", "properties": {"itemId": {"type": "string"}, "quantity": {"type": "string"}}, "required": ["itemId", "quantity"], "additionalProperties": false}, "header": {"type": "string"}, "editItemDetails": {"type": "string"}, "manageInventoryLabel": {"type": "string"}, "manageInventoryHint": {"type": "string"}, "allowBackordersLabel": {"type": "string"}, "allowBackordersHint": {"type": "string"}, "toast": {"type": "object", "properties": {"levelsBatch": {"type": "string"}, "update": {"type": "string"}, "updateLevel": {"type": "string"}, "itemsManageSuccess": {"type": "string"}}, "required": ["levelsBatch", "update", "updateLevel", "itemsManageSuccess"], "additionalProperties": false}}, "required": ["notManaged", "manageItems", "notManagedDesc", "manageKit", "navigateToItem", "actions", "inventoryKit", "inventoryKitHint", "validation", "header", "editItemDetails", "manageInventoryLabel", "manageInventoryHint", "allowBackordersLabel", "allowBackordersHint", "toast"], "additionalProperties": false}}, "required": ["edit", "create", "deleteWarning", "pricesPagination", "tableItemAvailable", "tableItem_one", "tableItem_other", "inventory"], "additionalProperties": false}, "options": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "deleteWarning": {"type": "string"}}, "required": ["header", "edit", "create", "deleteWarning"], "additionalProperties": false}, "organization": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "toasts": {"type": "object", "properties": {"success": {"type": "string"}}, "required": ["success"], "additionalProperties": false}}, "required": ["header", "toasts"], "additionalProperties": false}}, "required": ["header", "edit"], "additionalProperties": false}, "stock": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}, "loading": {"type": "string"}, "tooltips": {"type": "object", "properties": {"alreadyManaged": {"type": "string"}, "alreadyManagedWithSku": {"type": "string"}}, "required": ["alreadyManaged", "alreadyManagedWithSku"], "additionalProperties": false}}, "required": ["heading", "description", "loading", "tooltips"], "additionalProperties": false}, "shippingProfile": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "toasts": {"type": "object", "properties": {"success": {"type": "string"}}, "required": ["success"], "additionalProperties": false}}, "required": ["header", "toasts"], "additionalProperties": false}, "create": {"type": "object", "properties": {"errors": {"type": "object", "properties": {"required": {"type": "string"}}, "required": ["required"], "additionalProperties": false}}, "required": ["errors"], "additionalProperties": false}}, "required": ["header", "edit", "create"], "additionalProperties": false}, "toasts": {"type": "object", "properties": {"delete": {"type": "object", "properties": {"success": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}}, "required": ["header", "description"], "additionalProperties": false}, "error": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}}, "required": ["success", "error"], "additionalProperties": false}}, "required": ["delete"], "additionalProperties": false}}, "required": ["domain", "list", "edit", "create", "export", "import", "deleteWarning", "variants", "attributes", "editAttributes", "editOptions", "editPrices", "media", "discountableHint", "noSalesChannels", "variantCount_one", "variantCount_other", "deleteVariantWarning", "productStatus", "fields", "variant", "options", "organization", "stock", "shippingProfile", "toasts"], "additionalProperties": false}, "collections": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "createCollection": {"type": "string"}, "createCollectionHint": {"type": "string"}, "createSuccess": {"type": "string"}, "editCollection": {"type": "string"}, "handleTooltip": {"type": "string"}, "deleteWarning": {"type": "string"}, "removeSingleProductWarning": {"type": "string"}, "removeProductsWarning_one": {"type": "string"}, "removeProductsWarning_other": {"type": "string"}, "products": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "add": {"type": "object", "properties": {"successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}}, "required": ["successToast_one", "successToast_other"], "additionalProperties": false}, "remove": {"type": "object", "properties": {"successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}}, "required": ["successToast_one", "successToast_other"], "additionalProperties": false}}, "required": ["list", "add", "remove"], "additionalProperties": false}}, "required": ["domain", "subtitle", "createCollection", "createCollectionHint", "createSuccess", "editCollection", "handleTooltip", "deleteWarning", "removeSingleProductWarning", "removeProductsWarning_one", "removeProductsWarning_other", "products"], "additionalProperties": false}, "categories": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "tabs": {"type": "object", "properties": {"details": {"type": "string"}, "organize": {"type": "string"}}, "required": ["details", "organize"], "additionalProperties": false}, "successToast": {"type": "string"}}, "required": ["header", "hint", "tabs", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "products": {"type": "object", "properties": {"add": {"type": "object", "properties": {"disabledTooltip": {"type": "string"}, "successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}}, "required": ["disabledTooltip", "successToast_one", "successToast_other"], "additionalProperties": false}, "remove": {"type": "object", "properties": {"confirmation_one": {"type": "string"}, "confirmation_other": {"type": "string"}, "successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}}, "required": ["confirmation_one", "confirmation_other", "successToast_one", "successToast_other"], "additionalProperties": false}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["add", "remove", "list"], "additionalProperties": false}, "organize": {"type": "object", "properties": {"header": {"type": "string"}, "action": {"type": "string"}}, "required": ["header", "action"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"visibility": {"type": "object", "properties": {"label": {"type": "string"}, "internal": {"type": "string"}, "public": {"type": "string"}}, "required": ["label", "internal", "public"], "additionalProperties": false}, "status": {"type": "object", "properties": {"label": {"type": "string"}, "active": {"type": "string"}, "inactive": {"type": "string"}}, "required": ["label", "active", "inactive"], "additionalProperties": false}, "path": {"type": "object", "properties": {"label": {"type": "string"}, "tooltip": {"type": "string"}}, "required": ["label", "tooltip"], "additionalProperties": false}, "children": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}, "new": {"type": "object", "properties": {"label": {"type": "string"}}, "required": ["label"], "additionalProperties": false}}, "required": ["visibility", "status", "path", "children", "new"], "additionalProperties": false}}, "required": ["domain", "subtitle", "create", "edit", "delete", "products", "organize", "fields"], "additionalProperties": false}, "inventory": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "reserved": {"type": "string"}, "available": {"type": "string"}, "locationLevels": {"type": "string"}, "associatedVariants": {"type": "string"}, "manageLocations": {"type": "string"}, "manageLocationQuantity": {"type": "string"}, "deleteWarning": {"type": "string"}, "editItemDetails": {"type": "string"}, "quantityAcrossLocations": {"type": "string"}, "create": {"type": "object", "properties": {"title": {"type": "string"}, "details": {"type": "string"}, "availability": {"type": "string"}, "locations": {"type": "string"}, "attributes": {"type": "string"}, "requiresShipping": {"type": "string"}, "requiresShippingHint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "details", "availability", "locations", "attributes", "requiresShipping", "requiresShippingHint", "successToast"], "additionalProperties": false}, "reservation": {"type": "object", "properties": {"header": {"type": "string"}, "editItemDetails": {"type": "string"}, "lineItemId": {"type": "string"}, "orderID": {"type": "string"}, "description": {"type": "string"}, "location": {"type": "string"}, "inStockAtLocation": {"type": "string"}, "availableAtLocation": {"type": "string"}, "reservedAtLocation": {"type": "string"}, "reservedAmount": {"type": "string"}, "create": {"type": "string"}, "itemToReserve": {"type": "string"}, "quantityPlaceholder": {"type": "string"}, "descriptionPlaceholder": {"type": "string"}, "successToast": {"type": "string"}, "updateSuccessToast": {"type": "string"}, "deleteSuccessToast": {"type": "string"}, "errors": {"type": "object", "properties": {"noAvaliableQuantity": {"type": "string"}, "quantityOutOfRange": {"type": "string"}}, "required": ["noAvaliableQuantity", "quantityOutOfRange"], "additionalProperties": false}}, "required": ["header", "editItemDetails", "lineItemId", "orderID", "description", "location", "inStockAtLocation", "availableAtLocation", "reservedAtLocation", "reservedAmount", "create", "itemToReserve", "quantityPlaceholder", "descriptionPlaceholder", "successToast", "updateSuccessToast", "deleteSuccessToast", "errors"], "additionalProperties": false}, "adjustInventory": {"type": "object", "properties": {"errors": {"type": "object", "properties": {"stockedQuantity": {"type": "string"}}, "required": ["stockedQuantity"], "additionalProperties": false}}, "required": ["errors"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"updateLocations": {"type": "string"}, "updateLevel": {"type": "string"}, "updateItem": {"type": "string"}}, "required": ["updateLocations", "updateLevel", "updateItem"], "additionalProperties": false}, "stock": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "action": {"type": "string"}, "placeholder": {"type": "string"}, "disablePrompt_one": {"type": "string"}, "disablePrompt_other": {"type": "string"}, "disabledToggleTooltip": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "action", "placeholder", "disablePrompt_one", "disablePrompt_other", "disabledToggleTooltip", "successToast"], "additionalProperties": false}}, "required": ["domain", "subtitle", "reserved", "available", "locationLevels", "associatedVariants", "manageLocations", "manageLocationQuantity", "deleteWarning", "editItemDetails", "quantityAcrossLocations", "create", "reservation", "adjustInventory", "toast", "stock"], "additionalProperties": false}, "giftCards": {"type": "object", "properties": {"domain": {"type": "string"}, "editGiftCard": {"type": "string"}, "createGiftCard": {"type": "string"}, "createGiftCardHint": {"type": "string"}, "selectRegionFirst": {"type": "string"}, "deleteGiftCardWarning": {"type": "string"}, "balanceHigherThanValue": {"type": "string"}, "balanceLowerThanZero": {"type": "string"}, "expiryDateHint": {"type": "string"}, "regionHint": {"type": "string"}, "enabledHint": {"type": "string"}, "balance": {"type": "string"}, "currentBalance": {"type": "string"}, "initialBalance": {"type": "string"}, "personalMessage": {"type": "string"}, "recipient": {"type": "string"}}, "required": ["domain", "editGiftCard", "createGiftCard", "createGiftCardHint", "selectRegionFirst", "deleteGiftCardWarning", "balance<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "balanceLowerThanZero", "expiryDateHint", "regionHint", "enabledHint", "balance", "currentBalance", "initialBalance", "personalMessage", "recipient"], "additionalProperties": false}, "customers": {"type": "object", "properties": {"domain": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "groups": {"type": "object", "properties": {"label": {"type": "string"}, "remove": {"type": "string"}, "removeMany": {"type": "string"}, "alreadyAddedTooltip": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "add": {"type": "object", "properties": {"success": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["success", "list"], "additionalProperties": false}, "removed": {"type": "object", "properties": {"success": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["success", "list"], "additionalProperties": false}}, "required": ["label", "remove", "remove<PERSON>any", "alreadyAddedTooltip", "list", "add", "removed"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "emailDisabledTooltip": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "emailDisabledTooltip", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"guest": {"type": "string"}, "registered": {"type": "string"}, "groups": {"type": "string"}}, "required": ["guest", "registered", "groups"], "additionalProperties": false}, "registered": {"type": "string"}, "guest": {"type": "string"}, "hasAccount": {"type": "string"}, "addresses": {"type": "object", "properties": {"title": {"type": "string"}, "fields": {"type": "object", "properties": {"addressName": {"type": "string"}, "address1": {"type": "string"}, "address2": {"type": "string"}, "city": {"type": "string"}, "province": {"type": "string"}, "postalCode": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "company": {"type": "string"}, "countryCode": {"type": "string"}, "provinceCode": {"type": "string"}}, "required": ["addressName", "address1", "address2", "city", "province", "postalCode", "country", "phone", "company", "countryCode", "provinceCode"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}}, "required": ["title", "fields", "create"], "additionalProperties": false}}, "required": ["domain", "list", "create", "groups", "edit", "delete", "fields", "registered", "guest", "hasAccount", "addresses"], "additionalProperties": false}, "customerGroups": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "list": {"type": "object", "properties": {"empty": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}, "filtered": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}}, "required": ["empty", "filtered"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "successToast"], "additionalProperties": false}, "customers": {"type": "object", "properties": {"alreadyAddedTooltip": {"type": "string"}, "add": {"type": "object", "properties": {"successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["successToast_one", "successToast_other", "list"], "additionalProperties": false}, "remove": {"type": "object", "properties": {"title_one": {"type": "string"}, "title_other": {"type": "string"}, "description_one": {"type": "string"}, "description_other": {"type": "string"}}, "required": ["title_one", "title_other", "description_one", "description_other"], "additionalProperties": false}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["alreadyAddedTooltip", "add", "remove", "list"], "additionalProperties": false}}, "required": ["domain", "subtitle", "list", "create", "edit", "delete", "customers"], "additionalProperties": false}, "orders": {"type": "object", "properties": {"giftCardsStoreCreditLines": {"type": "string"}, "creditLines": {"type": "object", "properties": {"title": {"type": "string"}, "total": {"type": "string"}, "creditOrDebit": {"type": "string"}, "createCreditLine": {"type": "string"}, "createCreditLineSuccess": {"type": "string"}, "createCreditLineError": {"type": "string"}, "createCreditLineDescription": {"type": "string"}, "operation": {"type": "string"}, "credit": {"type": "string"}, "debit": {"type": "string"}, "debitDescription": {"type": "string"}, "creditDescription": {"type": "string"}}}, "balanceSettlement": {"type": "object", "properties": {"title": {"type": "string"}, "settlementType": {"type": "string"}, "settlementTypes": {"type": "object", "properties": {"paymentMethod": {"type": "string"}, "paymentMethodDescription": {"type": "string"}, "creditLine": {"type": "string"}, "creditLineDescription": {"type": "string"}}}}}, "domain": {"type": "string"}, "claim": {"type": "string"}, "exchange": {"type": "string"}, "return": {"type": "string"}, "cancelWarning": {"type": "string"}, "orderCanceled": {"type": "string"}, "onDateFromSalesChannel": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "status": {"type": "object", "properties": {"not_paid": {"type": "string"}, "pending": {"type": "string"}, "completed": {"type": "string"}, "draft": {"type": "string"}, "archived": {"type": "string"}, "canceled": {"type": "string"}, "requires_action": {"type": "string"}}, "required": ["not_paid", "pending", "completed", "draft", "archived", "canceled", "requires_action"], "additionalProperties": false}, "summary": {"type": "object", "properties": {"requestReturn": {"type": "string"}, "allocateItems": {"type": "string"}, "editOrder": {"type": "string"}, "editOrderContinue": {"type": "string"}, "inventoryKit": {"type": "string"}, "itemTotal": {"type": "string"}, "shippingTotal": {"type": "string"}, "discountTotal": {"type": "string"}, "taxTotalIncl": {"type": "string"}, "itemSubtotal": {"type": "string"}, "shippingSubtotal": {"type": "string"}, "discountSubtotal": {"type": "string"}, "taxTotal": {"type": "string"}}, "required": ["requestReturn", "allocateItems", "editOrder", "editOrderContinue", "inventoryKit", "itemTotal", "shippingTotal", "discountTotal", "taxTotalIncl", "itemSubtotal", "shippingSubtotal", "discountSubtotal", "taxTotal"], "additionalProperties": false}, "transfer": {"type": "object", "properties": {"title": {"type": "string"}, "requestSuccess": {"type": "string"}, "currentOwner": {"type": "string"}, "newOwner": {"type": "string"}, "currentOwnerDescription": {"type": "string"}, "newOwnerDescription": {"type": "string"}}, "required": ["title", "requestSuccess", "current<PERSON>wner", "new<PERSON>wner", "currentOwnerDescription", "newOwnerDescription"], "additionalProperties": false}, "payment": {"type": "object", "properties": {"title": {"type": "string"}, "isReadyToBeCaptured": {"type": "string"}, "totalPaidByCustomer": {"type": "string"}, "totalStoreCreditRefunds": {"type": "string"}, "capture": {"type": "string"}, "capture_short": {"type": "string"}, "refund": {"type": "string"}, "markAsPaid": {"type": "string"}, "statusLabel": {"type": "string"}, "statusTitle": {"type": "string"}, "status": {"type": "object", "properties": {"notPaid": {"type": "string"}, "authorized": {"type": "string"}, "partiallyAuthorized": {"type": "string"}, "awaiting": {"type": "string"}, "captured": {"type": "string"}, "partiallyRefunded": {"type": "string"}, "partiallyCaptured": {"type": "string"}, "refunded": {"type": "string"}, "canceled": {"type": "string"}, "requiresAction": {"type": "string"}}, "required": ["notPaid", "authorized", "partiallyAuthorized", "awaiting", "captured", "partiallyRefunded", "partiallyCaptured", "refunded", "canceled", "requiresAction"], "additionalProperties": false}, "capturePayment": {"type": "string"}, "capturePaymentSuccess": {"type": "string"}, "markAsPaidPayment": {"type": "string"}, "markAsPaidPaymentSuccess": {"type": "string"}, "createRefund": {"type": "string"}, "refundPaymentSuccess": {"type": "string"}, "createRefundWrongQuantity": {"type": "string"}, "refundAmount": {"type": "string"}, "paymentLink": {"type": "string"}, "selectPaymentToRefund": {"type": "string"}}, "required": ["title", "isReadyToBeCaptured", "totalPaidByCustomer", "totalStoreCreditRefunds", "capture", "capture_short", "refund", "markAsPaid", "statusLabel", "statusTitle", "status", "capturePayment", "capturePaymentSuccess", "markAsPaidPayment", "markAsPaidPaymentSuccess", "createRefund", "refundPaymentSuccess", "createRefundWrongQuantity", "refundAmount", "paymentLink", "selectPaymentToRefund"], "additionalProperties": false}, "edits": {"type": "object", "properties": {"title": {"type": "string"}, "confirm": {"type": "string"}, "confirmText": {"type": "string"}, "cancel": {"type": "string"}, "currentItems": {"type": "string"}, "currentItemsDescription": {"type": "string"}, "addItemsDescription": {"type": "string"}, "addItems": {"type": "string"}, "amountPaid": {"type": "string"}, "newTotal": {"type": "string"}, "differenceDue": {"type": "string"}, "create": {"type": "string"}, "currentTotal": {"type": "string"}, "noteHint": {"type": "string"}, "cancelSuccessToast": {"type": "string"}, "createSuccessToast": {"type": "string"}, "activeChangeError": {"type": "string"}, "panel": {"type": "object", "properties": {"title": {"type": "string"}, "titlePending": {"type": "string"}}, "required": ["title", "titlePending"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"canceledSuccessfully": {"type": "string"}, "confirmedSuccessfully": {"type": "string"}}, "required": ["canceledSuccessfully", "confirmedSuccessfully"], "additionalProperties": false}, "validation": {"type": "object", "properties": {"quantityLowerThanFulfillment": {"type": "string"}}, "required": ["quantityLowerThanFulfillment"], "additionalProperties": false}}, "required": ["title", "confirm", "confirmText", "cancel", "currentItems", "currentItemsDescription", "addItemsDescription", "addItems", "amountPaid", "newTotal", "differenceDue", "create", "currentTotal", "noteHint", "cancelSuccessToast", "createSuccessToast", "activeChangeError", "panel", "toast", "validation"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"email": {"type": "object", "properties": {"title": {"type": "string"}, "requestSuccess": {"type": "string"}}, "required": ["title", "requestSuccess"], "additionalProperties": false}, "shippingAddress": {"type": "object", "properties": {"title": {"type": "string"}, "requestSuccess": {"type": "string"}}, "required": ["title", "requestSuccess"], "additionalProperties": false}, "billingAddress": {"type": "object", "properties": {"title": {"type": "string"}, "requestSuccess": {"type": "string"}}, "required": ["title", "requestSuccess"], "additionalProperties": false}}, "required": ["email", "shippingAddress", "billing<PERSON><PERSON>ress"], "additionalProperties": false}, "returns": {"type": "object", "properties": {"create": {"type": "string"}, "confirm": {"type": "string"}, "confirmText": {"type": "string"}, "inbound": {"type": "string"}, "outbound": {"type": "string"}, "sendNotification": {"type": "string"}, "sendNotificationHint": {"type": "string"}, "returnTotal": {"type": "string"}, "inboundTotal": {"type": "string"}, "estDifference": {"type": "string"}, "outstandingAmount": {"type": "string"}, "reason": {"type": "string"}, "reasonHint": {"type": "string"}, "note": {"type": "string"}, "noInventoryLevel": {"type": "string"}, "noInventoryLevelDesc": {"type": "string"}, "noteHint": {"type": "string"}, "location": {"type": "string"}, "locationHint": {"type": "string"}, "inboundShipping": {"type": "string"}, "inboundShippingHint": {"type": "string"}, "returnableQuantityLabel": {"type": "string"}, "refundableAmountLabel": {"type": "string"}, "returnRequestedInfo": {"type": "string"}, "returnReceivedInfo": {"type": "string"}, "itemReceived": {"type": "string"}, "returnRequested": {"type": "string"}, "damagedItemReceived": {"type": "string"}, "damagedItemsReturned": {"type": "string"}, "activeChangeError": {"type": "string"}, "cancel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "placeholders": {"type": "object", "properties": {"noReturnShippingOptions": {"type": "object", "properties": {"title": {"type": "string"}, "hint": {"type": "string"}}, "required": ["title", "hint"], "additionalProperties": false}, "outboundShippingOptions": {"type": "object", "properties": {"title": {"type": "string"}, "hint": {"type": "string"}}, "required": ["title", "hint"], "additionalProperties": false}}, "required": ["noReturnShippingOptions", "outboundShippingOptions"], "additionalProperties": false}, "receive": {"type": "object", "properties": {"action": {"type": "string"}, "receiveItems": {"type": "string"}, "restockAll": {"type": "string"}, "itemsLabel": {"type": "string"}, "title": {"type": "string"}, "sendNotificationHint": {"type": "string"}, "inventoryWarning": {"type": "string"}, "writeOffInputLabel": {"type": "string"}, "toast": {"type": "object", "properties": {"success": {"type": "string"}, "errorLargeValue": {"type": "string"}, "errorNegativeValue": {"type": "string"}, "errorLargeDamagedValue": {"type": "string"}}, "required": ["success", "errorLarge<PERSON><PERSON>ue", "errorNegativeValue", "errorLargeDamagedValue"], "additionalProperties": false}}, "required": ["action", "receiveItems", "restockAll", "itemsLabel", "title", "sendNotificationHint", "inventoryWarning", "writeOffInputLabel", "toast"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"canceledSuccessfully": {"type": "string"}, "confirmedSuccessfully": {"type": "string"}}, "required": ["canceledSuccessfully", "confirmedSuccessfully"], "additionalProperties": false}, "panel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["create", "confirm", "confirmText", "inbound", "outbound", "sendNotification", "sendNotificationHint", "returnTotal", "inboundTotal", "estDifference", "outstandingAmount", "reason", "reasonHint", "note", "noInventoryLevel", "noInventoryLevelDesc", "noteHint", "location", "locationHint", "inboundShipping", "inboundShippingHint", "returnableQuantityLabel", "refundableAmountLabel", "returnRequestedInfo", "returnReceivedInfo", "itemReceived", "returnRequested", "damagedItemReceived", "damagedItemsReturned", "activeChangeError", "cancel", "placeholders", "receive", "toast", "panel"], "additionalProperties": false}, "claims": {"type": "object", "properties": {"create": {"type": "string"}, "confirm": {"type": "string"}, "confirmText": {"type": "string"}, "manage": {"type": "string"}, "outbound": {"type": "string"}, "outboundItemAdded": {"type": "string"}, "outboundTotal": {"type": "string"}, "outboundShipping": {"type": "string"}, "outboundShippingHint": {"type": "string"}, "refundAmount": {"type": "string"}, "activeChangeError": {"type": "string"}, "actions": {"type": "object", "properties": {"cancelClaim": {"type": "object", "properties": {"successToast": {"type": "string"}}, "required": ["successToast"], "additionalProperties": false}}, "required": ["cancelClaim"], "additionalProperties": false}, "cancel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "tooltips": {"type": "object", "properties": {"onlyReturnShippingOptions": {"type": "string"}}, "required": ["onlyReturnShippingOptions"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"canceledSuccessfully": {"type": "string"}, "confirmedSuccessfully": {"type": "string"}}, "required": ["canceledSuccessfully", "confirmedSuccessfully"], "additionalProperties": false}, "panel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["create", "confirm", "confirmText", "manage", "outbound", "outboundItemAdded", "outboundTotal", "outboundShipping", "outboundShippingHint", "refundAmount", "activeChangeError", "actions", "cancel", "tooltips", "toast", "panel"], "additionalProperties": false}, "exchanges": {"type": "object", "properties": {"create": {"type": "string"}, "manage": {"type": "string"}, "confirm": {"type": "string"}, "confirmText": {"type": "string"}, "outbound": {"type": "string"}, "outboundItemAdded": {"type": "string"}, "outboundTotal": {"type": "string"}, "outboundShipping": {"type": "string"}, "outboundShippingHint": {"type": "string"}, "refundAmount": {"type": "string"}, "activeChangeError": {"type": "string"}, "actions": {"type": "object", "properties": {"cancelExchange": {"type": "object", "properties": {"successToast": {"type": "string"}}, "required": ["successToast"], "additionalProperties": false}}, "required": ["cancelExchange"], "additionalProperties": false}, "cancel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "tooltips": {"type": "object", "properties": {"onlyReturnShippingOptions": {"type": "string"}}, "required": ["onlyReturnShippingOptions"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"canceledSuccessfully": {"type": "string"}, "confirmedSuccessfully": {"type": "string"}}, "required": ["canceledSuccessfully", "confirmedSuccessfully"], "additionalProperties": false}, "panel": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["create", "manage", "confirm", "confirmText", "outbound", "outboundItemAdded", "outboundTotal", "outboundShipping", "outboundShippingHint", "refundAmount", "activeChangeError", "actions", "cancel", "tooltips", "toast", "panel"], "additionalProperties": false}, "reservations": {"type": "object", "properties": {"allocatedLabel": {"type": "string"}, "notAllocatedLabel": {"type": "string"}}, "required": ["allocatedLabel", "notAllocatedLabel"], "additionalProperties": false}, "allocateItems": {"type": "object", "properties": {"action": {"type": "string"}, "title": {"type": "string"}, "locationDescription": {"type": "string"}, "itemsToAllocate": {"type": "string"}, "itemsToAllocateDesc": {"type": "string"}, "search": {"type": "string"}, "consistsOf": {"type": "string"}, "requires": {"type": "string"}, "toast": {"type": "object", "properties": {"created": {"type": "string"}}, "required": ["created"], "additionalProperties": false}, "error": {"type": "object", "properties": {"quantityNotAllocated": {"type": "string"}}, "required": ["quantityNotAllocated"], "additionalProperties": false}}, "required": ["action", "title", "locationDescription", "itemsToAllocate", "itemsToAllocateDesc", "search", "consistsOf", "requires", "toast", "error"], "additionalProperties": false}, "shipment": {"type": "object", "properties": {"title": {"type": "string"}, "trackingNumber": {"type": "string"}, "addTracking": {"type": "string"}, "sendNotification": {"type": "string"}, "sendNotificationHint": {"type": "string"}, "toastCreated": {"type": "string"}}, "required": ["title", "trackingNumber", "addTracking", "sendNotification", "sendNotificationHint", "toastCreated"], "additionalProperties": false}, "fulfillment": {"type": "object", "properties": {"cancelWarning": {"type": "string"}, "markAsDeliveredWarning": {"type": "string"}, "differentOptionSelected": {"type": "string"}, "disabledItemTooltip": {"type": "string"}, "unfulfilledItems": {"type": "string"}, "statusLabel": {"type": "string"}, "statusTitle": {"type": "string"}, "fulfillItems": {"type": "string"}, "awaitingFulfillmentBadge": {"type": "string"}, "requiresShipping": {"type": "string"}, "number": {"type": "string"}, "itemsToFulfill": {"type": "string"}, "create": {"type": "string"}, "available": {"type": "string"}, "inStock": {"type": "string"}, "markAsShipped": {"type": "string"}, "markAsPickedUp": {"type": "string"}, "markAsDelivered": {"type": "string"}, "itemsToFulfillDesc": {"type": "string"}, "locationDescription": {"type": "string"}, "sendNotificationHint": {"type": "string"}, "methodDescription": {"type": "string"}, "error": {"type": "object", "properties": {"wrongQuantity": {"type": "string"}, "wrongQuantity_other": {"type": "string"}, "noItems": {"type": "string"}, "noShippingOption": {"type": "string"}, "noLocation": {"type": "string"}}, "required": ["wrongQuantity", "wrongQuantity_other", "noItems", "noShippingOption", "noLocation"], "additionalProperties": false}, "status": {"type": "object", "properties": {"notFulfilled": {"type": "string"}, "partiallyFulfilled": {"type": "string"}, "fulfilled": {"type": "string"}, "partiallyShipped": {"type": "string"}, "shipped": {"type": "string"}, "delivered": {"type": "string"}, "partiallyDelivered": {"type": "string"}, "partiallyReturned": {"type": "string"}, "returned": {"type": "string"}, "canceled": {"type": "string"}, "requiresAction": {"type": "string"}}, "required": ["notFulfilled", "partiallyFulfilled", "fulfilled", "partiallyShipped", "shipped", "delivered", "partiallyDelivered", "partiallyReturned", "returned", "canceled", "requiresAction"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"created": {"type": "string"}, "canceled": {"type": "string"}, "fulfillmentShipped": {"type": "string"}, "fulfillmentDelivered": {"type": "string"}, "fulfillmentPickedUp": {"type": "string"}}, "required": ["created", "canceled", "fulfillmentShipped", "fulfillmentDelivered", "fulfillmentPickedUp"], "additionalProperties": false}, "trackingLabel": {"type": "string"}, "shippingFromLabel": {"type": "string"}, "itemsLabel": {"type": "string"}}, "required": ["cancelWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "differentOptionSelected", "disabledItemTooltip", "unfulfilledItems", "statusLabel", "statusTitle", "fulfillItems", "awaitingFulfillmentBadge", "requiresShipping", "number", "itemsToFulfill", "create", "available", "inStock", "markAsShipped", "markAsPickedUp", "mark<PERSON>Delivered", "itemsToFulfillDesc", "locationDescription", "sendNotificationHint", "methodDescription", "error", "status", "toast", "trackingLabel", "shippingFromLabel", "itemsLabel"], "additionalProperties": false}, "refund": {"type": "object", "properties": {"title": {"type": "string"}, "sendNotificationHint": {"type": "string"}, "systemPayment": {"type": "string"}, "systemPaymentDesc": {"type": "string"}, "error": {"type": "object", "properties": {"amountToLarge": {"type": "string"}, "amountNegative": {"type": "string"}, "reasonRequired": {"type": "string"}}, "required": ["amountToLarge", "amountNegative", "reasonRequired"], "additionalProperties": false}}, "required": ["title", "sendNotificationHint", "systemPayment", "systemPaymentDesc", "error"], "additionalProperties": false}, "customer": {"type": "object", "properties": {"contactLabel": {"type": "string"}, "editEmail": {"type": "string"}, "transferOwnership": {"type": "string"}, "editBillingAddress": {"type": "string"}, "editShippingAddress": {"type": "string"}}, "required": ["contactLabel", "editEmail", "transferOwnership", "editBillingAddress", "editShip<PERSON><PERSON><PERSON><PERSON>"], "additionalProperties": false}, "activity": {"type": "object", "properties": {"header": {"type": "string"}, "showMoreActivities_one": {"type": "string"}, "showMoreActivities_other": {"type": "string"}, "comment": {"type": "object", "properties": {"label": {"type": "string"}, "placeholder": {"type": "string"}, "addButtonText": {"type": "string"}, "deleteButtonText": {"type": "string"}}, "required": ["label", "placeholder", "addButtonText", "deleteButtonText"], "additionalProperties": false}, "from": {"type": "string"}, "to": {"type": "string"}, "events": {"type": "object", "properties": {"common": {"type": "object", "properties": {"toReturn": {"type": "string"}, "toSend": {"type": "string"}}, "required": ["toReturn", "toSend"], "additionalProperties": false}, "placed": {"type": "object", "properties": {"title": {"type": "string"}, "fromSalesChannel": {"type": "string"}}, "required": ["title", "fromSalesChannel"], "additionalProperties": false}, "canceled": {"type": "object", "properties": {"title": {"type": "string"}}, "required": ["title"], "additionalProperties": false}, "payment": {"type": "object", "properties": {"awaiting": {"type": "string"}, "captured": {"type": "string"}, "canceled": {"type": "string"}, "refunded": {"type": "string"}}, "required": ["awaiting", "captured", "canceled", "refunded"], "additionalProperties": false}, "fulfillment": {"type": "object", "properties": {"created": {"type": "string"}, "canceled": {"type": "string"}, "shipped": {"type": "string"}, "delivered": {"type": "string"}, "items_one": {"type": "string"}, "items_other": {"type": "string"}}, "required": ["created", "canceled", "shipped", "delivered", "items_one", "items_other"], "additionalProperties": false}, "return": {"type": "object", "properties": {"created": {"type": "string"}, "canceled": {"type": "string"}, "received": {"type": "string"}, "items_one": {"type": "string"}, "items_other": {"type": "string"}}, "required": ["created", "canceled", "received", "items_one", "items_other"], "additionalProperties": false}, "note": {"type": "object", "properties": {"comment": {"type": "string"}, "byLine": {"type": "string"}}, "required": ["comment", "byLine"], "additionalProperties": false}, "claim": {"type": "object", "properties": {"created": {"type": "string"}, "canceled": {"type": "string"}, "itemsInbound": {"type": "string"}, "itemsOutbound": {"type": "string"}}, "required": ["created", "canceled", "itemsInbound", "itemsOutbound"], "additionalProperties": false}, "exchange": {"type": "object", "properties": {"created": {"type": "string"}, "canceled": {"type": "string"}, "itemsInbound": {"type": "string"}, "itemsOutbound": {"type": "string"}}, "required": ["created", "canceled", "itemsInbound", "itemsOutbound"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"requested": {"type": "string"}, "confirmed": {"type": "string"}}, "required": ["requested", "confirmed"], "additionalProperties": false}, "transfer": {"type": "object", "properties": {"requested": {"type": "string"}, "confirmed": {"type": "string"}, "declined": {"type": "string"}}, "required": ["requested", "confirmed", "declined"], "additionalProperties": false}, "update_order": {"type": "object", "properties": {"shipping_address": {"type": "string"}, "billing_address": {"type": "string"}, "email": {"type": "string"}}, "required": ["shipping_address", "billing_address", "email"], "additionalProperties": false}}, "required": ["common", "placed", "canceled", "payment", "fulfillment", "return", "note", "claim", "exchange", "edit", "transfer", "update_order"], "additionalProperties": false}}, "required": ["header", "showMoreActivities_one", "showMoreActivities_other", "comment", "from", "to", "events"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"displayId": {"type": "string"}, "refundableAmount": {"type": "string"}, "returnableQuantity": {"type": "string"}}, "required": ["displayId", "refundableAmount", "returnableQuantity"], "additionalProperties": false}}, "required": ["domain", "claim", "exchange", "return", "cancelWarning", "orderCanceled", "onDateFromSalesChannel", "list", "status", "summary", "transfer", "payment", "edits", "edit", "returns", "claims", "exchanges", "reservations", "allocateItems", "shipment", "fulfillment", "refund", "customer", "activity", "fields"], "additionalProperties": false}, "draftOrders": {"type": "object", "properties": {"domain": {"type": "string"}, "deleteWarning": {"type": "string"}, "paymentLinkLabel": {"type": "string"}, "cartIdLabel": {"type": "string"}, "markAsPaid": {"type": "object", "properties": {"label": {"type": "string"}, "warningTitle": {"type": "string"}, "warningDescription": {"type": "string"}}, "required": ["label", "warningTitle", "warningDescription"], "additionalProperties": false}, "status": {"type": "object", "properties": {"open": {"type": "string"}, "completed": {"type": "string"}}, "required": ["open", "completed"], "additionalProperties": false}, "create": {"type": "object", "properties": {"createDraftOrder": {"type": "string"}, "createDraftOrderHint": {"type": "string"}, "chooseRegionHint": {"type": "string"}, "existingItemsLabel": {"type": "string"}, "existingItemsHint": {"type": "string"}, "customItemsLabel": {"type": "string"}, "customItemsHint": {"type": "string"}, "addExistingItemsAction": {"type": "string"}, "addCustomItemAction": {"type": "string"}, "noCustomItemsAddedLabel": {"type": "string"}, "noExistingItemsAddedLabel": {"type": "string"}, "chooseRegionTooltip": {"type": "string"}, "useExistingCustomerLabel": {"type": "string"}, "addShippingMethodsAction": {"type": "string"}, "unitPriceOverrideLabel": {"type": "string"}, "shippingOptionLabel": {"type": "string"}, "shippingOptionHint": {"type": "string"}, "shippingPriceOverrideLabel": {"type": "string"}, "shippingPriceOverrideHint": {"type": "string"}, "sendNotificationLabel": {"type": "string"}, "sendNotificationHint": {"type": "string"}}, "required": ["createDraftOrder", "createDraftOrderHint", "chooseRegionHint", "existingItemsLabel", "existingItemsHint", "customItemsLabel", "customItemsHint", "addExistingItemsAction", "addCustomItemAction", "noCustomItemsAddedLabel", "noExistingItemsAddedLabel", "chooseRegionTooltip", "useExistingCustomerLabel", "addShippingMethodsAction", "unitPriceOverrideLabel", "shippingOptionLabel", "shippingOptionHint", "shippingPriceOverrideLabel", "shippingPriceOverrideHint", "sendNotificationLabel", "sendNotificationHint"], "additionalProperties": false}, "validation": {"type": "object", "properties": {"requiredEmailOrCustomer": {"type": "string"}, "requiredItems": {"type": "string"}, "invalidEmail": {"type": "string"}}, "required": ["requiredEmailOrCustomer", "requiredItems", "invalidEmail"], "additionalProperties": false}}, "required": ["domain", "deleteWarning", "paymentLinkLabel", "cartIdLabel", "markAsPaid", "status", "create", "validation"], "additionalProperties": false}, "stockLocations": {"type": "object", "properties": {"domain": {"type": "string"}, "list": {"type": "object", "properties": {"description": {"type": "string"}}, "required": ["description"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "viewInventory": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "viewInventory", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}}, "required": ["confirmation"], "additionalProperties": false}, "fulfillmentProviders": {"type": "object", "properties": {"header": {"type": "string"}, "shippingOptionsTooltip": {"type": "string"}, "label": {"type": "string"}, "connectedTo": {"type": "string"}, "noProviders": {"type": "string"}, "action": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "shippingOptionsTooltip", "label", "connectedTo", "noProviders", "action", "successToast"], "additionalProperties": false}, "fulfillmentSets": {"type": "object", "properties": {"pickup": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "shipping": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "disable": {"type": "object", "properties": {"confirmation": {"type": "string"}, "pickup": {"type": "string"}, "shipping": {"type": "string"}}, "required": ["confirmation", "pickup", "shipping"], "additionalProperties": false}, "enable": {"type": "object", "properties": {"pickup": {"type": "string"}, "shipping": {"type": "string"}}, "required": ["pickup", "shipping"], "additionalProperties": false}}, "required": ["pickup", "shipping", "disable", "enable"], "additionalProperties": false}, "sidebar": {"type": "object", "properties": {"header": {"type": "string"}, "shippingProfiles": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}}, "required": ["label", "description"], "additionalProperties": false}}, "required": ["header", "shippingProfiles"], "additionalProperties": false}, "salesChannels": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "label": {"type": "string"}, "connectedTo": {"type": "string"}, "noChannels": {"type": "string"}, "action": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "label", "connectedTo", "noChannels", "action", "successToast"], "additionalProperties": false}, "pickupOptions": {"type": "object", "properties": {"edit": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}}, "required": ["edit"], "additionalProperties": false}, "shippingOptions": {"type": "object", "properties": {"create": {"type": "object", "properties": {"shipping": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "label": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "label", "successToast"], "additionalProperties": false}, "pickup": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "label": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "label", "successToast"], "additionalProperties": false}, "returns": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "label": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "label", "successToast"], "additionalProperties": false}, "tabs": {"type": "object", "properties": {"details": {"type": "string"}, "prices": {"type": "string"}}, "required": ["details", "prices"], "additionalProperties": false}, "action": {"type": "string"}}, "required": ["shipping", "pickup", "returns", "tabs", "action"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "action": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "action", "successToast"], "additionalProperties": false}, "pricing": {"type": "object", "properties": {"action": {"type": "string"}}, "required": ["action"], "additionalProperties": false}, "conditionalPrices": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "attributes": {"type": "object", "properties": {"cartItemTotal": {"type": "string"}}, "required": ["cartItemTotal"], "additionalProperties": false}, "summaries": {"type": "object", "properties": {"range": {"type": "string"}, "greaterThan": {"type": "string"}, "lessThan": {"type": "string"}}, "required": ["range", "greaterThan", "lessThan"], "additionalProperties": false}, "actions": {"type": "object", "properties": {"addPrice": {"type": "string"}, "manageConditionalPrices": {"type": "string"}}, "required": ["addPrice", "manageConditionalPrices"], "additionalProperties": false}, "rules": {"type": "object", "properties": {"amount": {"type": "string"}, "gte": {"type": "string"}, "lte": {"type": "string"}}, "required": ["amount", "gte", "lte"], "additionalProperties": false}, "customRules": {"type": "object", "properties": {"label": {"type": "string"}, "tooltip": {"type": "string"}, "eq": {"type": "string"}, "gt": {"type": "string"}, "lt": {"type": "string"}}, "required": ["label", "tooltip", "eq", "gt", "lt"], "additionalProperties": false}, "errors": {"type": "object", "properties": {"amountRequired": {"type": "string"}, "minOrMaxRequired": {"type": "string"}, "minGreaterThanMax": {"type": "string"}, "duplicateAmount": {"type": "string"}, "overlappingConditions": {"type": "string"}}, "required": ["amountRequired", "minOrMaxRequired", "minGreaterThanMax", "duplicateAmount", "overlappingConditions"], "additionalProperties": false}}, "required": ["header", "description", "attributes", "summaries", "actions", "rules", "customRules", "errors"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"count": {"type": "object", "properties": {"shipping_one": {"type": "string"}, "shipping_other": {"type": "string"}, "pickup_one": {"type": "string"}, "pickup_other": {"type": "string"}, "returns_one": {"type": "string"}, "returns_other": {"type": "string"}}, "required": ["shipping_one", "shipping_other", "pickup_one", "pickup_other", "returns_one", "returns_other"], "additionalProperties": false}, "priceType": {"type": "object", "properties": {"label": {"type": "string"}, "options": {"type": "object", "properties": {"fixed": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "calculated": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}}, "required": ["fixed", "calculated"], "additionalProperties": false}}, "required": ["label", "options"], "additionalProperties": false}, "enableInStore": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "provider": {"type": "string"}, "profile": {"type": "string"}, "fulfillmentOption": {"type": "string"}}, "required": ["count", "priceType", "enableInStore", "provider", "profile", "fulfillmentOption"], "additionalProperties": false}}, "required": ["create", "delete", "edit", "pricing", "conditionalPrices", "fields"], "additionalProperties": false}, "serviceZones": {"type": "object", "properties": {"create": {"type": "object", "properties": {"headerPickup": {"type": "string"}, "headerShipping": {"type": "string"}, "action": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["headerPickup", "headerShipping", "action", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "manageAreas": {"type": "object", "properties": {"header": {"type": "string"}, "action": {"type": "string"}, "label": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "action", "label", "hint", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"noRecords": {"type": "string"}, "tip": {"type": "string"}}, "required": ["noRecords", "tip"], "additionalProperties": false}}, "required": ["create", "edit", "delete", "manageAreas", "fields"], "additionalProperties": false}}, "required": ["domain", "list", "create", "edit", "delete", "fulfillmentProviders", "fulfillmentSets", "sidebar", "salesChannels", "pickupOptions", "shippingOptions", "serviceZones"], "additionalProperties": false}, "shippingProfile": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "successToast"], "additionalProperties": false}, "tooltip": {"type": "object", "properties": {"type": {"type": "string"}}, "required": ["type"], "additionalProperties": false}}, "required": ["domain", "subtitle", "create", "delete", "tooltip"], "additionalProperties": false}, "taxRegions": {"type": "object", "properties": {"domain": {"type": "string"}, "list": {"type": "object", "properties": {"hint": {"type": "string"}}, "required": ["hint"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "errors": {"type": "object", "properties": {"missingProvider": {"type": "string"}, "missingCountry": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "missingCountry"], "additionalProperties": false}, "successToast": {"type": "string"}}, "required": ["header", "hint", "errors", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "province": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "provider": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "state": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "stateOrTerritory": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "county": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "region": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "department": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "territory": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "prefecture": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "district": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "governorate": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "canton": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "emirate": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "sublevel": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create"], "additionalProperties": false}, "taxOverrides": {"type": "object", "properties": {"header": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}}, "required": ["header", "hint"], "additionalProperties": false}}, "required": ["header", "create", "edit"], "additionalProperties": false}, "taxRates": {"type": "object", "properties": {"create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}}, "required": ["create", "edit", "delete"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"isCombinable": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "true": {"type": "string"}, "false": {"type": "string"}}, "required": ["label", "hint", "true", "false"], "additionalProperties": false}, "defaultTaxRate": {"type": "object", "properties": {"label": {"type": "string"}, "tooltip": {"type": "string"}, "action": {"type": "string"}}, "required": ["label", "tooltip", "action"], "additionalProperties": false}, "taxRate": {"type": "string"}, "taxCode": {"type": "string"}, "taxProvider": {"type": "string"}, "targets": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "options": {"type": "object", "properties": {"product": {"type": "string"}, "productCollection": {"type": "string"}, "productTag": {"type": "string"}, "productType": {"type": "string"}, "customerGroup": {"type": "string"}}, "required": ["product", "productCollection", "productTag", "productType", "customerGroup"], "additionalProperties": false}, "operators": {"type": "object", "properties": {"in": {"type": "string"}, "on": {"type": "string"}, "and": {"type": "string"}}, "required": ["in", "on", "and"], "additionalProperties": false}, "placeholders": {"type": "object", "properties": {"product": {"type": "string"}, "productCollection": {"type": "string"}, "productTag": {"type": "string"}, "productType": {"type": "string"}, "customerGroup": {"type": "string"}}, "required": ["product", "productCollection", "productTag", "productType", "customerGroup"], "additionalProperties": false}, "tags": {"type": "object", "properties": {"product": {"type": "string"}, "productCollection": {"type": "string"}, "productTag": {"type": "string"}, "productType": {"type": "string"}, "customerGroup": {"type": "string"}}, "required": ["product", "productCollection", "productTag", "productType", "customerGroup"], "additionalProperties": false}, "modal": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "values_one": {"type": "string"}, "values_other": {"type": "string"}, "numberOfTargets_one": {"type": "string"}, "numberOfTargets_other": {"type": "string"}, "additionalValues_one": {"type": "string"}, "additionalValues_other": {"type": "string"}, "action": {"type": "string"}}, "required": ["label", "hint", "options", "operators", "placeholders", "tags", "modal", "values_one", "values_other", "numberOfTargets_one", "numberOfTargets_other", "additionalValues_one", "additionalValues_other", "action"], "additionalProperties": false}, "sublevels": {"type": "object", "properties": {"labels": {"type": "object", "properties": {"province": {"type": "string"}, "state": {"type": "string"}, "region": {"type": "string"}, "stateOrTerritory": {"type": "string"}, "department": {"type": "string"}, "county": {"type": "string"}, "territory": {"type": "string"}, "prefecture": {"type": "string"}, "district": {"type": "string"}, "governorate": {"type": "string"}, "emirate": {"type": "string"}, "canton": {"type": "string"}, "sublevel": {"type": "string"}}, "required": ["province", "state", "region", "stateOrTerritory", "department", "county", "territory", "prefecture", "district", "governorate", "emirate", "canton", "sublevel"], "additionalProperties": false}, "placeholders": {"type": "object", "properties": {"province": {"type": "string"}, "state": {"type": "string"}, "region": {"type": "string"}, "stateOrTerritory": {"type": "string"}, "department": {"type": "string"}, "county": {"type": "string"}, "territory": {"type": "string"}, "prefecture": {"type": "string"}, "district": {"type": "string"}, "governorate": {"type": "string"}, "emirate": {"type": "string"}, "canton": {"type": "string"}}, "required": ["province", "state", "region", "stateOrTerritory", "department", "county", "territory", "prefecture", "district", "governorate", "emirate", "canton"], "additionalProperties": false}, "tooltips": {"type": "object", "properties": {"sublevel": {"type": "string"}, "notPartOfCountry": {"type": "string"}}, "required": ["sublevel", "notPartOfCountry"], "additionalProperties": false}, "alert": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "action": {"type": "string"}}, "required": ["header", "description", "action"], "additionalProperties": false}}, "required": ["labels", "placeholders", "tooltips", "alert"], "additionalProperties": false}, "noDefaultRate": {"type": "object", "properties": {"label": {"type": "string"}, "tooltip": {"type": "string"}}, "required": ["label", "tooltip"], "additionalProperties": false}}, "required": ["isCombinable", "defaultTaxRate", "taxRate", "taxCode", "taxProvider", "targets", "sublevels", "noDefaultRate"], "additionalProperties": false}}, "required": ["domain", "list", "delete", "create", "edit", "province", "provider", "state", "stateOrTerritory", "county", "region", "department", "territory", "prefecture", "district", "governorate", "canton", "emirate", "sublevel", "taxOverrides", "taxRates", "fields"], "additionalProperties": false}, "promotions": {"type": "object", "properties": {"domain": {"type": "string"}, "sections": {"type": "object", "properties": {"details": {"type": "string"}}, "required": ["details"], "additionalProperties": false}, "tabs": {"type": "object", "properties": {"template": {"type": "string"}, "details": {"type": "string"}, "campaign": {"type": "string"}}, "required": ["template", "details", "campaign"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"type": {"type": "string"}, "value_type": {"type": "string"}, "value": {"type": "string"}, "campaign": {"type": "string"}, "method": {"type": "string"}, "allocation": {"type": "string"}, "addCondition": {"type": "string"}, "clearAll": {"type": "string"}, "taxInclusive": {"type": "string"}, "amount": {"type": "object", "properties": {"tooltip": {"type": "string"}}, "required": ["tooltip"], "additionalProperties": false}, "conditions": {"type": "object", "properties": {"rules": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "target-rules": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "buy-rules": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["rules", "target-rules", "buy-rules"], "additionalProperties": false}}, "required": ["type", "value_type", "value", "campaign", "method", "allocation", "addCondition", "clearAll", "taxInclusive", "amount", "conditions"], "additionalProperties": false}, "tooltips": {"type": "object", "properties": {"campaignType": {"type": "string"}}, "required": ["campaignType"], "additionalProperties": false}, "errors": {"type": "object", "properties": {"requiredField": {"type": "string"}, "promotionTabError": {"type": "string"}}, "required": ["requiredField", "promotionTabError"], "additionalProperties": false}, "toasts": {"type": "object", "properties": {"promotionCreateSuccess": {"type": "string"}}, "required": ["promotionCreateSuccess"], "additionalProperties": false}, "create": {"type": "object", "properties": {}, "required": [], "additionalProperties": false}, "edit": {"type": "object", "properties": {"title": {"type": "string"}, "rules": {"type": "object", "properties": {"title": {"type": "string"}}, "required": ["title"], "additionalProperties": false}, "target-rules": {"type": "object", "properties": {"title": {"type": "string"}}, "required": ["title"], "additionalProperties": false}, "buy-rules": {"type": "object", "properties": {"title": {"type": "string"}}, "required": ["title"], "additionalProperties": false}}, "required": ["title", "rules", "target-rules", "buy-rules"], "additionalProperties": false}, "campaign": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "actions": {"type": "object", "properties": {"goToCampaign": {"type": "string"}}, "required": ["goToCampaign"], "additionalProperties": false}}, "required": ["header", "edit", "actions"], "additionalProperties": false}, "campaign_currency": {"type": "object", "properties": {"tooltip": {"type": "string"}}, "required": ["tooltip"], "additionalProperties": false}, "form": {"type": "object", "properties": {"required": {"type": "string"}, "and": {"type": "string"}, "selectAttribute": {"type": "string"}, "campaign": {"type": "object", "properties": {"existing": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "placeholder": {"type": "object", "properties": {"title": {"type": "string"}, "desc": {"type": "string"}}, "required": ["title", "desc"], "additionalProperties": false}}, "required": ["title", "description", "placeholder"], "additionalProperties": false}, "new": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "none": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["existing", "new", "none"], "additionalProperties": false}, "taxInclusive": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "status": {"type": "object", "properties": {"label": {"type": "string"}, "draft": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "active": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "inactive": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["label", "draft", "active", "inactive"], "additionalProperties": false}, "method": {"type": "object", "properties": {"label": {"type": "string"}, "code": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "automatic": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["label", "code", "automatic"], "additionalProperties": false}, "max_quantity": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "type": {"type": "object", "properties": {"standard": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "buyget": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["standard", "buyget"], "additionalProperties": false}, "allocation": {"type": "object", "properties": {"each": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "across": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["each", "across"], "additionalProperties": false}, "code": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "value": {"type": "object", "properties": {"title": {"type": "string"}}, "required": ["title"], "additionalProperties": false}, "value_type": {"type": "object", "properties": {"fixed": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "percentage": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["fixed", "percentage"], "additionalProperties": false}}, "required": ["required", "and", "selectAttribute", "campaign", "taxInclusive", "status", "method", "max_quantity", "type", "allocation", "code", "value", "value_type"], "additionalProperties": false}, "deleteWarning": {"type": "string"}, "createPromotionTitle": {"type": "string"}, "type": {"type": "string"}, "conditions": {"type": "object", "properties": {"add": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["add", "list"], "additionalProperties": false}}, "required": ["domain", "sections", "tabs", "fields", "tooltips", "errors", "toasts", "create", "edit", "campaign", "campaign_currency", "form", "deleteWarning", "createPromotionTitle", "type", "conditions"], "additionalProperties": false}, "campaigns": {"type": "object", "properties": {"domain": {"type": "string"}, "details": {"type": "string"}, "status": {"type": "object", "properties": {"active": {"type": "string"}, "expired": {"type": "string"}, "scheduled": {"type": "string"}}, "required": ["active", "expired", "scheduled"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}, "configuration": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}}, "required": ["header", "edit"], "additionalProperties": false}, "create": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "hint": {"type": "string"}, "header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["title", "description", "hint", "header", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"name": {"type": "string"}, "identifier": {"type": "string"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "total_spend": {"type": "string"}, "total_used": {"type": "string"}, "budget_limit": {"type": "string"}, "campaign_id": {"type": "object", "properties": {"hint": {"type": "string"}}, "required": ["hint"], "additionalProperties": false}}, "required": ["name", "identifier", "start_date", "end_date", "total_spend", "total_used", "budget_limit", "campaign_id"], "additionalProperties": false}, "budget": {"type": "object", "properties": {"create": {"type": "object", "properties": {"hint": {"type": "string"}, "header": {"type": "string"}}, "required": ["hint", "header"], "additionalProperties": false}, "details": {"type": "string"}, "fields": {"type": "object", "properties": {"type": {"type": "string"}, "currency": {"type": "string"}, "limit": {"type": "string"}, "used": {"type": "string"}}, "required": ["type", "currency", "limit", "used"], "additionalProperties": false}, "type": {"type": "object", "properties": {"spend": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "usage": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}}, "required": ["spend", "usage"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}}, "required": ["create", "details", "fields", "type", "edit"], "additionalProperties": false}, "promotions": {"type": "object", "properties": {"remove": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "alreadyAdded": {"type": "string"}, "alreadyAddedDiffCampaign": {"type": "string"}, "currencyMismatch": {"type": "string"}, "toast": {"type": "object", "properties": {"success": {"type": "string"}}, "required": ["success"], "additionalProperties": false}, "add": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["list"], "additionalProperties": false}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["remove", "alreadyAdded", "alreadyAddedDiffCampaign", "currencyMismatch", "toast", "add", "list"], "additionalProperties": false}, "deleteCampaignWarning": {"type": "string"}, "totalSpend": {"type": "string"}}, "required": ["domain", "details", "status", "delete", "edit", "configuration", "create", "fields", "budget", "promotions", "deleteCampaignWarning", "totalSpend"], "additionalProperties": false}, "priceLists": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "subheader": {"type": "string"}, "tabs": {"type": "object", "properties": {"details": {"type": "string"}, "products": {"type": "string"}, "prices": {"type": "string"}}, "required": ["details", "products", "prices"], "additionalProperties": false}, "successToast": {"type": "string"}, "products": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["list"], "additionalProperties": false}}, "required": ["header", "subheader", "tabs", "successToast", "products"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "configuration": {"type": "object", "properties": {"header": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}}, "required": ["header", "edit"], "additionalProperties": false}, "products": {"type": "object", "properties": {"header": {"type": "string"}, "actions": {"type": "object", "properties": {"addProducts": {"type": "string"}, "editPrices": {"type": "string"}}, "required": ["addProducts", "editPrices"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation_one": {"type": "string"}, "confirmation_other": {"type": "string"}, "successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}}, "required": ["confirmation_one", "confirmation_other", "successToast_one", "successToast_other"], "additionalProperties": false}, "add": {"type": "object", "properties": {"successToast": {"type": "string"}}, "required": ["successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"successToast": {"type": "string"}}, "required": ["successToast"], "additionalProperties": false}}, "required": ["header", "actions", "delete", "add", "edit"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"priceOverrides": {"type": "object", "properties": {"label": {"type": "string"}, "header": {"type": "string"}}, "required": ["label", "header"], "additionalProperties": false}, "status": {"type": "object", "properties": {"label": {"type": "string"}, "options": {"type": "object", "properties": {"active": {"type": "string"}, "draft": {"type": "string"}, "expired": {"type": "string"}, "scheduled": {"type": "string"}}, "required": ["active", "draft", "expired", "scheduled"], "additionalProperties": false}}, "required": ["label", "options"], "additionalProperties": false}, "type": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}, "options": {"type": "object", "properties": {"sale": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}}, "required": ["label", "description"], "additionalProperties": false}, "override": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}}, "required": ["label", "description"], "additionalProperties": false}}, "required": ["sale", "override"], "additionalProperties": false}}, "required": ["label", "hint", "options"], "additionalProperties": false}, "startsAt": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "endsAt": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}, "customerAvailability": {"type": "object", "properties": {"header": {"type": "string"}, "label": {"type": "string"}, "hint": {"type": "string"}, "placeholder": {"type": "string"}, "attribute": {"type": "string"}}, "required": ["header", "label", "hint", "placeholder", "attribute"], "additionalProperties": false}}, "required": ["priceOverrides", "status", "type", "startsAt", "endsAt", "customerAvailability"], "additionalProperties": false}}, "required": ["domain", "subtitle", "delete", "create", "edit", "configuration", "products", "fields"], "additionalProperties": false}, "profile": {"type": "object", "properties": {"domain": {"type": "string"}, "manageYourProfileDetails": {"type": "string"}, "fields": {"type": "object", "properties": {"languageLabel": {"type": "string"}, "usageInsightsLabel": {"type": "string"}}, "required": ["languageLabel", "usageInsightsLabel"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "languageHint": {"type": "string"}, "languagePlaceholder": {"type": "string"}, "usageInsightsHint": {"type": "string"}}, "required": ["header", "languageHint", "languagePlaceholder", "usageInsightsHint"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"edit": {"type": "string"}}, "required": ["edit"], "additionalProperties": false}}, "required": ["domain", "manageYourProfileDetails", "fields", "edit", "toast"], "additionalProperties": false}, "users": {"type": "object", "properties": {"domain": {"type": "string"}, "editUser": {"type": "string"}, "inviteUser": {"type": "string"}, "inviteUserHint": {"type": "string"}, "sendInvite": {"type": "string"}, "pendingInvites": {"type": "string"}, "deleteInviteWarning": {"type": "string"}, "resendInvite": {"type": "string"}, "copyInviteLink": {"type": "string"}, "expiredOnDate": {"type": "string"}, "validFromUntil": {"type": "string"}, "acceptedOnDate": {"type": "string"}, "inviteStatus": {"type": "object", "properties": {"accepted": {"type": "string"}, "pending": {"type": "string"}, "expired": {"type": "string"}}, "required": ["accepted", "pending", "expired"], "additionalProperties": false}, "roles": {"type": "object", "properties": {"admin": {"type": "string"}, "developer": {"type": "string"}, "member": {"type": "string"}}, "required": ["admin", "developer", "member"], "additionalProperties": false}, "list": {"type": "object", "properties": {"empty": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}, "filtered": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}}, "required": ["empty", "filtered"], "additionalProperties": false}, "deleteUserWarning": {"type": "string"}, "deleteUserSuccess": {"type": "string"}, "invite": {"type": "string"}}, "required": ["domain", "editUser", "inviteUser", "inviteUserHint", "sendInvite", "pendingInvites", "deleteInviteWarning", "resendInvite", "copyInviteLink", "expiredOnDate", "validFromUntil", "acceptedOnDate", "inviteStatus", "roles", "list", "deleteUserWarning", "deleteUserSuccess", "invite"], "additionalProperties": false}, "store": {"type": "object", "properties": {"domain": {"type": "string"}, "manageYourStoresDetails": {"type": "string"}, "editStore": {"type": "string"}, "defaultCurrency": {"type": "string"}, "defaultRegion": {"type": "string"}, "defaultSalesChannel": {"type": "string"}, "defaultLocation": {"type": "string"}, "swapLinkTemplate": {"type": "string"}, "paymentLinkTemplate": {"type": "string"}, "inviteLinkTemplate": {"type": "string"}, "currencies": {"type": "string"}, "addCurrencies": {"type": "string"}, "enableTaxInclusivePricing": {"type": "string"}, "disableTaxInclusivePricing": {"type": "string"}, "removeCurrencyWarning_one": {"type": "string"}, "removeCurrencyWarning_other": {"type": "string"}, "currencyAlreadyAdded": {"type": "string"}, "edit": {"type": "object", "properties": {"header": {"type": "string"}}, "required": ["header"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"update": {"type": "string"}, "currenciesUpdated": {"type": "string"}, "currenciesRemoved": {"type": "string"}, "updatedTaxInclusivitySuccessfully": {"type": "string"}}, "required": ["update", "currenciesUpdated", "currenciesRemoved", "updatedTaxInclusivitySuccessfully"], "additionalProperties": false}}, "required": ["domain", "manageYourStoresDetails", "editStore", "defaultCurrency", "defaultRegion", "defaultSalesChannel", "defaultLocation", "swapLinkTemplate", "paymentLinkTemplate", "inviteLinkTemplate", "currencies", "addCurrencies", "enableTaxInclusivePricing", "disableTaxInclusivePricing", "removeCurrencyWarning_one", "removeCurrency<PERSON><PERSON>ning_other", "currencyAlreadyAdded", "edit", "toast"], "additionalProperties": false}, "regions": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "createRegion": {"type": "string"}, "createRegionHint": {"type": "string"}, "addCountries": {"type": "string"}, "editRegion": {"type": "string"}, "countriesHint": {"type": "string"}, "deleteRegionWarning": {"type": "string"}, "removeCountriesWarning_one": {"type": "string"}, "removeCountriesWarning_other": {"type": "string"}, "removeCountryWarning": {"type": "string"}, "automaticTaxesHint": {"type": "string"}, "taxInclusiveHint": {"type": "string"}, "providersHint": {"type": "string"}, "shippingOptions": {"type": "string"}, "deleteShippingOptionWarning": {"type": "string"}, "return": {"type": "string"}, "outbound": {"type": "string"}, "priceType": {"type": "string"}, "flatRate": {"type": "string"}, "calculated": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "toast": {"type": "object", "properties": {"delete": {"type": "string"}, "edit": {"type": "string"}, "create": {"type": "string"}, "countries": {"type": "string"}}, "required": ["delete", "edit", "create", "countries"], "additionalProperties": false}, "shippingOption": {"type": "object", "properties": {"createShippingOption": {"type": "string"}, "createShippingOptionHint": {"type": "string"}, "editShippingOption": {"type": "string"}, "fulfillmentMethod": {"type": "string"}, "type": {"type": "object", "properties": {"outbound": {"type": "string"}, "outboundHint": {"type": "string"}, "return": {"type": "string"}, "returnHint": {"type": "string"}}, "required": ["outbound", "outboundHint", "return", "returnHint"], "additionalProperties": false}, "priceType": {"type": "object", "properties": {"label": {"type": "string"}, "flatRate": {"type": "string"}, "calculated": {"type": "string"}}, "required": ["label", "flatRate", "calculated"], "additionalProperties": false}, "availability": {"type": "object", "properties": {"adminOnly": {"type": "string"}, "adminOnlyHint": {"type": "string"}}, "required": ["adminOnly", "adminOnlyHint"], "additionalProperties": false}, "taxInclusiveHint": {"type": "string"}, "requirements": {"type": "object", "properties": {"label": {"type": "string"}, "hint": {"type": "string"}}, "required": ["label", "hint"], "additionalProperties": false}}, "required": ["createShippingOption", "createShippingOptionHint", "editShippingOption", "fulfillmentMethod", "type", "priceType", "availability", "taxInclusiveHint", "requirements"], "additionalProperties": false}}, "required": ["domain", "subtitle", "createRegion", "createRegionHint", "addCountries", "editRegion", "countriesHint", "deleteRegionWarning", "removeCountriesWarning_one", "removeCountriesWarning_other", "removeCountryWarning", "automaticTaxesHint", "taxInclusiveHint", "providersHint", "shippingOptions", "deleteShippingOptionWarning", "return", "outbound", "priceType", "flatRate", "calculated", "list", "toast", "shippingOption"], "additionalProperties": false}, "taxes": {"type": "object", "properties": {"domain": {"type": "string"}, "domainDescription": {"type": "string"}, "countries": {"type": "object", "properties": {"taxCountriesHint": {"type": "string"}}, "required": ["taxCountriesHint"], "additionalProperties": false}, "settings": {"type": "object", "properties": {"editTaxSettings": {"type": "string"}, "taxProviderLabel": {"type": "string"}, "systemTaxProviderLabel": {"type": "string"}, "calculateTaxesAutomaticallyLabel": {"type": "string"}, "calculateTaxesAutomaticallyHint": {"type": "string"}, "applyTaxesOnGiftCardsLabel": {"type": "string"}, "applyTaxesOnGiftCardsHint": {"type": "string"}, "defaultTaxRateLabel": {"type": "string"}, "defaultTaxCodeLabel": {"type": "string"}}, "required": ["editTaxSettings", "taxProviderLabel", "systemTaxProviderLabel", "calculateTaxesAutomaticallyLabel", "calculateTaxesAutomaticallyHint", "applyTaxesOnGiftCardsLabel", "applyTaxesOnGiftCardsHint", "defaultTaxRateLabel", "defaultTaxCodeLabel"], "additionalProperties": false}, "defaultRate": {"type": "object", "properties": {"sectionTitle": {"type": "string"}}, "required": ["sectionTitle"], "additionalProperties": false}, "taxRate": {"type": "object", "properties": {"sectionTitle": {"type": "string"}, "createTaxRate": {"type": "string"}, "createTaxRateHint": {"type": "string"}, "deleteRateDescription": {"type": "string"}, "editRateAction": {"type": "string"}, "editOverridesAction": {"type": "string"}, "editOverridesTitle": {"type": "string"}, "editOverridesHint": {"type": "string"}, "deleteTaxRateWarning": {"type": "string"}, "productOverridesLabel": {"type": "string"}, "productOverridesHint": {"type": "string"}, "addProductOverridesAction": {"type": "string"}, "productTypeOverridesLabel": {"type": "string"}, "productTypeOverridesHint": {"type": "string"}, "addProductTypeOverridesAction": {"type": "string"}, "shippingOptionOverridesLabel": {"type": "string"}, "shippingOptionOverridesHint": {"type": "string"}, "addShippingOptionOverridesAction": {"type": "string"}, "productOverridesHeader": {"type": "string"}, "productTypeOverridesHeader": {"type": "string"}, "shippingOptionOverridesHeader": {"type": "string"}}, "required": ["sectionTitle", "createTaxRate", "createTaxRateHint", "deleteRateDescription", "editRateAction", "editOverridesAction", "editOverridesTitle", "editOverridesHint", "deleteTaxRateWarning", "productOverridesLabel", "productOverridesHint", "addProductOverridesAction", "productTypeOverridesLabel", "productTypeOverridesHint", "addProductTypeOverridesAction", "shippingOptionOverridesLabel", "shippingOptionOverridesHint", "addShippingOptionOverridesAction", "productOverridesHeader", "productTypeOverridesHeader", "shippingOptionOverridesHeader"], "additionalProperties": false}}, "required": ["domain", "domainDescription", "countries", "settings", "defaultRate", "taxRate"], "additionalProperties": false}, "locations": {"type": "object", "properties": {"domain": {"type": "string"}, "editLocation": {"type": "string"}, "addSalesChannels": {"type": "string"}, "noLocationsFound": {"type": "string"}, "selectLocations": {"type": "string"}, "deleteLocationWarning": {"type": "string"}, "removeSalesChannelsWarning_one": {"type": "string"}, "removeSalesChannelsWarning_other": {"type": "string"}, "toast": {"type": "object", "properties": {"create": {"type": "string"}, "update": {"type": "string"}, "removeChannel": {"type": "string"}}, "required": ["create", "update", "removeChannel"], "additionalProperties": false}}, "required": ["domain", "editLocation", "addSalesChannels", "noLocationsFound", "selectLocations", "deleteLocationWarning", "removeSalesChannelsWarning_one", "removeSalesChannelsWarning_other", "toast"], "additionalProperties": false}, "reservations": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "deleteWarning": {"type": "string"}}, "required": ["domain", "subtitle", "deleteWarning"], "additionalProperties": false}, "salesChannels": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "list": {"type": "object", "properties": {"empty": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}, "filtered": {"type": "object", "properties": {"heading": {"type": "string"}, "description": {"type": "string"}}, "required": ["heading", "description"], "additionalProperties": false}}, "required": ["empty", "filtered"], "additionalProperties": false}, "createSalesChannel": {"type": "string"}, "createSalesChannelHint": {"type": "string"}, "enabledHint": {"type": "string"}, "removeProductsWarning_one": {"type": "string"}, "removeProductsWarning_other": {"type": "string"}, "addProducts": {"type": "string"}, "editSalesChannel": {"type": "string"}, "productAlreadyAdded": {"type": "string"}, "deleteSalesChannelWarning": {"type": "string"}, "toast": {"type": "object", "properties": {"create": {"type": "string"}, "update": {"type": "string"}, "delete": {"type": "string"}}, "required": ["create", "update", "delete"], "additionalProperties": false}, "tooltip": {"type": "object", "properties": {"cannotDeleteDefault": {"type": "string"}}, "required": ["cannotDeleteDefault"], "additionalProperties": false}, "products": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "add": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["list"], "additionalProperties": false}}, "required": ["list", "add"], "additionalProperties": false}}, "required": ["domain", "subtitle", "list", "createSalesChannel", "createSalesChannelHint", "enabledHint", "removeProductsWarning_one", "removeProductsWarning_other", "addProducts", "editSalesChannel", "productAlreadyAdded", "deleteSalesChannelWarning", "toast", "tooltip", "products"], "additionalProperties": false}, "apiKeyManagement": {"type": "object", "properties": {"domain": {"type": "object", "properties": {"publishable": {"type": "string"}, "secret": {"type": "string"}}, "required": ["publishable", "secret"], "additionalProperties": false}, "subtitle": {"type": "object", "properties": {"publishable": {"type": "string"}, "secret": {"type": "string"}}, "required": ["publishable", "secret"], "additionalProperties": false}, "status": {"type": "object", "properties": {"active": {"type": "string"}, "revoked": {"type": "string"}}, "required": ["active", "revoked"], "additionalProperties": false}, "type": {"type": "object", "properties": {"publishable": {"type": "string"}, "secret": {"type": "string"}}, "required": ["publishable", "secret"], "additionalProperties": false}, "create": {"type": "object", "properties": {"createPublishableHeader": {"type": "string"}, "createPublishableHint": {"type": "string"}, "createSecretHeader": {"type": "string"}, "createSecretHint": {"type": "string"}, "secretKeyCreatedHeader": {"type": "string"}, "secretKeyCreatedHint": {"type": "string"}, "copySecretTokenSuccess": {"type": "string"}, "copySecretTokenFailure": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["createPublishableHeader", "createPublishableHint", "createSecretHeader", "createSecretHint", "secret<PERSON>ey<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "secretKeyCreatedHint", "copySecretTokenSuccess", "copySecretTokenFailure", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "description": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "description", "successToast"], "additionalProperties": false}, "salesChannels": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "successToast_one": {"type": "string"}, "successToast_other": {"type": "string"}, "alreadyAddedTooltip": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["title", "description", "successToast_one", "successToast_other", "alreadyAddedTooltip", "list"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"warning": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["warning", "successToast"], "additionalProperties": false}, "revoke": {"type": "object", "properties": {"warning": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["warning", "successToast"], "additionalProperties": false}, "addSalesChannels": {"type": "object", "properties": {"list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}}, "required": ["list"], "additionalProperties": false}, "removeSalesChannel": {"type": "object", "properties": {"warning": {"type": "string"}, "warningBatch_one": {"type": "string"}, "warningBatch_other": {"type": "string"}, "successToast": {"type": "string"}, "successToastBatch_one": {"type": "string"}, "successToastBatch_other": {"type": "string"}}, "required": ["warning", "warningBatch_one", "warningBatch_other", "successToast", "successToastBatch_one", "successToastBatch_other"], "additionalProperties": false}, "actions": {"type": "object", "properties": {"revoke": {"type": "string"}, "copy": {"type": "string"}, "copySuccessToast": {"type": "string"}}, "required": ["revoke", "copy", "copySuccessToast"], "additionalProperties": false}, "table": {"type": "object", "properties": {"lastUsedAtHeader": {"type": "string"}, "createdAtHeader": {"type": "string"}}, "required": ["lastUsedAtHeader", "createdAtHeader"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"lastUsedAtLabel": {"type": "string"}, "revokedByLabel": {"type": "string"}, "revokedAtLabel": {"type": "string"}, "createdByLabel": {"type": "string"}}, "required": ["lastUsedAtLabel", "revokedByLabel", "revokedAtLabel", "createdByLabel"], "additionalProperties": false}}, "required": ["domain", "subtitle", "status", "type", "create", "edit", "salesChannels", "delete", "revoke", "addSalesChannels", "removeSalesChannel", "actions", "table", "fields"], "additionalProperties": false}, "returnReasons": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "calloutHint": {"type": "string"}, "editReason": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "subtitle": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "subtitle", "hint", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "subtitle": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "subtitle", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"value": {"type": "object", "properties": {"label": {"type": "string"}, "placeholder": {"type": "string"}, "tooltip": {"type": "string"}}, "required": ["label", "placeholder", "tooltip"], "additionalProperties": false}, "label": {"type": "object", "properties": {"label": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "placeholder"], "additionalProperties": false}, "description": {"type": "object", "properties": {"label": {"type": "string"}, "placeholder": {"type": "string"}}, "required": ["label", "placeholder"], "additionalProperties": false}}, "required": ["value", "label", "description"], "additionalProperties": false}}, "required": ["domain", "subtitle", "calloutHint", "editReason", "create", "edit", "delete", "fields"], "additionalProperties": false}, "login": {"type": "object", "properties": {"forgotPassword": {"type": "string"}, "title": {"type": "string"}, "hint": {"type": "string"}}, "required": ["forgotPassword", "title", "hint"], "additionalProperties": false}, "invite": {"type": "object", "properties": {"title": {"type": "string"}, "hint": {"type": "string"}, "backToLogin": {"type": "string"}, "createAccount": {"type": "string"}, "alreadyHaveAccount": {"type": "string"}, "emailTooltip": {"type": "string"}, "invalidInvite": {"type": "string"}, "successTitle": {"type": "string"}, "successHint": {"type": "string"}, "successAction": {"type": "string"}, "invalidTokenTitle": {"type": "string"}, "invalidTokenHint": {"type": "string"}, "passwordMismatch": {"type": "string"}, "toast": {"type": "object", "properties": {"accepted": {"type": "string"}}, "required": ["accepted"], "additionalProperties": false}}, "required": ["title", "hint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createAccount", "alreadyHaveAccount", "emailTooltip", "invalidInvite", "successTitle", "successHint", "successAction", "invalidTokenTitle", "invalidTokenHint", "passwordMismatch", "toast"], "additionalProperties": false}, "resetPassword": {"type": "object", "properties": {"title": {"type": "string"}, "hint": {"type": "string"}, "email": {"type": "string"}, "sendResetInstructions": {"type": "string"}, "backToLogin": {"type": "string"}, "newPasswordHint": {"type": "string"}, "invalidTokenTitle": {"type": "string"}, "invalidTokenHint": {"type": "string"}, "expiredTokenTitle": {"type": "string"}, "goToResetPassword": {"type": "string"}, "resetPassword": {"type": "string"}, "newPassword": {"type": "string"}, "repeatNewPassword": {"type": "string"}, "tokenExpiresIn": {"type": "string"}, "successfulRequestTitle": {"type": "string"}, "successfulRequest": {"type": "string"}, "successfulResetTitle": {"type": "string"}, "successfulReset": {"type": "string"}, "passwordMismatch": {"type": "string"}, "invalidLinkTitle": {"type": "string"}, "invalidLinkHint": {"type": "string"}}, "required": ["title", "hint", "email", "sendResetInstructions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newPasswordHint", "invalidTokenTitle", "invalidTokenHint", "expiredTokenTitle", "goToResetPassword", "resetPassword", "newPassword", "repeatNewPassword", "tokenExpiresIn", "successfulRequestTitle", "successfulRequest", "successfulResetTitle", "successful<PERSON><PERSON>t", "passwordMismatch", "invalidLinkTitle", "invalidLinkHint"], "additionalProperties": false}, "workflowExecutions": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "transactionIdLabel": {"type": "string"}, "workflowIdLabel": {"type": "string"}, "progressLabel": {"type": "string"}, "stepsCompletedLabel_one": {"type": "string"}, "stepsCompletedLabel_other": {"type": "string"}, "list": {"type": "object", "properties": {"noRecordsMessage": {"type": "string"}}, "required": ["noRecordsMessage"], "additionalProperties": false}, "history": {"type": "object", "properties": {"sectionTitle": {"type": "string"}, "runningState": {"type": "string"}, "awaitingState": {"type": "string"}, "failedState": {"type": "string"}, "skippedState": {"type": "string"}, "skippedFailureState": {"type": "string"}, "definitionLabel": {"type": "string"}, "outputLabel": {"type": "string"}, "compensateInputLabel": {"type": "string"}, "revertedLabel": {"type": "string"}, "errorLabel": {"type": "string"}}, "required": ["sectionTitle", "runningState", "awaitingState", "failedState", "skippedState", "skippedFailureState", "definitionLabel", "outputLabel", "compensateInputLabel", "<PERSON><PERSON><PERSON><PERSON>", "error<PERSON><PERSON><PERSON>"], "additionalProperties": false}, "state": {"type": "object", "properties": {"done": {"type": "string"}, "failed": {"type": "string"}, "reverted": {"type": "string"}, "invoking": {"type": "string"}, "compensating": {"type": "string"}, "notStarted": {"type": "string"}}, "required": ["done", "failed", "reverted", "invoking", "compensating", "notStarted"], "additionalProperties": false}, "transaction": {"type": "object", "properties": {"state": {"type": "object", "properties": {"waitingToCompensate": {"type": "string"}}, "required": ["waitingToCompensate"], "additionalProperties": false}}, "required": ["state"], "additionalProperties": false}, "step": {"type": "object", "properties": {"state": {"type": "object", "properties": {"skipped": {"type": "string"}, "skippedFailure": {"type": "string"}, "dormant": {"type": "string"}, "timeout": {"type": "string"}}, "required": ["skipped", "skippedFailure", "dormant", "timeout"], "additionalProperties": false}}, "required": ["state"], "additionalProperties": false}}, "required": ["domain", "subtitle", "transactionIdLabel", "workflowIdLabel", "progressLabel", "stepsCompletedLabel_one", "stepsCompleted<PERSON><PERSON><PERSON>_other", "list", "history", "state", "transaction", "step"], "additionalProperties": false}, "productTypes": {"type": "object", "properties": {"domain": {"type": "string"}, "subtitle": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "hint": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "hint", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"value": {"type": "string"}}, "required": ["value"], "additionalProperties": false}}, "required": ["domain", "subtitle", "create", "edit", "delete", "fields"], "additionalProperties": false}, "productTags": {"type": "object", "properties": {"domain": {"type": "string"}, "create": {"type": "object", "properties": {"header": {"type": "string"}, "subtitle": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "subtitle", "successToast"], "additionalProperties": false}, "edit": {"type": "object", "properties": {"header": {"type": "string"}, "subtitle": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["header", "subtitle", "successToast"], "additionalProperties": false}, "delete": {"type": "object", "properties": {"confirmation": {"type": "string"}, "successToast": {"type": "string"}}, "required": ["confirmation", "successToast"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"value": {"type": "string"}}, "required": ["value"], "additionalProperties": false}}, "required": ["domain", "create", "edit", "delete", "fields"], "additionalProperties": false}, "notifications": {"type": "object", "properties": {"domain": {"type": "string"}, "emptyState": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}, "required": ["title", "description"], "additionalProperties": false}, "accessibility": {"type": "object", "properties": {"description": {"type": "string"}}, "required": ["description"], "additionalProperties": false}}, "required": ["domain", "emptyState", "accessibility"], "additionalProperties": false}, "errors": {"type": "object", "properties": {"serverError": {"type": "string"}, "invalidCredentials": {"type": "string"}}, "required": ["serverError", "invalidCredentials"], "additionalProperties": false}, "statuses": {"type": "object", "properties": {"scheduled": {"type": "string"}, "expired": {"type": "string"}, "active": {"type": "string"}, "inactive": {"type": "string"}, "draft": {"type": "string"}, "enabled": {"type": "string"}, "disabled": {"type": "string"}}, "required": ["scheduled", "expired", "active", "inactive", "draft", "enabled", "disabled"], "additionalProperties": false}, "labels": {"type": "object", "properties": {"productVariant": {"type": "string"}, "prices": {"type": "string"}, "available": {"type": "string"}, "inStock": {"type": "string"}, "added": {"type": "string"}, "removed": {"type": "string"}, "from": {"type": "string"}, "to": {"type": "string"}, "beaware": {"type": "string"}, "loading": {"type": "string"}}, "required": ["productVariant", "prices", "available", "inStock", "added", "removed", "from", "to", "beaware", "loading"], "additionalProperties": false}, "fields": {"type": "object", "properties": {"amount": {"type": "string"}, "reference": {"type": "string"}, "reference_id": {"type": "string"}, "refundAmount": {"type": "string"}, "name": {"type": "string"}, "default": {"type": "string"}, "lastName": {"type": "string"}, "firstName": {"type": "string"}, "title": {"type": "string"}, "customTitle": {"type": "string"}, "manageInventory": {"type": "string"}, "inventoryKit": {"type": "string"}, "inventoryItems": {"type": "string"}, "inventoryItem": {"type": "string"}, "requiredQuantity": {"type": "string"}, "description": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "repeatPassword": {"type": "string"}, "confirmPassword": {"type": "string"}, "newPassword": {"type": "string"}, "repeatNewPassword": {"type": "string"}, "categories": {"type": "string"}, "shippingMethod": {"type": "string"}, "configurations": {"type": "string"}, "conditions": {"type": "string"}, "category": {"type": "string"}, "collection": {"type": "string"}, "discountable": {"type": "string"}, "handle": {"type": "string"}, "subtitle": {"type": "string"}, "by": {"type": "string"}, "item": {"type": "string"}, "qty": {"type": "string"}, "limit": {"type": "string"}, "tags": {"type": "string"}, "type": {"type": "string"}, "reason": {"type": "string"}, "none": {"type": "string"}, "all": {"type": "string"}, "search": {"type": "string"}, "percentage": {"type": "string"}, "sales_channels": {"type": "string"}, "customer_groups": {"type": "string"}, "product_tags": {"type": "string"}, "product_types": {"type": "string"}, "product_collections": {"type": "string"}, "status": {"type": "string"}, "code": {"type": "string"}, "value": {"type": "string"}, "disabled": {"type": "string"}, "dynamic": {"type": "string"}, "normal": {"type": "string"}, "years": {"type": "string"}, "months": {"type": "string"}, "days": {"type": "string"}, "hours": {"type": "string"}, "minutes": {"type": "string"}, "totalRedemptions": {"type": "string"}, "countries": {"type": "string"}, "paymentProviders": {"type": "string"}, "refundReason": {"type": "string"}, "fulfillmentProviders": {"type": "string"}, "fulfillmentProvider": {"type": "string"}, "providers": {"type": "string"}, "availability": {"type": "string"}, "inventory": {"type": "string"}, "optional": {"type": "string"}, "note": {"type": "string"}, "automaticTaxes": {"type": "string"}, "taxInclusivePricing": {"type": "string"}, "currency": {"type": "string"}, "address": {"type": "string"}, "address2": {"type": "string"}, "city": {"type": "string"}, "postalCode": {"type": "string"}, "country": {"type": "string"}, "state": {"type": "string"}, "province": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "metadata": {"type": "string"}, "selectCountry": {"type": "string"}, "products": {"type": "string"}, "variants": {"type": "string"}, "orders": {"type": "string"}, "account": {"type": "string"}, "total": {"type": "string"}, "paidTotal": {"type": "string"}, "creditTotal": {"type": "string"}, "totalExclTax": {"type": "string"}, "subtotal": {"type": "string"}, "shipping": {"type": "string"}, "outboundShipping": {"type": "string"}, "returnShipping": {"type": "string"}, "tax": {"type": "string"}, "created": {"type": "string"}, "key": {"type": "string"}, "customer": {"type": "string"}, "date": {"type": "string"}, "order": {"type": "string"}, "fulfillment": {"type": "string"}, "provider": {"type": "string"}, "payment": {"type": "string"}, "items": {"type": "string"}, "salesChannel": {"type": "string"}, "region": {"type": "string"}, "discount": {"type": "string"}, "role": {"type": "string"}, "sent": {"type": "string"}, "salesChannels": {"type": "string"}, "product": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "revokedAt": {"type": "string"}, "true": {"type": "string"}, "false": {"type": "string"}, "giftCard": {"type": "string"}, "tag": {"type": "string"}, "dateIssued": {"type": "string"}, "issuedDate": {"type": "string"}, "expiryDate": {"type": "string"}, "price": {"type": "string"}, "priceTemplate": {"type": "string"}, "height": {"type": "string"}, "width": {"type": "string"}, "length": {"type": "string"}, "weight": {"type": "string"}, "midCode": {"type": "string"}, "hsCode": {"type": "string"}, "ean": {"type": "string"}, "upc": {"type": "string"}, "inventoryQuantity": {"type": "string"}, "barcode": {"type": "string"}, "countryOfOrigin": {"type": "string"}, "material": {"type": "string"}, "thumbnail": {"type": "string"}, "sku": {"type": "string"}, "managedInventory": {"type": "string"}, "allowBackorder": {"type": "string"}, "inStock": {"type": "string"}, "location": {"type": "string"}, "quantity": {"type": "string"}, "variant": {"type": "string"}, "id": {"type": "string"}, "parent": {"type": "string"}, "minSubtotal": {"type": "string"}, "maxSubtotal": {"type": "string"}, "shippingProfile": {"type": "string"}, "summary": {"type": "string"}, "details": {"type": "string"}, "label": {"type": "string"}, "rate": {"type": "string"}, "requiresShipping": {"type": "string"}, "unitPrice": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "draft": {"type": "string"}, "values": {"type": "string"}}, "required": ["amount", "refundAmount", "name", "default", "lastName", "firstName", "title", "customTitle", "manageInventory", "inventoryKit", "inventoryItems", "inventoryItem", "requiredQuantity", "description", "email", "password", "repeatPassword", "confirmPassword", "newPassword", "repeatNewPassword", "categories", "shippingMethod", "configurations", "conditions", "category", "collection", "discountable", "handle", "subtitle", "by", "item", "qty", "limit", "tags", "type", "reason", "none", "all", "search", "percentage", "sales_channels", "customer_groups", "product_tags", "product_types", "product_collections", "status", "code", "value", "disabled", "dynamic", "normal", "years", "months", "days", "hours", "minutes", "totalRedemptions", "countries", "paymentProviders", "refundReason", "fulfillmentProviders", "fulfillmentProvider", "providers", "availability", "inventory", "optional", "note", "automaticTaxes", "taxInclusivePricing", "currency", "address", "address2", "city", "postalCode", "country", "state", "province", "company", "phone", "metadata", "selectCountry", "products", "variants", "orders", "account", "total", "paidTotal", "totalExclTax", "subtotal", "shipping", "outboundShipping", "returnShipping", "tax", "created", "key", "customer", "date", "order", "fulfillment", "provider", "payment", "items", "salesChannel", "region", "discount", "role", "sent", "salesChannels", "product", "createdAt", "updatedAt", "revokedAt", "true", "false", "giftCard", "tag", "dateIssued", "issuedDate", "expiryDate", "price", "priceTemplate", "height", "width", "length", "weight", "midCode", "hsCode", "ean", "upc", "inventoryQuantity", "barcode", "countryOf<PERSON><PERSON>in", "material", "thumbnail", "sku", "managedInventory", "allowBackorder", "inStock", "location", "quantity", "variant", "id", "parent", "minSubtotal", "maxSubtotal", "shippingProfile", "summary", "details", "label", "rate", "requiresShipping", "unitPrice", "startDate", "endDate", "draft", "values"], "additionalProperties": false}, "dateTime": {"type": "object", "properties": {"years_one": {"type": "string"}, "years_other": {"type": "string"}, "months_one": {"type": "string"}, "months_other": {"type": "string"}, "weeks_one": {"type": "string"}, "weeks_other": {"type": "string"}, "days_one": {"type": "string"}, "days_other": {"type": "string"}, "hours_one": {"type": "string"}, "hours_other": {"type": "string"}, "minutes_one": {"type": "string"}, "minutes_other": {"type": "string"}, "seconds_one": {"type": "string"}, "seconds_other": {"type": "string"}}, "required": ["years_one", "years_other", "months_one", "months_other", "weeks_one", "weeks_other", "days_one", "days_other", "hours_one", "hours_other", "minutes_one", "minutes_other", "seconds_one", "seconds_other"], "additionalProperties": false}}, "required": ["$schema", "general", "json", "metadata", "validation", "actions", "operators", "app", "dataGrid", "filters", "errorBoundary", "addresses", "email", "transferOwnership", "sales_channels", "products", "collections", "categories", "inventory", "giftCards", "customers", "customerGroups", "orders", "draftOrders", "stockLocations", "shippingProfile", "taxRegions", "promotions", "campaigns", "priceLists", "profile", "users", "store", "regions", "taxes", "locations", "reservations", "salesChannels", "apiKeyManagement", "returnReasons", "login", "invite", "resetPassword", "workflowExecutions", "productTypes", "productTags", "notifications", "errors", "statuses", "labels", "fields", "dateTime"], "additionalProperties": false}