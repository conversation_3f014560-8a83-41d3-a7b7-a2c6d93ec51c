import { useQueryParams } from "../../../../../hooks/use-query-params"

type UseApprovalsTableQueryProps = {
  pageSize: number
  prefix?: string
}

export const useApprovalsTableQuery = ({
  pageSize,
  prefix,
}: UseApprovalsTableQueryProps) => {
  const queryObject = useQueryParams(
    ["offset", "q", "status", "created_at", "updated_at", "order"],
    prefix
  )

  const { offset, q, status, created_at, updated_at, order } = queryObject

  const searchParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    q,
    status: status?.split(","),
    created_at: created_at ? JSON.parse(created_at) : undefined,
    updated_at: updated_at ? JSON.parse(updated_at) : undefined,
    order: order ? order : "-created_at",
  }

  return {
    searchParams,
    raw: queryObject,
  }
}
