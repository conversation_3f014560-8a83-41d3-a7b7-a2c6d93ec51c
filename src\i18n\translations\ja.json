{"$schema": "./$schema.json", "general": {"ascending": "昇順", "descending": "降順", "add": "追加", "start": "開始", "end": "終了", "open": "開く", "close": "閉じる", "apply": "適用", "range": "範囲", "search": "検索", "of": "の", "results": "結果", "pages": "ページ", "next": "次へ", "prev": "前へ", "is": "は", "timeline": "タイムライン", "success": "成功", "warning": "警告", "tip": "ヒント", "error": "エラー", "select": "選択", "selected": "選択済み", "enabled": "有効", "disabled": "無効", "expired": "期限切れ", "active": "アクティブ", "revoked": "取り消し済み", "new": "新規", "modified": "変更済み", "added": "追加済み", "removed": "削除済み", "admin": "管理者", "store": "ストア", "details": "詳細", "items_one": "{{count}}個のアイテム", "items_other": "{{count}}個のアイテム", "countSelected": "{{count}}個選択済み", "countOfTotalSelected": "{{total}}個中{{count}}個選択済み", "plusCount": "+ {{count}}", "plusCountMore": "+ さらに{{count}}個", "areYouSure": "本当によろしいですか？", "noRecordsFound": "レコードが見つかりません", "typeToConfirm": "確認のために{val}と入力してください:", "noResultsTitle": "結果なし", "noResultsMessage": "フィルターまたは検索クエリを変更してみてください", "noSearchResults": "検索結果なし", "noSearchResultsFor": "<0>'{{query}}'</0>の検索結果はありません", "noRecordsTitle": "レコードなし", "noRecordsMessage": "表示するレコードがありません", "unsavedChangesTitle": "このフォームから離れてもよろしいですか？", "unsavedChangesDescription": "保存されていない変更があります。このフォームを終了すると、これらの変更は失われます。", "includesTaxTooltip": "この列の価格は税込みです。", "excludesTaxTooltip": "この列の価格は税抜きです。", "noMoreData": "これ以上データはありません"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}}個のキー", "numberOfKeys_other": "{{count}}個のキー", "drawer": {"header_one": "JSON <0>· {{count}}個のキー</0>", "header_other": "JSON <0>· {{count}}個のキー</0>", "description": "このオブジェクトのJSONデータを表示します。"}}, "metadata": {"header": "メタデータ", "numberOfKeys_one": "{{count}}個のキー", "numberOfKeys_other": "{{count}}個のキー", "edit": {"header": "メタデータを編集", "description": "このオブジェクトのメタデータを編集します。", "successToast": "メタデータが正常に更新されました。", "actions": {"insertRowAbove": "上に行を挿入", "insertRowBelow": "下に行を挿入", "deleteRow": "行を削除"}, "labels": {"key": "キー", "value": "値"}, "complexRow": {"label": "一部の行が無効になっています", "description": "このオブジェクトには、配列やオブジェクトなどのプリミティブでないメタデータが含まれており、ここでは編集できません。無効になっている行を編集するには、APIを直接使用してください。", "tooltip": "この行はプリミティブでないデータを含むため無効になっています。"}}}, "validation": {"mustBeInt": "値は数値でなければなりません。", "mustBePositive": "値は正の数値でなければなりません。"}, "actions": {"save": "保存", "saveAsDraft": "下書きとして保存", "copy": "コピー", "copied": "コピーしました", "duplicate": "複製", "publish": "公開", "create": "作成", "delete": "削除", "remove": "除去", "revoke": "無効", "cancel": "キャンセル", "forceConfirm": "強制確認", "continueEdit": "編集を続ける", "enable": "有効化", "disable": "無効化", "undo": "元に戻す", "complete": "完了", "viewDetails": "詳細を表示", "back": "戻る", "close": "閉じる", "showMore": "もっと見る", "continue": "続ける", "continueWithEmail": "メールで続ける", "idCopiedToClipboard": "IDをクリップボードにコピーしました", "addReason": "理由を追加", "addNote": "メモを追加", "reset": "リセット", "confirm": "確認", "edit": "編集", "addItems": "項目を追加", "download": "ダウンロード", "clear": "クリア", "clearAll": "すべてクリア", "apply": "適用", "add": "追加", "select": "選択", "browse": "参照", "logout": "ログアウト", "hide": "非表示", "export": "エクスポート", "import": "インポート", "cannotUndo": "この操作は元に戻せません"}, "operators": {"in": "含む"}, "app": {"search": {"label": "検索", "title": "検索", "description": "注文、商品、顧客など、ストア全体を検索します。", "allAreas": "すべての領域", "navigation": "ナビゲーション", "openResult": "結果を開く", "showMore": "もっと見る", "placeholder": "何でも検索...", "noResultsTitle": "結果が見つかりません", "noResultsMessage": "検索に一致するものが見つかりませんでした。", "emptySearchTitle": "検索するには入力してください", "emptySearchMessage": "キーワードまたはフレーズを入力して探索してください。", "loadMore": "さらに{{count}}件読み込む", "groups": {"all": "すべての領域", "customer": "顧客", "customerGroup": "顧客グループ", "product": "商品", "productVariant": "商品バリエーション", "inventory": "在庫", "reservation": "予約", "category": "カテゴリ", "collection": "コレクション", "order": "注文", "promotion": "プロモーション", "campaign": "キャンペーン", "priceList": "価格リスト", "user": "ユーザー", "region": "地域", "taxRegion": "税地域", "returnReason": "返品理由", "salesChannel": "販売チャネル", "productType": "商品タイプ", "productTag": "商品タグ", "location": "拠点", "shippingProfile": "配送プロファイル", "publishableApiKey": "公開可能なAPIキー", "secretApiKey": "秘密のAPIキー", "command": "コマンド", "navigation": "ナビゲーション"}}, "keyboardShortcuts": {"pageShortcut": "飛び先", "settingShortcut": "設定", "commandShortcut": "コマンド", "then": "次に", "navigation": {"goToOrders": "注文", "goToProducts": "商品", "goToCollections": "コレクション", "goToCategories": "カテゴリ", "goToCustomers": "顧客", "goToCustomerGroups": "顧客グループ", "goToInventory": "在庫", "goToReservations": "予約", "goToPriceLists": "価格リスト", "goToPromotions": "プロモーション", "goToCampaigns": "キャンペーン"}, "settings": {"goToSettings": "設定", "goToStore": "ストア", "goToUsers": "ユーザー", "goToRegions": "地域", "goToTaxRegions": "税地域", "goToSalesChannels": "販売チャネル", "goToProductTypes": "商品タイプ", "goToLocations": "拠点", "goToPublishableApiKeys": "公開可能なAPIキー", "goToSecretApiKeys": "秘密のAPIキー", "goToWorkflows": "ワークフロー", "goToProfile": "プロファイル", "goToReturnReasons": "返品理由"}}, "menus": {"user": {"documentation": "ドキュメント", "changelog": "変更履歴", "shortcuts": "ショートカット", "profileSettings": "プロファイル設定", "theme": {"label": "テーマ", "dark": "ダーク", "light": "ライト", "system": "システム"}}, "store": {"label": "ストア", "storeSettings": "ストア設定"}, "actions": {"logout": "ログアウト"}}, "nav": {"accessibility": {"title": "ナビゲーション", "description": "ダッシュボードのナビゲーションメニュー。"}, "common": {"extensions": "拡張機能"}, "main": {"store": "ストア", "storeSettings": "ストア設定"}, "settings": {"header": "設定", "general": "一般", "developer": "開発者", "myAccount": "マイアカウント"}}}, "dataGrid": {"columns": {"view": "表示", "resetToDefault": "デフォルトにリセット", "disabled": "表示列の変更は無効になっています。"}, "shortcuts": {"label": "ショートカット", "commands": {"undo": "元に戻す", "redo": "やり直し", "copy": "コピー", "paste": "貼り付け", "edit": "編集", "delete": "削除", "clear": "クリア", "moveUp": "上に移動", "moveDown": "下に移動", "moveLeft": "左に移動", "moveRight": "右に移動", "moveTop": "一番上に移動", "moveBottom": "一番下に移動", "selectDown": "下を選択", "selectUp": "上を選択", "selectColumnDown": "下の列を選択", "selectColumnUp": "上の列を選択", "focusToolbar": "ツールバーにフォーカス", "focusCancel": "キャンセルにフォーカス"}}, "errors": {"fixError": "エラーを修正", "count_one": "{{count}}個のエラー", "count_other": "{{count}}個のエラー"}}, "filters": {"date": {"today": "今日", "lastSevenDays": "過去7日間", "lastThirtyDays": "過去30日間", "lastNinetyDays": "過去90日間", "lastTwelveMonths": "過去12ヶ月", "custom": "カスタム", "from": "開始日", "to": "終了日"}, "compare": {"lessThan": "未満", "greaterThan": "以上", "exact": "一致", "range": "範囲", "lessThanLabel": "{{value}}未満", "greaterThanLabel": "{{value}}以上", "andLabel": "かつ"}, "radio": {"yes": "はい", "no": "いいえ", "true": "真", "false": "偽"}, "addFilter": "フィルター追加"}, "errorBoundary": {"badRequestTitle": "400 - 不正なリクエスト", "badRequestMessage": "構文が不正なため、サーバーがリクエストを理解できませんでした。", "notFoundTitle": "404 - このアドレスにページが存在しません", "notFoundMessage": "URLを確認して再試行するか、検索バーを使用して探しているものを見つけてください。", "internalServerErrorTitle": "500 - 内部サーバーエラー", "internalServerErrorMessage": "サーバーで予期せぬエラーが発生しました。後ほど再試行してください。", "defaultTitle": "エラーが発生しました", "defaultMessage": "このページの表示中に予期せぬエラーが発生しました。", "noMatchMessage": "お探しのページは存在しません。", "backToDashboard": "ダッシュボードに戻る"}, "addresses": {"title": "住所", "shippingAddress": {"header": "配送先住所", "editHeader": "配送先住所の編集", "editLabel": "配送先住所", "label": "配送先住所"}, "billingAddress": {"header": "請求先住所", "editHeader": "請求先住所の編集", "editLabel": "請求先住所", "label": "請求先住所", "sameAsShipping": "配送先住所と同じ"}, "contactHeading": "連絡先", "locationHeading": "所在地"}, "email": {"editHeader": "Eメールを編集", "editLabel": "Eメール", "label": "Eメール"}, "transferOwnership": {"header": "所有権の移転", "label": "所有権の移転", "details": {"order": "注文詳細", "draft": "下書き詳細"}, "currentOwner": {"label": "現在の所有者", "hint": "注文の現在の所有者です。"}, "newOwner": {"label": "新しい所有者", "hint": "注文を移転する新しい所有者です。"}, "validation": {"mustBeDifferent": "新しい所有者は現在の所有者と異なる必要があります。", "required": "新しい所有者は必須です。"}}, "sales_channels": {"availableIn": "<1>{{y}}</1>の販売チャネルのうち<0>{{x}}</0>で利用可能"}, "products": {"domain": "商品", "list": {"noRecordsMessage": "最初の商品を作成して販売を開始しましょう。"}, "edit": {"header": "商品を編集", "description": "商品の詳細を編集します。", "successToast": "商品「{{title}}」が正常に更新されました。"}, "create": {"title": "商品を作成", "description": "新しい商品を作成します。", "header": "一般", "tabs": {"details": "詳細", "organize": "整理", "variants": "バリエーション", "inventory": "在庫キット"}, "errors": {"variants": "少なくとも1つのバリエーションを選択してください。", "options": "少なくとも1つのオプションを作成してください。", "uniqueSku": "SKUは一意である必要があります。"}, "inventory": {"heading": "在庫キット", "label": "バリエーションの在庫キットに在庫アイテムを追加します。", "itemPlaceholder": "在庫アイテムを選択", "quantityPlaceholder": "このキットに必要な数量は？"}, "variants": {"header": "バリエーション", "subHeadingTitle": "はい、これはバリエーションのある商品です", "subHeadingDescription": "チェックを外すと、デフォルトのバリエーションを作成します", "optionTitle": {"placeholder": "サイズ"}, "optionValues": {"placeholder": "小、中、大"}, "productVariants": {"label": "商品バリエーション", "hint": "このランキングはストアフロントでのバリエーションの順序に影響します。", "alert": "バリエーションを作成するにはオプションを追加してください。", "tip": "チェックをしていないバリエーションは作成されません。後でバリエーションを作成・編集できますが、このリストは商品オプションのバリエーションに適合します。"}, "productOptions": {"label": "商品オプション", "hint": "色、サイズなど、商品のオプションを定義します。"}}, "successToast": "商品「{{title}}」が正常に作成されました。"}, "export": {"header": "商品リストをエクスポート", "description": "商品リストをCSVファイルにエクスポートします。", "success": {"title": "エクスポートを処理中です", "description": "データのエクスポートには数分かかる場合があります。完了時にお知らせします。"}, "filters": {"title": "フィルター", "description": "テーブル概要でフィルターを適用してこのビューを調整します"}, "columns": {"title": "列", "description": "特定のニーズに合わせてエクスポートするデータをカスタマイズします"}}, "import": {"header": "商品リストをインポート", "uploadLabel": "商品をインポート", "uploadHint": "CSVファイルをドラッグ＆ドロップするか、クリックしてアップロードします", "description": "事前に定義されたフォーマットのCSVファイルを提供して商品をインポートします", "template": {"title": "リストの配置方法が不明ですか？", "description": "正しいフォーマットに従っていることを確認するために、以下のテンプレートをダウンロードしてください。"}, "upload": {"title": "CSVファイルをアップロード", "description": "インポートにより商品を追加または更新できます。既存の商品を更新するには、既存のハンドルとIDを使用する必要があります。既存のバリエーションを更新するには、既存のIDを使用する必要があります。商品をインポートする前に確認を求められます。", "preprocessing": "前処理中...", "productsToCreate": "作成される商品", "productsToUpdate": "更新される商品"}, "success": {"title": "インポートを処理中です", "description": "データのインポートには時間がかかる場合があります。完了時にお知らせします。"}}, "deleteWarning": "商品「{{title}}」を削除しようとしています。この操作は元に戻せません。", "variants": {"header": "バリエーション", "empty": {"heading": "バリエーションはありません", "description": "表示するバリエーションはありません。"}, "filtered": {"heading": "結果はありません", "description": "現在のフィルター条件に一致するバリエーションはありません。"}}, "attributes": "属性", "editAttributes": "属性を編集", "editOptions": "オプションを編集", "editPrices": "価格を編集", "media": {"label": "メディア", "editHint": "ストアフロントで商品を展示するためにメディアを追加します。", "makeThumbnail": "サムネイルを作成", "uploadImagesLabel": "画像をアップロード", "uploadImagesHint": "ここに画像をドラッグ＆ドロップするか、クリックしてアップロードします。", "invalidFileType": "「{{name}}」はサポートされていないファイルタイプです。サポートされているファイルタイプは次のとおりです：{{types}}。", "failedToUpload": "追加されたメディアのアップロードに失敗しました。もう一度お試しください。", "deleteWarning_one": "{{count}}枚の画像を削除しようとしています。この操作は元に戻せません。", "deleteWarning_other": "{{count}}枚の画像を削除しようとしています。この操作は元に戻せません。", "deleteWarningWithThumbnail_one": "サムネイルを含む{{count}}枚の画像を削除しようとしています。この操作は元に戻せません。", "deleteWarningWithThumbnail_other": "サムネイルを含む{{count}}枚の画像を削除しようとしています。この操作は元に戻せません。", "thumbnailTooltip": "サムネイル", "galleryLabel": "ギャラリー", "downloadImageLabel": "現在の画像をダウンロード", "deleteImageLabel": "現在の画像を削除", "emptyState": {"header": "まだメディアはありません", "description": "ストアフロントで商品を展示するためにメディアを追加します。", "action": "メディアを追加"}, "successToast": "メディアは正常に更新されました。"}, "discountableHint": "チェックを外すと、この商品に割引は適用されません。", "noSalesChannels": "どの販売チャネルでも利用できません", "variantCount_one": "{{count}}個のバリエーション", "variantCount_other": "{{count}}個のバリエーション", "deleteVariantWarning": "バリエーション「{{title}}」を削除しようとしています。この操作は元に戻せません。", "productStatus": {"draft": "下書き", "published": "公開済み", "proposed": "提案済み", "rejected": "却下"}, "fields": {"title": {"label": "タイトル", "hint": "商品に短く明確なタイトルを付けてください。<0/>検索エンジンには50〜60文字が推奨されています。", "placeholder": "ウインタージャケット"}, "subtitle": {"label": "サブタイトル", "placeholder": "暖かく快適"}, "handle": {"label": "ハンドル", "tooltip": "ハンドルはストアフロントで商品を参照するために使用されます。指定しない場合、商品タイトルからハンドルが生成されます。", "placeholder": "ウインタージャケット"}, "description": {"label": "説明", "hint": "商品に短く明確な説明を付けてください。<0/>検索エンジンには120〜160文字が推奨されています。", "placeholder": "暖かく快適なジャケット"}, "discountable": {"label": "割引可能", "hint": "チェックを外すと、この商品に割引は適用されません"}, "type": {"label": "タイプ"}, "collection": {"label": "コレクション"}, "categories": {"label": "カテゴリー"}, "tags": {"label": "タグ"}, "sales_channels": {"label": "販売チャネル", "hint": "未選択の場合、この商品はデフォルトの販売チャネルでのみ利用可能になります。"}, "countryOrigin": {"label": "原産国"}, "material": {"label": "素材"}, "width": {"label": "幅"}, "length": {"label": "長さ"}, "height": {"label": "高さ"}, "weight": {"label": "重さ"}, "options": {"label": "商品オプション", "hint": "オプションは商品の色、サイズなどを定義するために使用されます", "add": "オプションを追加", "optionTitle": "オプションタイトル", "optionTitlePlaceholder": "色", "variations": "バリエーション（カンマ区切り）", "variantionsPlaceholder": "赤,青,緑"}, "variants": {"label": "商品バリエーション", "hint": "チェックを外したバリエーションは作成されません。このランキングはフロントエンドでのバリエーションのランク付けに影響します。"}, "mid_code": {"label": "中分類コード"}, "hs_code": {"label": "HSコード"}}, "variant": {"edit": {"header": "バリエーションを編集", "success": "商品バリエーションが正常に編集されました"}, "create": {"header": "バリエーション詳細"}, "deleteWarning": "このバリエーションを削除してもよろしいですか？", "pricesPagination": "1 - {{total}}件中{{current}}件の価格", "tableItemAvailable": "{{availableCount}}個利用可能", "tableItem_one": "{{locationCount}}の拠点で{{availableCount}}個利用可能", "tableItem_other": "{{locationCount}}の拠点で{{availableCount}}個利用可能", "inventory": {"notManaged": "管理されていません", "manageItems": "在庫アイテムを管理", "notManagedDesc": "このバリエーションの在庫は管理されていません。「在庫を管理」をオンにしてバリエーションの在庫を追跡します。", "manageKit": "在庫キットを管理", "navigateToItem": "在庫アイテムに移動", "actions": {"inventoryItems": "在庫アイテムに移動", "inventoryKit": "在庫アイテムを表示"}, "inventoryKit": "在庫キット", "inventoryKitHint": "このバリエーションは複数の在庫アイテムで構成されていますか？", "validation": {"itemId": "在庫アイテムを選択してください。", "quantity": "数量は必須です。正の数値を入力してください。"}, "header": "在庫と在庫管理", "editItemDetails": "アイテム詳細を編集", "manageInventoryLabel": "在庫を管理", "manageInventoryHint": "有効にすると、注文や返品が作成されたときに在庫数量を変更します。", "allowBackordersLabel": "取り寄せを許可", "allowBackordersHint": "有効にすると、利用可能な数量がない場合でも顧客がバリエーションを購入できます。", "toast": {"levelsBatch": "在庫レベルが更新されました。", "update": "在庫アイテムが正常に更新されました。", "updateLevel": "在庫レベルが正常に更新されました。", "itemsManageSuccess": "在庫アイテムが正常に更新されました。"}}}, "options": {"header": "オプション", "edit": {"header": "オプションを編集", "successToast": "オプション「{{title}}」が正常に更新されました。"}, "create": {"header": "オプションを作成", "successToast": "オプション「{{title}}」が正常に作成されました。"}, "deleteWarning": "商品オプション「{{title}}」を削除しようとしています。この操作は元に戻せません。"}, "organization": {"header": "整理", "edit": {"header": "整理を編集", "toasts": {"success": "「{{title}}」の整理が正常に更新されました。"}}}, "toasts": {"delete": {"success": {"header": "商品が削除されました", "description": "「{{title}}」が正常に削除されました。"}, "error": {"header": "商品の削除に失敗しました"}}}}, "collections": {"domain": "コレクション", "subtitle": "商品をコレクションに整理します。", "createCollection": "コレクションを作成", "createCollectionHint": "商品を整理するための新しいコレクションを作成します。", "createSuccess": "コレクションが正常に作成されました。", "editCollection": "コレクションを編集", "handleTooltip": "ハンドルはストアフロントでコレクションを参照するために使用されます。指定しない場合、コレクションのタイトルからハンドルが生成されます。", "deleteWarning": "コレクション「{{title}}」を削除しようとしています。この操作は元に戻せません。", "removeSingleProductWarning": "商品「{{title}}」をコレクションから削除しようとしています。この操作は元に戻せません。", "removeProductsWarning_one": "{{count}}個の商品をコレクションから削除しようとしています。この操作は元に戻せません。", "removeProductsWarning_other": "{{count}}個の商品をコレクションから削除しようとしています。この操作は元に戻せません。", "products": {"list": {"noRecordsMessage": "コレクションに商品がありません。"}, "add": {"successToast_one": "商品がコレクションに正常に追加されました。", "successToast_other": "商品がコレクションに正常に追加されました。"}, "remove": {"successToast_one": "商品がコレクションから正常に削除されました。", "successToast_other": "商品がコレクションから正常に削除されました。"}}}, "categories": {"domain": "カテゴリー", "subtitle": "商品をカテゴリーに整理し、それらのカテゴリーのランキングと階層を管理します。", "create": {"header": "カテゴリーを作成", "hint": "商品を整理するための新しいカテゴリーを作成します。", "tabs": {"details": "詳細", "organize": "ランキングを整理"}, "successToast": "カテゴリー「{{name}}」が正常に作成されました。"}, "edit": {"header": "カテゴリーを編集", "description": "カテゴリーを編集して詳細を更新します。", "successToast": "カテゴリーが正常に更新されました。"}, "delete": {"confirmation": "カテゴリー「{{name}}」を削除しようとしています。この操作は元に戻せません。", "successToast": "カテゴリー「{{name}}」が正常に削除されました。"}, "products": {"add": {"disabledTooltip": "この商品は既にこのカテゴリーに含まれています。", "successToast_one": "{{count}}個の商品をカテゴリーに追加しました。", "successToast_other": "{{count}}個の商品をカテゴリーに追加しました。"}, "remove": {"confirmation_one": "{{count}}個の商品をカテゴリーから削除しようとしています。この操作は元に戻せません。", "confirmation_other": "{{count}}個の商品をカテゴリーから削除しようとしています。この操作は元に戻せません。", "successToast_one": "{{count}}個の商品をカテゴリーから削除しました。", "successToast_other": "{{count}}個の商品をカテゴリーから削除しました。"}, "list": {"noRecordsMessage": "このカテゴリーに商品はありません。"}}, "organize": {"header": "整理", "action": "ランキングを編集"}, "fields": {"visibility": {"label": "可視性", "internal": "内部", "public": "公開"}, "status": {"label": "ステータス", "active": "アクティブ", "inactive": "非アクティブ"}, "path": {"label": "パス", "tooltip": "カテゴリーの完全なパスを表示します。"}, "children": {"label": "子カテゴリー"}, "new": {"label": "新規"}}}, "inventory": {"domain": "在庫", "subtitle": "在庫アイテムを管理する", "reserved": "予約済み", "available": "利用可能", "locationLevels": "拠点", "associatedVariants": "関連バリエーション", "manageLocations": "拠点を管理", "deleteWarning": "在庫アイテムを削除しようとしています。この操作は元に戻せません。", "editItemDetails": "アイテム詳細を編集", "create": {"title": "在庫アイテムを作成", "details": "詳細", "availability": "在庫状況", "locations": "拠点", "attributes": "属性", "requiresShipping": "配送が必要", "requiresShippingHint": "この在庫アイテムは配送が必要ですか？", "successToast": "在庫アイテムが正常に作成されました。"}, "reservation": {"header": "{{itemName}}の予約", "editItemDetails": "予約を編集", "lineItemId": "ラインアイテムID", "orderID": "注文ID", "description": "説明", "location": "拠点", "inStockAtLocation": "この拠点の在庫", "availableAtLocation": "この拠点で利用可能", "reservedAtLocation": "この拠点で予約済み", "reservedAmount": "予約数量", "create": "予約を作成", "itemToReserve": "予約するアイテム", "quantityPlaceholder": "予約する数量を入力してください", "descriptionPlaceholder": "どのタイプの予約ですか？", "successToast": "予約が正常に作成されました。", "updateSuccessToast": "予約が正常に更新されました。", "deleteSuccessToast": "予約が正常に削除されました。", "errors": {"noAvaliableQuantity": "在庫ロケーションに利用可能な数量がありません。", "quantityOutOfRange": "最小数量は1、最大数量は{{max}}です"}}, "adjustInventory": {"errors": {"stockedQuantity": "在庫数量を予約数量{{quantity}}より少ない数に更新することはできません。"}}, "toast": {"updateLocations": "拠点が正常に更新されました。", "updateLevel": "在庫レベルが正常に更新されました。", "updateItem": "在庫アイテムが正常に更新されました。"}}, "giftCards": {"domain": "ギフトカード", "editGiftCard": "ギフトカードを編集", "createGiftCard": "ギフトカードを作成", "createGiftCardHint": "ストアで支払い方法として使用できるギフトカードを手動で作成します。", "selectRegionFirst": "まず地域を選択してください", "deleteGiftCardWarning": "ギフトカード「{{code}}」を削除しようとしています。この操作は元に戻せません。", "balanceHigherThanValue": "残高は元の金額を超えることはできません。", "balanceLowerThanZero": "残高をマイナスにすることはできません。", "expiryDateHint": "ギフトカードの有効期限に関する法律は国によって異なります。有効期限を設定する前に、現地の法令を確認してください。", "regionHint": "ギフトカードの地域を変更すると、通貨も変更され、金銭的価値に影響を与える可能性があります。", "enabledHint": "ギフトカードを有効にするか無効にするかを指定します。", "balance": "残高", "currentBalance": "現在の残高", "initialBalance": "初期残高", "personalMessage": "個人メッセージ", "recipient": "受取人"}, "customers": {"domain": "顧客", "list": {"noRecordsMessage": "顧客情報がここに表示されます。"}, "create": {"header": "顧客を作成", "hint": "新しい顧客を作成し、その詳細を管理します。", "successToast": "顧客「{{email}}」が正常に作成されました。"}, "groups": {"label": "顧客グループ", "remove": "顧客を「{{name}}」グループから削除してもよろしいですか？", "removeMany": "顧客を以下の顧客グループから削除してもよろしいですか：{{groups}}？", "alreadyAddedTooltip": "顧客は既にこの顧客グループに所属しています。", "list": {"noRecordsMessage": "この顧客はどのグループにも所属していません。"}, "add": {"success": "顧客を追加しました：{{groups}}", "list": {"noRecordsMessage": "まず顧客グループを作成してください。"}}, "removed": {"success": "顧客を削除しました：{{groups}}", "list": {"noRecordsMessage": "まず顧客グループを作成してください。"}}}, "edit": {"header": "顧客を編集", "emailDisabledTooltip": "登録済み顧客のメールアドレスは変更できません。", "successToast": "顧客「{{email}}」が正常に更新されました。"}, "delete": {"title": "顧客を削除", "description": "顧客「{{email}}」を削除しようとしています。この操作は元に戻せません。", "successToast": "顧客「{{email}}」が正常に削除されました。"}, "fields": {"guest": "ゲスト", "registered": "登録済み", "groups": "グループ"}, "registered": "登録済み", "guest": "ゲスト", "hasAccount": "アカウントあり"}, "customerGroups": {"domain": "顧客グループ", "subtitle": "顧客をグループに整理します。グループごとに異なるプロモーションや価格を設定できます。", "create": {"header": "顧客グループを作成", "hint": "顧客をセグメント化するための新しい顧客グループを作成します。", "successToast": "顧客グループ「{{name}}」が正常に作成されました。"}, "edit": {"header": "顧客グループを編集", "successToast": "顧客グループ「{{name}}」が正常に更新されました。"}, "delete": {"title": "顧客グループを削除", "description": "顧客グループ「{{name}}」を削除しようとしています。この操作は元に戻せません。", "successToast": "顧客グループ「{{name}}」が正常に削除されました。"}, "customers": {"alreadyAddedTooltip": "この顧客は既にグループに追加されています。", "add": {"successToast_one": "顧客がグループに正常に追加されました。", "successToast_other": "顧客がグループに正常に追加されました。", "list": {"noRecordsMessage": "まず顧客を作成してください。"}}, "remove": {"title_one": "顧客を削除", "title_other": "顧客を削除", "description_one": "{{count}}人の顧客を顧客グループから削除しようとしています。この操作は元に戻せません。", "description_other": "{{count}}人の顧客を顧客グループから削除しようとしています。この操作は元に戻せません。"}, "list": {"noRecordsMessage": "このグループには顧客がいません。"}}}, "orders": {"domain": "注文", "claim": "クレーム", "exchange": "交換", "return": "返品", "cancelWarning": "注文{{id}}をキャンセルしようとしています。この操作は元に戻せません。", "onDateFromSalesChannel": "{{salesChannel}}から{{date}}", "list": {"noRecordsMessage": "あなたの注文がここに表示されます。"}, "summary": {"requestReturn": "返品をリクエスト", "allocateItems": "アイテムを割り当てる", "editOrder": "注文を編集", "editOrderContinue": "注文編集を続ける", "inventoryKit": "{{count}}個の在庫アイテムで構成", "itemTotal": "アイテム合計", "shippingTotal": "配送合計", "discountTotal": "割引合計", "taxTotalIncl": "税金合計（税込）", "itemSubtotal": "アイテム小計", "shippingSubtotal": "配送小計", "discountSubtotal": "割引小計", "taxTotal": "税金合計"}, "transfer": {"title": "所有権の移転", "requestSuccess": "注文移転リクエストが{{email}}に送信されました。", "currentOwner": "現在の所有者", "newOwner": "新しい所有者", "currentOwnerDescription": "この注文に現在関連付けられている顧客。", "newOwnerDescription": "この注文を移転する顧客。"}, "payment": {"title": "支払い", "isReadyToBeCaptured": "支払い<0/>が確定しました。", "totalPaidByCustomer": "顧客による支払い合計", "capture": "支払いが確定", "capture_short": "確定済み", "refund": "返金", "markAsPaid": "支払い済みとしてマーク", "statusLabel": "支払いステータス", "statusTitle": "支払いステータス", "status": {"notPaid": "未払い", "authorized": "承認済み", "partiallyAuthorized": "一部承認", "awaiting": "待機中", "captured": "確定済み", "partiallyRefunded": "一部返金", "partiallyCaptured": "一部支払い済み", "refunded": "返金済み", "canceled": "キャンセル済み", "requiresAction": "対応が必要"}, "capturePayment": "{{amount}}の支払いが確定されます。", "capturePaymentSuccess": "{{amount}}の支払いが正常に確定されました", "markAsPaidPayment": "{{amount}}の支払いが支払い済みとしてマークされます。", "markAsPaidPaymentSuccess": "{{amount}}の支払いが正常に支払い済みとしてマークされました", "createRefund": "返金を作成", "refundPaymentSuccess": "{{amount}}の返金が成功しました", "createRefundWrongQuantity": "数量は1から{{number}}の間の数字である必要があります", "refundAmount": "{{ amount }}を返金", "paymentLink": "{{ amount }}の支払いリンクをコピー", "selectPaymentToRefund": "返金する支払いを選択"}, "edits": {"title": "注文を編集", "confirm": "編集を確認", "confirmText": "注文編集を確認しようとしています。この操作は元に戻せません。", "cancel": "編集をキャンセル", "currentItems": "現在のアイテム", "currentItemsDescription": "アイテムの数量を調整または削除。", "addItemsDescription": "注文に新しいアイテムを追加できます。", "addItems": "アイテムを追加", "amountPaid": "支払い済み金額", "newTotal": "新しい合計", "differenceDue": "差額", "create": "注文を編集", "currentTotal": "現在の合計", "noteHint": "編集の内部メモを追加", "cancelSuccessToast": "注文編集がキャンセルされました", "createSuccessToast": "注文編集リクエストが作成されました", "activeChangeError": "注文にはすでにアクティブな注文変更（返品、クレーム、交換など）があります。注文を編集する前に変更を完了またはキャンセルしてください。", "panel": {"title": "注文編集がリクエストされました", "titlePending": "注文編集が保留中です"}, "toast": {"canceledSuccessfully": "注文編集がキャンセルされました", "confirmedSuccessfully": "注文編集が確認されました"}, "validation": {"quantityLowerThanFulfillment": "数量を出荷済みの数量以下に設定することはできません"}}, "edit": {"email": {"title": "Eメールを編集", "requestSuccess": "注文のEメールが「{{email}}」に更新されました。"}, "shippingAddress": {"title": "配送先住所を編集", "requestSuccess": "注文の配送先住所が更新されました。"}, "billingAddress": {"title": "請求先住所を編集", "requestSuccess": "注文の請求先住所が更新されました。"}}, "returns": {"create": "返品を作成", "confirm": "返品を確認", "confirmText": "返品を確認しようとしています。この操作は元に戻せません。", "inbound": "入庫", "outbound": "出庫", "sendNotification": "通知を送信", "sendNotificationHint": "顧客に返品について通知します。", "returnTotal": "返品合計", "inboundTotal": "入庫合計", "refundAmount": "返金額", "outstandingAmount": "未払い金額", "reason": "理由", "reasonHint": "顧客がアイテムを返品したい理由を選択してください。", "note": "メモ", "noInventoryLevel": "在庫レベルなし", "noInventoryLevelDesc": "選択した拠点には選択したアイテムの在庫レベルがありません。返品をリクエストできますが、選択した拠点の在庫レベルが作成されるまで受け取ることはできません。", "noteHint": "何か指定したい場合は自由に入力できます。", "location": "拠点", "locationHint": "アイテムを返品する拠点を選択してください。", "inboundShipping": "返品配送", "inboundShippingHint": "使用する方法を選択してください。", "returnableQuantityLabel": "返品可能数量", "refundableAmountLabel": "返金可能金額", "returnRequestedInfo": "{{requestedItemsCount}}個のアイテムの返品がリクエストされました", "returnReceivedInfo": "{{requestedItemsCount}}個のアイテムの返品を受け取りました", "itemReceived": "アイテムを受け取りました", "returnRequested": "返品がリクエストされました", "damagedItemReceived": "破損したアイテムを受け取りました", "damagedItemsReturned": "{{quantity}}個の破損したアイテムが返品されました", "activeChangeError": "この注文には進行中のアクティブな注文変更があります。まず変更を完了または破棄してください。", "cancel": {"title": "返品をキャンセル", "description": "返品リクエストをキャンセルしてもよろしいですか？"}, "placeholders": {"noReturnShippingOptions": {"title": "返品配送オプションが見つかりません", "hint": "この拠点の返品配送オプションが作成されていません。<LinkComponent>拠点と配送</LinkComponent>で作成できます。"}, "outboundShippingOptions": {"title": "出庫配送オプションが見つかりません", "hint": "この拠点の出庫配送オプションが作成されていません。<LinkComponent>拠点と配送</LinkComponent>で作成できます。"}}, "receive": {"action": "アイテムを受け取る", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "すべてのアイテムを再入庫", "itemsLabel": "受け取ったアイテム", "title": "#{{returnId}}のアイテムを受け取る", "sendNotificationHint": "顧客に返品の受け取りについて通知します。", "inventoryWarning": "上記の入力に基づいて自動的に在庫レベルを調整することに注意してください。", "writeOffInputLabel": "アイテムのうち何個が損傷していますか？", "toast": {"success": "返品が正常に受け取られました。", "errorLargeValue": "数量がリクエストされたアイテム数量を超えています。", "errorNegativeValue": "数量は負の値にはできません。", "errorLargeDamagedValue": "損傷したアイテムの数量 + 損傷していない受け取ったアイテムの数量が返品の総アイテム数量を超えています。損傷していないアイテムの数量を減らしてください。"}}, "toast": {"canceledSuccessfully": "返品が正常にキャンセルされました", "confirmedSuccessfully": "返品が正常に確認されました"}, "panel": {"title": "返品が開始されました", "description": "完了する必要のある未処理の返品リクエストがあります"}}, "claims": {"create": "クレームを作成", "confirm": "クレームを確認", "confirmText": "クレームを確認しようとしています。この操作は元に戻せません。", "manage": "クレームを管理", "outbound": "出庫", "outboundItemAdded": "クレームを通じて{{itemsCount}}個追加されました", "outboundTotal": "出庫合計", "outboundShipping": "出庫配送", "outboundShippingHint": "使用する方法を選択してください。", "refundAmount": "見積もり差額", "activeChangeError": "この注文にはアクティブな注文変更があります。前の変更を完了または破棄してください。", "actions": {"cancelClaim": {"successToast": "クレームが正常にキャンセルされました。"}}, "cancel": {"title": "クレームをキャンセル", "description": "クレームをキャンセルしてもよろしいですか？"}, "tooltips": {"onlyReturnShippingOptions": "このリストには返品配送オプションのみが含まれます。"}, "toast": {"canceledSuccessfully": "クレームが正常にキャンセルされました", "confirmedSuccessfully": "クレームが正常に確認されました"}, "panel": {"title": "クレームが開始されました", "description": "完了する必要のある未処理のクレームリクエストがあります"}}, "exchanges": {"create": "交換を作成", "manage": "交換を管理", "confirm": "交換を確認", "confirmText": "交換を確認しようとしています。この操作は元に戻せません。", "outbound": "出庫", "outboundItemAdded": "交換を通じて{{itemsCount}}個追加されました", "outboundTotal": "出庫合計", "outboundShipping": "出庫配送", "outboundShippingHint": "使用する方法を選択してください。", "refundAmount": "見積もり差額", "activeChangeError": "この注文にはアクティブな注文変更があります。前の変更を完了または破棄してください。", "actions": {"cancelExchange": {"successToast": "交換が正常にキャンセルされました。"}}, "cancel": {"title": "交換をキャンセル", "description": "交換をキャンセルしてもよろしいですか？"}, "tooltips": {"onlyReturnShippingOptions": "このリストには返品配送オプションのみが含まれます。"}, "toast": {"canceledSuccessfully": "交換が正常にキャンセルされました", "confirmedSuccessfully": "交換が正常に確認されました"}, "panel": {"title": "交換が開始されました", "description": "完了する必要のある未処理の交換リクエストがあります"}}, "reservations": {"allocatedLabel": "割り当て済み", "notAllocatedLabel": "未割り当て"}, "allocateItems": {"action": "アイテムを割り当てる", "title": "注文アイテムを割り当てる", "locationDescription": "割り当てる拠点を選択してください。", "itemsToAllocate": "割り当てるアイテム", "itemsToAllocateDesc": "割り当てたいアイテムの数を選択してください", "search": "アイテムを検索", "consistsOf": "{{num}}個の在庫アイテムで構成", "requires": "バリエーションごとに{{num}}個必要", "toast": {"created": "アイテムが正常に割り当てられました"}, "error": {"quantityNotAllocated": "割り当てられていないアイテムがあります。"}}, "shipment": {"title": "出荷済みとしてマーク", "trackingNumber": "追跡番号", "addTracking": "追跡番号を追加", "sendNotification": "通知を送信", "sendNotificationHint": "この出荷について顧客に通知します。", "toastCreated": "出荷が正常に作成されました。"}, "fulfillment": {"cancelWarning": "出荷をキャンセルしようとしています。この操作は元に戻せません。", "markAsDeliveredWarning": "出荷を配達済みとしてマークしようとしています。この操作は元に戻せません。", "unfulfilledItems": "未出荷アイテム", "statusLabel": "出荷ステータス", "statusTitle": "出荷ステータス", "fulfillItems": "アイテムを出荷", "awaitingFulfillmentBadge": "出荷待ち", "requiresShipping": "配送が必要", "number": "出荷 #{{number}}", "itemsToFulfill": "出荷するアイテム", "create": "出荷を作成", "available": "利用可能", "inStock": "在庫あり", "markAsShipped": "配送済みとしてマーク", "markAsDelivered": "配達済みとしてマーク", "itemsToFulfillDesc": "出荷するアイテムと数量を選択してください", "locationDescription": "アイテムを出荷する拠点を選択してください。", "sendNotificationHint": "作成された出荷について顧客に通知します。", "methodDescription": "顧客が選択した配送方法とは異なる方法を選択してください", "error": {"wrongQuantity": "出荷可能なアイテムは1つのみです", "wrongQuantity_other": "数量は1から{{number}}の間の数字である必要があります", "noItems": "出荷するアイテムがありません。"}, "status": {"notFulfilled": "未出荷", "partiallyFulfilled": "一部出荷", "fulfilled": "出荷済み", "partiallyShipped": "一部配送", "shipped": "配送済み", "delivered": "配達済み", "partiallyDelivered": "一部配達", "partiallyReturned": "一部返品", "returned": "返品済み", "canceled": "キャンセル済み", "requiresAction": "対応が必要"}, "toast": {"created": "出荷が正常に作成されました", "canceled": "出荷が正常にキャンセルされました", "fulfillmentShipped": "すでに配送された出荷をキャンセルすることはできません", "fulfillmentDelivered": "出荷が正常に配達済みとしてマークされました"}, "trackingLabel": "追跡", "shippingFromLabel": "発送元", "itemsLabel": "アイテム"}, "refund": {"title": "返金を作成", "sendNotificationHint": "作成された返金について顧客に通知します。", "systemPayment": "システム支払い", "systemPaymentDesc": "1つ以上の支払いがシステム支払いです。このような支払いの取り込みと返金はMedusaでは処理されないことに注意してください。", "error": {"amountToLarge": "元の注文金額を超えて返金することはできません。", "amountNegative": "返金額は正の数でなければなりません。", "reasonRequired": "返金理由を選択してください。"}}, "customer": {"contactLabel": "連絡先", "editEmail": "Eメールを編集", "transferOwnership": "所有権を移転", "editBillingAddress": "請求先住所を編集", "editShippingAddress": "配送先住所を編集"}, "activity": {"header": "アクティビティ", "showMoreActivities_one": "さらに{{count}}件のアクティビティを表示", "showMoreActivities_other": "さらに{{count}}件のアクティビティを表示", "comment": {"label": "コメント", "placeholder": "コメントを残す", "addButtonText": "コメントを追加", "deleteButtonText": "コメントを削除"}, "from": "から", "to": "まで", "events": {"common": {"toReturn": "返品する", "toSend": "送信する"}, "placed": {"title": "注文が完了しました", "fromSalesChannel": "{{salesChannel}}から"}, "canceled": {"title": "注文がキャンセルされました"}, "payment": {"awaiting": "支払い待ち", "captured": "支払いが確定されました", "canceled": "支払いがキャンセルされました", "refunded": "支払いが返金されました"}, "fulfillment": {"created": "アイテムが出荷されました", "canceled": "出荷がキャンセルされました", "shipped": "アイテムが配送されました", "delivered": "アイテムが配達されました", "items_one": "{{count}}個のアイテム", "items_other": "{{count}}個のアイテム"}, "return": {"created": "返品#{{returnId}}がリクエストされました", "canceled": "返品#{{returnId}}がキャンセルされました", "received": "返品#{{returnId}}を受け取りました", "items_one": "{{count}}個のアイテムが返品されました", "items_other": "{{count}}個のアイテムが返品されました"}, "note": {"comment": "コメント", "byLine": "{{author}}による"}, "claim": {"created": "クレーム#{{claimId}}がリクエストされました", "canceled": "クレーム#{{claimId}}がキャンセルされました", "itemsInbound": "{{count}}個のアイテムを返品", "itemsOutbound": "{{count}}個のアイテムを送信"}, "exchange": {"created": "交換#{{exchangeId}}がリクエストされました", "canceled": "交換#{{exchangeId}}がキャンセルされました", "itemsInbound": "{{count}}個のアイテムを返品", "itemsOutbound": "{{count}}個のアイテムを送信"}, "edit": {"requested": "注文編集#{{editId}}がリクエストされました", "confirmed": "注文編集#{{editId}}が確認されました"}, "transfer": {"requested": "注文移転#{{transferId}}がリクエストされました", "confirmed": "注文移転#{{transferId}}が確認されました", "declined": "注文移転#{{transferId}}が拒否されました"}, "update_order": {"shipping_address": "配送先住所が更新されました", "billing_address": "請求先住所が更新されました", "email": "Eメールが更新されました"}}}, "fields": {"displayId": "表示ID", "refundableAmount": "返金可能金額", "returnableQuantity": "返品可能数量"}}, "draftOrders": {"domain": "下書き注文", "deleteWarning": "下書き注文{{id}}を削除しようとしています。この操作は取り消せません。", "paymentLinkLabel": "支払いリンク", "cartIdLabel": "カートID", "markAsPaid": {"label": "支払い済みとしてマーク", "warningTitle": "支払い済みとしてマーク", "warningDescription": "下書き注文を支払い済みとしてマークしようとしています。この操作は取り消せず、後で支払いを受け取ることはできなくなります。"}, "status": {"open": "未完了", "completed": "完了済み"}, "create": {"createDraftOrder": "下書き注文を作成", "createDraftOrderHint": "注文が確定する前に詳細を管理するための新しい下書き注文を作成します。", "chooseRegionHint": "地域を選択", "existingItemsLabel": "既存の商品", "existingItemsHint": "下書き注文に既存の商品を追加します。", "customItemsLabel": "カスタム商品", "customItemsHint": "下書き注文にカスタム商品を追加します。", "addExistingItemsAction": "既存の商品を追加", "addCustomItemAction": "カスタム商品を追加", "noCustomItemsAddedLabel": "まだカスタム商品が追加されていません", "noExistingItemsAddedLabel": "まだ既存の商品が追加されていません", "chooseRegionTooltip": "まず地域を選択してください", "useExistingCustomerLabel": "既存の顧客を使用", "addShippingMethodsAction": "配送方法を追加", "unitPriceOverrideLabel": "単価の上書き", "shippingOptionLabel": "配送オプション", "shippingOptionHint": "下書き注文の配送オプションを選択してください。", "shippingPriceOverrideLabel": "配送料金の上書き", "shippingPriceOverrideHint": "下書き注文の配送料金を上書きします。", "sendNotificationLabel": "通知を送信", "sendNotificationHint": "下書き注文が作成されたときに顧客に通知を送信します。"}, "validation": {"requiredEmailOrCustomer": "Eメールまたは顧客情報が必要です。", "requiredItems": "少なくとも1つの商品が必要です。", "invalidEmail": "有効なEメールを入力してください。"}}, "stockLocations": {"domain": "在庫拠点と配送", "list": {"description": "店舗の在庫拠点と配送オプションを管理します。"}, "create": {"header": "在庫拠点を作成", "hint": "在庫拠点は、商品が保管され出荷される物理的な拠点です。", "successToast": "拠点「{{name}}」が正常に作成されました。"}, "edit": {"header": "在庫拠点を編集", "viewInventory": "在庫を表示", "successToast": "拠点「{{name}}」が正常に更新されました。"}, "delete": {"confirmation": "在庫拠点「{{name}}」を削除しようとしています。この操作は取り消せません。"}, "fulfillmentProviders": {"header": "フルフィルメントプロバイダー", "shippingOptionsTooltip": "このドロップダウンには、この拠点で有効化されたプロバイダーのみが含まれます。ドロップダウンが無効の場合は、拠点にプロバイダーを追加してください。", "label": "接続済みフルフィルメントプロバイダー", "connectedTo": "{{total}}個中{{count}}個のフルフィルメントプロバイダーに接続済み", "noProviders": "この在庫拠点はフルフィルメントプロバイダーに接続されていません。", "action": "プロバイダーを接続", "successToast": "在庫拠点のフルフィルメントプロバイダーが正常に更新されました。"}, "fulfillmentSets": {"pickup": {"header": "ピックアップ"}, "shipping": {"header": "配送"}, "disable": {"confirmation": "「{{name}}」を無効にしてもよろしいですか？関連するすべてのサービスゾーンと配送オプションが削除され、この操作は取り消せません。", "pickup": "プックアップが正常に無効化されました。", "shipping": "配送が正常に無効化されました。"}, "enable": {"pickup": "ピックアップが正常に有効化されました。", "shipping": "配送が正常に有効化されました。"}}, "sidebar": {"header": "配送設定", "shippingProfiles": {"label": "配送プロファイル", "description": "配送要件ごとに商品をグループ化"}}, "salesChannels": {"header": "販売チャネル", "label": "接続済み販売チャネル", "connectedTo": "{{total}}個中{{count}}個の販売チャネルに接続済み", "noChannels": "この拠点は販売チャネルに接続されていません。", "action": "販売チャネルを接続", "successToast": "販売チャネルが正常に更新されました。"}, "shippingOptions": {"create": {"shipping": {"header": "「{{zone}}」の配送オプションを作成", "hint": "この拠点からの商品の配送方法を定義する新しい配送オプションを作成します。", "label": "配送オプション", "successToast": "配送オプション「{{name}}」が正常に作成されました。"}, "returns": {"header": "「{{zone}}」の返品オプションを作成", "hint": "この拠点への商品の返品方法を定義する新しい返品オプションを作成します。", "label": "返品オプション", "successToast": "返品オプション「{{name}}」が正常に作成されました。"}, "tabs": {"details": "詳細", "prices": "価格"}, "action": "オプションを作成"}, "delete": {"confirmation": "配送オプション「{{name}}」を削除しようとしています。この操作は取り消せません。", "successToast": "配送オプション「{{name}}」が正常に削除されました。"}, "edit": {"header": "配送オプションを編集", "action": "オプションを編集", "successToast": "配送オプション「{{name}}」が正常に更新されました。"}, "pricing": {"action": "価格を編集"}, "fields": {"count": {"shipping_one": "{{count}}個の配送オプション", "shipping_other": "{{count}}個の配送オプション", "returns_one": "{{count}}個の返品オプション", "returns_other": "{{count}}個の返品オプション"}, "priceType": {"label": "価格タイプ", "options": {"fixed": {"label": "固定", "hint": "配送オプションの価格は固定で、注文内容に基づいて変更されません。"}, "calculated": {"label": "計算式", "hint": "配送オプションの価格は、チェックアウト時にフルフィルメントプロバイダーによって計算されます。"}}}, "enableInStore": {"label": "ストアで有効化", "hint": "顧客がチェックアウト時にこのオプションを使用できるかどうか。"}, "provider": "フルフィルメントプロバイダー", "profile": "配送プロファイル"}}, "serviceZones": {"create": {"headerPickup": "{{location}}からピックアップのサービスゾーンを作成", "headerShipping": "{{location}}から配送のサービスゾーンを作成", "action": "サービスゾーンを作成", "successToast": "サービスゾーン「{{name}}」が正常に作成されました。"}, "edit": {"header": "サービスゾーンを編集", "successToast": "サービスゾーン「{{name}}」が正常に更新されました。"}, "delete": {"confirmation": "サービスゾーン「{{name}}」を削除しようとしています。この操作は取り消せません。", "successToast": "サービスゾーン「{{name}}」が正常に削除されました。"}, "manageAreas": {"header": "{{name}}のエリアを管理", "action": "エリアを管理", "label": "エリア", "hint": "サービスゾーンがカバーする地理的エリアを選択してください。", "successToast": "「{{name}}」のエリアが正常に更新されました。"}, "fields": {"noRecords": "配送オプションを追加するサービスゾーンがありません。", "tip": "サービスゾーンは地理的ゾーンまたはエリアの集合です。定義された一連の拠点に利用可能な配送オプションを制限するために使用されます。"}}}, "shippingProfile": {"domain": "配送プロファイル", "subtitle": "類似の配送要件を持つ商品をプロファイルにグループ化します。", "create": {"header": "配送プロファイルを作成", "hint": "類似の配送要件を持つ商品をグループ化するための新しい配送プロファイルを作成します。", "successToast": "配送プロファイル「{{name}}」が正常に作成されました。"}, "delete": {"title": "配送プロファイルを削除", "description": "配送プロファイル「{{name}}」を削除しようとしています。この操作は取り消せません。", "successToast": "配送プロファイル「{{name}}」が正常に削除されました。"}, "tooltip": {"type": "配送プロファイルのタイプを入力してください。例：重い、特大、貨物専用など。"}}, "taxRegions": {"domain": "税地域", "list": {"hint": "顧客が異なる国や地域から購入する際の課金方法を管理します。"}, "delete": {"confirmation": "税地域を削除しようとしています。この操作は取り消せません。", "successToast": "税地域が正常に削除されました。"}, "create": {"header": "税地域を作成", "hint": "特定の国の税率を定義するための新しい税地域を作成します。", "errors": {"rateIsRequired": "デフォルトの税率を作成する際は税率が必要です。", "nameIsRequired": "デフォルトの税率を作成する際は名前が必要です。"}, "successToast": "税地域が正常に作成されました。"}, "province": {"header": "省", "create": {"header": "省の税地域を作成", "hint": "特定の省の税率を定義するための新しい税地域を作成します。"}}, "state": {"header": "州", "create": {"header": "州の税地域を作成", "hint": "特定の州の税率を定義するための新しい税地域を作成します。"}}, "stateOrTerritory": {"header": "州または準州", "create": {"header": "州/準州の税地域を作成", "hint": "特定の州/準州の税率を定義するための新しい税地域を作成します。"}}, "county": {"header": "郡", "create": {"header": "郡の税地域を作成", "hint": "特定の郡の税率を定義するための新しい税地域を作成します。"}}, "region": {"header": "地域", "create": {"header": "地域の税地域を作成", "hint": "特定の地域の税率を定義するための新しい税地域を作成します。"}}, "department": {"header": "部", "create": {"header": "部の税地域を作成", "hint": "特定の部の税率を定義するための新しい税地域を作成します。"}}, "territory": {"header": "準州", "create": {"header": "準州の税地域を作成", "hint": "特定の準州の税率を定義するための新しい税地域を作成します。"}}, "prefecture": {"header": "都道府県", "create": {"header": "都道府県の税地域を作成", "hint": "特定の都道府県の税率を定義するための新しい税地域を作成します。"}}, "district": {"header": "地区", "create": {"header": "地区の税地域を作成", "hint": "特定の地区の税率を定義するための新しい税地域を作成します。"}}, "governorate": {"header": "行政区", "create": {"header": "行政区の税地域を作成", "hint": "特定の行政区の税率を定義するための新しい税地域を作成します。"}}, "canton": {"header": "カントン", "create": {"header": "カントンの税地域を作成", "hint": "特定のカントンの税率を定義するための新しい税地域を作成します。"}}, "emirate": {"header": "首長国", "create": {"header": "首長国の税地域を作成", "hint": "特定の首長国の税率を定義するための新しい税地域を作成します。"}}, "sublevel": {"header": "サブレベル", "create": {"header": "サブレベルの税地域を作成", "hint": "特定のサブレベルの税率を定義するための新しい税地域を作成します。"}}, "taxOverrides": {"header": "上書き", "create": {"header": "上書きを作成", "hint": "選択した条件に対してデフォルトの税率を上書きする税率を作成します。"}, "edit": {"header": "上書きを編集", "hint": "選択した条件に対してデフォルトの税率を上書きする税率を編集します。"}}, "taxRates": {"create": {"header": "税率を作成", "hint": "地域の税率を定義するための新しい税率を作成します。", "successToast": "税率が正常に作成されました。"}, "edit": {"header": "税率を編集", "hint": "地域の税率を定義するための税率を編集します。", "successToast": "税率が正常に更新されました。"}, "delete": {"confirmation": "税率「{{name}}」を削除しようとしています。この操作は取り消せません。", "successToast": "税率が正常に削除されました。"}}, "fields": {"isCombinable": {"label": "組み合わせ可能", "hint": "この税率を税地域のデフォルト税率と組み合わせることができるかどうか。", "true": "組み合わせ可能", "false": "組み合わせ不可"}, "defaultTaxRate": {"label": "デフォルト税率", "tooltip": "この地域のデフォルト税率。例えば、国や地域の標準VAT税率など。", "action": "デフォルト税率を作成"}, "taxRate": "税率", "taxCode": "税コード", "targets": {"label": "対象", "hint": "この税率が適用される対象を選択してください。", "options": {"product": "商品", "productCollection": "商品コレクション", "productTag": "商品タグ", "productType": "商品タイプ", "customerGroup": "顧客グループ"}, "operators": {"in": "に含まれる", "on": "に対して", "and": "および"}, "placeholders": {"product": "商品を検索", "productCollection": "商品コレクションを検索", "productTag": "商品タグを検索", "productType": "商品タイプを検索", "customerGroup": "顧客グループを検索"}, "tags": {"product": "商品", "productCollection": "商品コレクション", "productTag": "商品タグ", "productType": "商品タイプ", "customerGroup": "顧客グループ"}, "modal": {"header": "対象を追加"}, "values_one": "{{count}}個の値", "values_other": "{{count}}個の値", "numberOfTargets_one": "{{count}}個の対象", "numberOfTargets_other": "{{count}}個の対象", "additionalValues_one": "さらに{{count}}個の値", "additionalValues_other": "さらに{{count}}個の値", "action": "対象を追加"}, "sublevels": {"labels": {"province": "省", "state": "州", "region": "地域", "stateOrTerritory": "州/準州", "department": "部", "county": "郡", "territory": "準州", "prefecture": "都道府県", "district": "地区", "governorate": "行政区", "emirate": "首長国", "canton": "カントン", "sublevel": "サブレベルコード"}, "placeholders": {"province": "省を選択", "state": "州を選択", "region": "地域を選択", "stateOrTerritory": "州/準州を選択", "department": "行政区を選択", "county": "郡を選択", "territory": "準州を選択", "prefecture": "都道府県を選択", "district": "地区を選択", "governorate": "行政区を選択", "emirate": "首長国を選択", "canton": "カントンを選択"}, "tooltips": {"sublevel": "サブレベルの税地域のISO 3166-2コードを入力してください。", "notPartOfCountry": "{{province}}は{{country}}の一部ではないようです。これが正しいかどうか確認してください。"}, "alert": {"header": "この税地域ではサブレベル地域が無効になっています", "description": "この地域ではデフォルトでサブレベル地域が無効になっています。有効にすると、州、県、準州などのサブレベル地域を作成できます。", "action": "サブレベル地域を有効にする"}}, "noDefaultRate": {"label": "デフォルト税率なし", "tooltip": "この税地域にはデフォルトの税率がありません。国のVATなど標準税率がある場合は、この地域に追加してください。"}}}, "promotions": {"domain": "プロモーション", "sections": {"details": "プロモーションの詳細"}, "tabs": {"template": "タイプ", "details": "詳細", "campaign": "キャンペーン"}, "fields": {"type": "タイプ", "value_type": "値のタイプ", "value": "値", "campaign": "キャンペーン", "method": "方法", "allocation": "配分", "addCondition": "条件を追加", "clearAll": "すべてクリア", "amount": {"tooltip": "金額を設定するには通貨コードを選択してください"}, "conditions": {"rules": {"title": "誰がこのコードを使用できますか？", "description": "どの顧客がプロモーションコードを使用できるか？何も変更しなければ、すべての顧客が使用できます。"}, "target-rules": {"title": "プロモーションはどの商品に適用されますか？", "description": "プロモーションは、次の条件に一致する商品に適用されます。"}, "buy-rules": {"title": "プロモーションを有効にするためにカートに必要なものは？", "description": "これらの条件が一致した場合、ターゲット商品に対してプロモーションが有効になります。"}}}, "tooltips": {"campaignType": "支出予算を設定するには、プロモーションで通貨コードを選択する必要があります。"}, "errors": {"requiredField": "必須項目です", "promotionTabError": "進む前にプロモーションタブのエラーを修正してください"}, "toasts": {"promotionCreateSuccess": "プロモーション「{{code}}」が正常に作成されました。"}, "create": {}, "edit": {"title": "プロモーションの詳細を編集", "rules": {"title": "使用条件を編集"}, "target-rules": {"title": "商品条件を編集"}, "buy-rules": {"title": "購入条件を編集"}}, "campaign": {"header": "キャンペーン", "edit": {"header": "キャンペーンを編集", "successToast": "プロモーションのキャンペーンが正常に更新されました。"}, "actions": {"goToCampaign": "キャンペーンに移動"}}, "campaign_currency": {"tooltip": "これはプロモーションの通貨です。詳細タブから変更してください。"}, "form": {"required": "必須", "and": "と", "selectAttribute": "属性を選択", "campaign": {"existing": {"title": "既存のキャンペーン", "description": "既存のキャンペーンにプロモーションを追加します。", "placeholder": {"title": "既存のキャンペーンはありません", "desc": "複数のプロモーションを追跡し、予算制限を設定するために作成できます。"}}, "new": {"title": "新しいキャンペーン", "description": "このプロモーションのために新しいキャンペーンを作成します。"}, "none": {"title": "キャンペーンなし", "description": "プロモーションをキャンペーンに関連付けずに進めます。"}}, "status": {"title": "ステータス"}, "method": {"label": "方法", "code": {"title": "プロモーションコード", "description": "顧客はチェックアウト時にこのコードを入力する必要があります。"}, "automatic": {"title": "自動", "description": "顧客はチェックアウト時にこのプロモーションを見ることができます。"}}, "max_quantity": {"title": "最大数量", "description": "このプロモーションが適用される商品の最大数量。"}, "type": {"standard": {"title": "標準", "description": "標準的なプロモーション"}, "buyget": {"title": "購入して取得", "description": "Xを購入してYを取得するプロモーション"}}, "allocation": {"each": {"title": "各々", "description": "各アイテムに値を適用します。"}, "across": {"title": "全体", "description": "アイテム全体に値を適用します。"}}, "code": {"title": "コード", "description": "顧客がチェックアウト時に入力するコードです。"}, "value": {"title": "プロモーション値"}, "value_type": {"fixed": {"title": "プロモーション値", "description": "割引される金額。例：100"}, "percentage": {"title": "プロモーション値", "description": "金額から割引されるパーセンテージ。例：8%"}}}, "deleteWarning": "プロモーション「{{code}}」を削除しようとしています。この操作は取り消せません。", "createPromotionTitle": "プロモーションを作成", "type": "プロモーションタイプ", "conditions": {"add": "条件を追加", "list": {"noRecordsMessage": "プロモーションが適用されるアイテムを制限する条件を追加してください。"}}}, "campaigns": {"domain": "キャンペーン", "details": "キャンペーン詳細", "status": {"active": "アクティブ", "expired": "期限切れ", "scheduled": "スケジュール済み"}, "delete": {"title": "本当ですか？", "description": "キャンペーン「{{name}}」を削除しようとしています。この操作は取り消せません。", "successToast": "キャンペーン「{{name}}」が正常に作成されました。"}, "edit": {"header": "キャンペーンを編集", "description": "キャンペーンの詳細を編集します。", "successToast": "キャンペーン「{{name}}」が正常に更新されました。"}, "configuration": {"header": "設定", "edit": {"header": "キャンペーン設定を編集", "description": "キャンペーンの設定を編集します。", "successToast": "キャンペーン設定が正常に更新されました。"}}, "create": {"title": "キャンペーンを作成", "description": "プロモーションキャンペーンを作成します。", "hint": "プロモーションキャンペーンを作成します。", "header": "キャンペーンを作成", "successToast": "キャンペーン「{{name}}」が正常に作成されました。"}, "fields": {"name": "名前", "identifier": "識別子", "start_date": "開始日", "end_date": "終了日", "total_spend": "予算支出", "total_used": "使用済み予算", "budget_limit": "予算制限", "campaign_id": {"hint": "プロモーションと同じ通貨コードのキャンペーンのみがこのリストに表示されます。"}}, "budget": {"create": {"hint": "キャンペーンの予算を作成します。", "header": "キャンペーン予算"}, "details": "キャンペーン予算", "fields": {"type": "タイプ", "currency": "通貨", "limit": "制限", "used": "使用済み"}, "type": {"spend": {"title": "支出", "description": "すべてのプロモーション使用の合計割引額に制限を設定します。"}, "usage": {"title": "使用", "description": "プロモーションが使用できる回数に制限を設定します。"}}, "edit": {"header": "キャンペーン予算を編集"}}, "promotions": {"remove": {"title": "キャンペーンからプロモーションを削除", "description": "キャンペーンから{{count}}個のプロモーションを削除しようとしています。この操作は取り消せません。"}, "alreadyAdded": "このプロモーションはすでにキャンペーンに追加されています。", "alreadyAddedDiffCampaign": "このプロモーションは別のキャンペーン（{{name}}）にすでに追加されています。", "currencyMismatch": "プロモーションとキャンペーンの通貨が一致しません。", "toast": {"success": "{{count}}個のプロモーションが正常にキャンペーンに追加されました。"}, "add": {"list": {"noRecordsMessage": "最初にプロモーションを作成してください。"}}, "list": {"noRecordsMessage": "このキャンペーンにはプロモーションがありません。"}}, "deleteCampaignWarning": "キャンペーン「{{name}}」を削除しようとしています。この操作は取り消せません。", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "価格リスト", "subtitle": "特定の条件に対して販売価格を作成または上書きします。", "delete": {"confirmation": "価格リスト「{{title}}」を削除しようとしています。この操作は取り消せません。", "successToast": "価格リスト「{{title}}」が正常に削除されました。"}, "create": {"header": "価格リストを作成", "subheader": "商品の価格を管理するための新しい価格リストを作成します。", "tabs": {"details": "詳細", "products": "商品", "prices": "価格"}, "successToast": "価格リスト「{{title}}」が正常に作成されました。", "products": {"list": {"noRecordsMessage": "最初に商品を作成してください。"}}}, "edit": {"header": "価格リストを編集", "successToast": "価格リスト「{{title}}」が正常に更新されました。"}, "configuration": {"header": "設定", "edit": {"header": "価格リストの設定を編集", "description": "価格リストの設定を編集します。", "successToast": "価格リストの設定が正常に更新されました。"}}, "products": {"header": "商品", "actions": {"addProducts": "商品を追加", "editPrices": "価格を編集"}, "delete": {"confirmation_one": "{{count}}個の商品に対する価格をこの価格リストから削除しようとしています。この操作は取り消せません。", "confirmation_other": "{{count}}個の商品に対する価格をこの価格リストから削除しようとしています。この操作は取り消せません。", "successToast_one": "{{count}}個の商品に対する価格が正常に削除されました。", "successToast_other": "{{count}}個の商品に対する価格が正常に削除されました。"}, "add": {"successToast": "価格が正常に価格リストに追加されました。"}, "edit": {"successToast": "価格が正常に更新されました。"}}, "fields": {"priceOverrides": {"label": "価格の上書き", "header": "価格の上書き"}, "status": {"label": "ステータス", "options": {"active": "アクティブ", "draft": "ドラフト", "expired": "期限切れ", "scheduled": "スケジュール済み"}}, "type": {"label": "タイプ", "hint": "作成したい価格リストのタイプを選択します。", "options": {"sale": {"label": "セール", "description": "セール価格は商品の一時的な価格変更です。"}, "override": {"label": "上書き", "description": "上書きは通常、顧客特有の価格を作成するために使用されます。"}}}, "startsAt": {"label": "価格リストには開始日がありますか？", "hint": "将来有効になるように、価格リストをスケジュールします。"}, "endsAt": {"label": "価格リストには終了日がありますか？", "hint": "将来無効になるように、価格リストをスケジュールします。"}, "customerAvailability": {"header": "顧客グループを選択", "label": "顧客の可用性", "hint": "この価格リストが適用される顧客グループを選択します。", "placeholder": "顧客グループを検索", "attribute": "顧客グループ"}}}, "profile": {"domain": "プロフィール", "manageYourProfileDetails": "プロフィールの詳細を管理します。", "fields": {"languageLabel": "言語", "usageInsightsLabel": "使用状況のインサイト"}, "edit": {"header": "プロフィールを編集", "languageHint": "管理ダッシュボードで使用する言語です。これにより、ストアの言語は変更されません。", "languagePlaceholder": "言語を選択", "usageInsightsHint": "使用状況のインサイトを共有し、Medusaの改善にご協力ください。収集内容やその使用方法については、<0>ドキュメント</0>をご覧ください。"}, "toast": {"edit": "プロフィールの変更が保存されました"}}, "users": {"domain": "ユーザー", "editUser": "ユーザーを編集", "inviteUser": "ユーザーを招待", "inviteUserHint": "新しいユーザーをストアに招待します。", "sendInvite": "招待を送信", "pendingInvites": "保留中の招待", "deleteInviteWarning": "{{email}}への招待を削除しようとしています 。この操作は取り消せません。", "resendInvite": "招待を再送信", "copyInviteLink": "招待リンクをコピー", "expiredOnDate": "{{date}}に期限切れ", "validFromUntil": "<0>{{from}}</0> - <1>{{until}}</1> の間有効", "acceptedOnDate": "{{date}}に承認されました", "inviteStatus": {"accepted": "承認済み", "pending": "保留中", "expired": "期限切れ"}, "roles": {"admin": "管理者", "developer": "開発者", "member": "メンバー"}, "deleteUserWarning": "{{name}}を削除しようとしています。この操作は取り消せません。", "invite": "招待"}, "store": {"domain": "ストア", "manageYourStoresDetails": "ストアの詳細を管理します。", "editStore": "ストアを編集", "defaultCurrency": "デフォルト通貨", "defaultRegion": "デフォルト地域", "swapLinkTemplate": "リンクテンプレートを交換", "paymentLinkTemplate": "支払いリンクテンプレート", "inviteLinkTemplate": "招待リンクテンプレート", "currencies": "通貨", "addCurrencies": "通貨を追加", "enableTaxInclusivePricing": "税込価格を有効にする", "disableTaxInclusivePricing": "税込価格を無効にする", "removeCurrencyWarning_one": "{{count}}個の通貨をストアから削除しようとしています。進む前に、その通貨を使用しているすべての価格が削除されていることを確認してください。", "removeCurrencyWarning_other": "{{count}}個の通貨をストアから削除しようとしています。進む前に、それらの通貨を使用しているすべての価格が削除されていることを確認してください。", "currencyAlreadyAdded": "この通貨はすでにストアに追加されています。", "edit": {"header": "ストアを編集"}, "toast": {"update": "ストアが正常に更新されました。", "currenciesUpdated": "通貨が正常に更新されました。", "currenciesRemoved": "ストアから通貨が正常に削除されました。", "updatedTaxInclusivitySuccessfully": "税込価格が正常に更新されました。"}}, "regions": {"domain": "地域", "subtitle": "地域は、商品を販売するエリアです。複数の国をカバーでき、異なる税率、プロバイダー、通貨を持ちます。", "createRegion": "地域を作成", "createRegionHint": "一連の国の税率とプロバイダーを管理します。", "addCountries": "国を追加", "editRegion": "地域を編集", "countriesHint": "この地域に含まれる国を追加します。", "deleteRegionWarning": "地域「{{name}}」を削除しようとしています。この操作は取り消せません。", "removeCountriesWarning_one": "{{count}}か国を地域から削除しようとしています。この操作は取り消せません。", "removeCountriesWarning_other": "{{count}}か国を地域から削除しようとしています。この操作は取り消せません。", "removeCountryWarning": "国「{{name}}」を地域から削除しようとしています。この操作は取り消せません。", "automaticTaxesHint": "有効にすると、税金は配送先住所に基づいてチェックアウト時にのみ計算されます。", "taxInclusiveHint": "有効にすると、この地域の価格は税込みになります。", "providersHint": "この地域で利用可能な決済プロバイダーを追加します。", "shippingOptions": "配送オプション", "deleteShippingOptionWarning": "配送オプション「{{name}}」を削除しようとしています。この操作は取り消せません。", "return": "返品", "outbound": "発送", "priceType": "価格タイプ", "flatRate": "定額", "calculated": "計算式", "list": {"noRecordsMessage": "販売地域の地域を作成してください。"}, "toast": {"delete": "地域が正常に削除されました", "edit": "地域の編集が保存されました", "create": "地域が正常に作成されました", "countries": "地域の国が正常に更新されました"}, "shippingOption": {"createShippingOption": "配送オプションを作成", "createShippingOptionHint": "地域の新しい配送オプションを作成します。", "editShippingOption": "配送オプションを編集", "fulfillmentMethod": "出荷方法", "type": {"outbound": "発送", "outboundHint": "顧客に商品を送る配送オプションを作成する場合に使用します。", "return": "返品", "returnHint": "顧客が商品を返品するための配送オプションを作成する場合に使用します。"}, "priceType": {"label": "価格タイプ", "flatRate": "定額", "calculated": "計算式"}, "availability": {"adminOnly": "管理者のみ", "adminOnlyHint": "有効にすると、配送オプションは管理ダッシュボードでのみ利用可能になり、ストアフロントでは利用できません。"}, "taxInclusiveHint": "有効にすると、配送オプションの価格は税込みになります。", "requirements": {"label": "要件", "hint": "配送オプションの要件を指定します。"}}}, "taxes": {"domain": "税地域", "domainDescription": "税地域を管理する", "countries": {"taxCountriesHint": "税設定は表示されている国に適用されます。"}, "settings": {"editTaxSettings": "税設定を編集", "taxProviderLabel": "税プロバイダー", "systemTaxProviderLabel": "システム税プロバイダー", "calculateTaxesAutomaticallyLabel": "税金を自動計算する", "calculateTaxesAutomaticallyHint": "有効にすると、税率が自動的に計算され、カートに適用されます。無効の場合、税金はチェックアウト時に手動で計算する必要があります。サードパーティの税プロバイダーを使用する場合は、手動での税金計算が推奨されます。", "applyTaxesOnGiftCardsLabel": "ギフトカードに税金を適用する", "applyTaxesOnGiftCardsHint": "有効にすると、チェックアウト時にギフトカードに税金が適用されます。一部の国では、税法により購入時にギフトカードへの課税が必要とされています。", "defaultTaxRateLabel": "デフォルト税率", "defaultTaxCodeLabel": "デフォルト税コード"}, "defaultRate": {"sectionTitle": "デフォルト税率"}, "taxRate": {"sectionTitle": "税率", "createTaxRate": "税率を作成", "createTaxRateHint": "地域の新しい税率を作成します。", "deleteRateDescription": "税率「{{name}}」を削除しようとしています。この操作は取り消せません。", "editTaxRate": "税率を編集", "editRateAction": "税率を編集", "editOverridesAction": "上書きを編集", "editOverridesTitle": "税率の上書きを編集", "editOverridesHint": "税率の上書きを指定します。", "deleteTaxRateWarning": "税率「{{name}}」を削除しようとしています。この操作は取り消せません。", "productOverridesLabel": "商品の上書き", "productOverridesHint": "税率の商品上書きを指定します。", "addProductOverridesAction": "商品の上書きを追加", "productTypeOverridesLabel": "商品タイプの上書き", "productTypeOverridesHint": "税率の商品タイプ上書きを指定します。", "addProductTypeOverridesAction": "商品タイプの上書きを追加", "shippingOptionOverridesLabel": "配送オプションの上書き", "shippingOptionOverridesHint": "税率の配送オプション上書きを指定します。", "addShippingOptionOverridesAction": "配送オプションの上書きを追加", "productOverridesHeader": "商品", "productTypeOverridesHeader": "商品タイプ", "shippingOptionOverridesHeader": "配送オプション"}}, "locations": {"domain": "拠点", "editLocation": "拠点を編集", "addSalesChannels": "販売チャネルを追加", "noLocationsFound": "拠点が見つかりません", "selectLocations": "商品を在庫する拠点を選択してください。", "deleteLocationWarning": "拠点「{{name}}」を削除しようとしています。この操作は取り消せません。", "removeSalesChannelsWarning_one": "{{count}}個の販売チャネルを拠点から削除しようとしています。", "removeSalesChannelsWarning_other": "{{count}}個の販売チャネルを拠点から削除しようとしています。", "toast": {"create": "拠点が正常に作成されました", "update": "拠点が正常に更新されました", "removeChannel": "販売チャネルが正常に削除されました"}}, "reservations": {"domain": "予約", "subtitle": "在庫アイテムの予約数量を管理します。", "deleteWarning": "予約を削除しようとしています。この操作は取り消せません。"}, "salesChannels": {"domain": "販売チャネル", "subtitle": "商品を販売するオンラインおよびオフラインのチャネルを管理します。", "createSalesChannel": "販売チャネルを作成", "createSalesChannelHint": "商品を販売するための新しい販売チャネルを作成します。", "enabledHint": "販売チャネルが有効かどうかを指定します。", "removeProductsWarning_one": "{{sales_channel}}から{{count}}個の商品を削除しようとしています。", "removeProductsWarning_other": "{{sales_channel}}から{{count}}個の商品を削除しようとしています。", "addProducts": "商品を追加", "editSalesChannel": "販売チャネルを編集", "productAlreadyAdded": "この商品はすでに販売チャネルに追加されています。", "deleteSalesChannelWarning": "販売チャネル{{name}}を削除しようとしています。この操作は取り消せません。", "toast": {"create": "販売チャネルが正常に作成されました", "update": "販売チャネルが正常に更新されました", "delete": "販売チャネルが正常に削除されました"}, "tooltip": {"cannotDeleteDefault": "デフォルトの販売チャネルは削除できません"}, "products": {"list": {"noRecordsMessage": "この販売チャネルには商品がありません。"}, "add": {"list": {"noRecordsMessage": "最初に商品を作成してください。"}}}}, "apiKeyManagement": {"domain": {"publishable": "公開可能なAPIキー", "secret": "秘密のAPIキー"}, "subtitle": {"publishable": "ストアフロントで使用されるAPIキーを管理し、特定の販売チャネルへのリクエストの範囲を制限します。", "secret": "管理者アプリケーションで管理者ユーザーを認証するために使用されるAPIキーを管理します。"}, "status": {"active": "アクティブ", "revoked": "無効"}, "type": {"publishable": "公開可能", "secret": "秘密"}, "create": {"createPublishableHeader": "公開可能なAPIキーを作成", "createPublishableHint": "特定の販売チャネルへのリクエストの範囲を制限するための新しい公開可能なAPIキーを作成します。", "createSecretHeader": "秘密のAPIキーを作成", "createSecretHint": "認証された管理者ユーザーとしてMedusa APIにアクセスするための新しい秘密のAPIキーを作成します。", "secretKeyCreatedHeader": "秘密のキーが作成されました", "secretKeyCreatedHint": "新しい秘密のキーが生成されました。今すぐコピーして安全に保管してください。これが表示される唯一の機会です。", "copySecretTokenSuccess": "秘密のキーがクリップボードにコピーされました。", "copySecretTokenFailure": "秘密のキーをクリップボードにコピーできませんでした。", "successToast": "APIキーが正常に作成されました。"}, "edit": {"header": "APIキーを編集", "description": "APIキーのタイトルを編集します。", "successToast": "APIキー「{{title}}」が正常に更新されました。"}, "salesChannels": {"title": "販売チャネルを追加", "description": "APIキーが制限される販売チャネルを追加します。", "successToast_one": "{{count}}個の販売チャネルがAPIキーに正常に追加されました。", "successToast_other": "{{count}}個の販売チャネルがAPIキーに正常に追加されました。", "alreadyAddedTooltip": "この販売チャネルはすでにAPIキーに追加されています。", "list": {"noRecordsMessage": "公開可能なAPIキーの範囲内に販売チャネルがありません。"}}, "delete": {"warning": "APIキー「{{title}}」を削除しようとしています。この操作は取り消せません。", "successToast": "APIキー「{{title}}」が正常に削除されました。"}, "revoke": {"warning": "APIキー「{{title}}」を無効化しようとしています。この操作は取り消せません。", "successToast": "APIキー「{{title}}」が正常に無効化されました。"}, "addSalesChannels": {"list": {"noRecordsMessage": "最初に販売チャネルを作成してください。"}}, "removeSalesChannel": {"warning": "販売チャネル「{{name}}」をAPIキーから削除しようとしています。この操作は取り消せません。", "warningBatch_one": "{{count}}個の販売チャネルをAPIキーから削除しようとしています。この操作は取り消せません。", "warningBatch_other": "{{count}}個の販売チャネルをAPIキーから削除しようとしています。この操作は取り消せません。", "successToast": "販売チャネルがAPIキーから正常に削除されました。", "successToastBatch_one": "{{count}}個の販売チャネルがAPIキーから正常に削除されました。", "successToastBatch_other": "{{count}}個の販売チャネルがAPIキーから正常に削除されました。"}, "actions": {"revoke": "APIキーを無効化", "copy": "APIキーをコピー", "copySuccessToast": "APIキーがクリップボードにコピーされました。"}, "table": {"lastUsedAtHeader": "最終使用日時", "createdAtHeader": "無効化日時"}, "fields": {"lastUsedAtLabel": "最終使用日時", "revokedByLabel": "無効化者", "revokedAtLabel": "無効化日時", "createdByLabel": "作成者"}}, "returnReasons": {"domain": "返品理由", "subtitle": "返品された商品の理由を管理します。", "calloutHint": "返品を分類するための理由を管理します。", "editReason": "返品理由を編集", "create": {"header": "返品理由を追加", "subtitle": "最も一般的な返品理由を指定します。", "hint": "返品を分類するための新しい返品理由を作成します。", "successToast": "返品理由「{{label}}」が正常に作成されました。"}, "edit": {"header": "返品理由を編集", "subtitle": "返品理由の値を編集します。", "successToast": "返品理由「{{label}}」が正常に更新されました。"}, "delete": {"confirmation": "返品理由「{{label}}」を削除しようとしています。この操作は取り消せません。", "successToast": "返品理由「{{label}}」が正常に削除されました。"}, "fields": {"value": {"label": "値", "placeholder": "wrong_size", "tooltip": "値は返品理由の一意の識別子である必要があります。"}, "label": {"label": "ラベル", "placeholder": "サイズが合わない"}, "description": {"label": "説明", "placeholder": "顧客が間違ったサイズを受け取った"}}}, "login": {"forgotPassword": "パスワードをお忘れですか？ - <0>リセット</0>", "title": "Medusaへようこそ", "hint": "アカウントエリアにアクセスするにはサインインしてください"}, "invite": {"title": "Medusaへようこそ", "hint": "以下でアカウントを作成してください", "backToLogin": "ログインに戻る", "createAccount": "アカウントを作成", "alreadyHaveAccount": "すでにアカウントをお持ちですか？ - <0>ログイン</0>", "emailTooltip": "Eメールは変更できません。別のEメールを使用したい場合は、新しい招待を送信する必要があります。", "invalidInvite": "招待が無効であるか、期限が切れています。", "successTitle": "アカウントが登録されました", "successHint": "Medusa Adminをすぐに始めましょう。", "successAction": "Medusa Adminを開始", "invalidTokenTitle": "招待トークンが無効です", "invalidTokenHint": "新しい招待リンクをリクエストしてみてください。", "passwordMismatch": "パスワードが一致しません", "toast": {"accepted": "招待が正常に受け入れられました"}}, "resetPassword": {"title": "パスワードをリセット", "hint": "以下にEメールを入力してください。パスワードのリセット方法についての説明をお送りします。", "email": "Eメール", "sendResetInstructions": "リセット手順を送信", "backToLogin": "<0>ログインに戻る</0>", "newPasswordHint": "以下に新しいパスワードを選択してください。", "invalidTokenTitle": "リセットトークンが無効です", "invalidTokenHint": "新しいリセットリンクをリクエストしてみてください。", "expiredTokenTitle": "リセットトークンの有効期限が切れています", "goToResetPassword": "パスワードリセットページへ", "resetPassword": "パスワードをリセット", "newPassword": "新しいパスワード", "repeatNewPassword": "新しいパスワードを再入力", "tokenExpiresIn": "トークンの有効期限は残り<0>{{time}}</0>分です", "successfulRequestTitle": "Eメールを正常に送信しました", "successfulRequest": "パスワードをリセットするためのEメールを送信しました。数分経っても受信していない場合は、迷惑フォルダをご確認ください。", "successfulResetTitle": "パスワードのリセットに成功しました", "successfulReset": "ログインページからログインしてください。", "passwordMismatch": "パスワードが一致しません", "invalidLinkTitle": "リセットリンクが無効です", "invalidLinkHint": "もう一度パスワードのリセットを試みてください。"}, "workflowExecutions": {"domain": "ワークフロー", "subtitle": "Medusaアプリケーションでのワークフロー実行を表示し、追跡します。", "transactionIdLabel": "トランザクションID", "workflowIdLabel": "ワークフローID", "progressLabel": "進捗", "stepsCompletedLabel_one": "{{count}}ステップ中{{completed}}ステップ完了", "stepsCompletedLabel_other": "{{count}}ステップ中{{completed}}ステップ完了", "list": {"noRecordsMessage": "まだワークフローが実行されていません。"}, "history": {"sectionTitle": "履歴", "runningState": "実行中...", "awaitingState": "待機中", "failedState": "失敗", "skippedState": "スキップ", "skippedFailureState": "スキップ（失敗）", "definitionLabel": "定義", "outputLabel": "出力", "compensateInputLabel": "補償処理入力", "revertedLabel": "復元済み", "errorLabel": "エラー"}, "state": {"done": "完了", "failed": "失敗", "reverted": "復元済み", "invoking": "呼び出し中", "compensating": "補償処理中", "notStarted": "未開始"}, "transaction": {"state": {"waitingToCompensate": "補償処理待ち"}}, "step": {"state": {"skipped": "スキップ", "skippedFailure": "スキップ（失敗）", "dormant": "休止中", "timeout": "タイムアウト"}}}, "productTypes": {"domain": "商品タイプ", "subtitle": "商品をタイプ別に整理します。", "create": {"header": "商品タイプを作成", "hint": "商品を分類するための新しい商品タイプを作成します。", "successToast": "商品タイプ「{{value}}」が正常に作成されました。"}, "edit": {"header": "商品タイプを編集", "successToast": "商品タイプ「{{value}}」が正常に更新されました。"}, "delete": {"confirmation": "商品タイプ「{{value}}」を削除しようとしています。この操作は取り消せません。", "successToast": "商品タイプ「{{value}}」が正常に削除されました。"}, "fields": {"value": "値"}}, "productTags": {"domain": "商品タグ", "create": {"header": "商品タグを作成", "subtitle": "商品を分類するための新しい商品タグを作成します。", "successToast": "商品タグ「{{value}}」が正常に作成されました。"}, "edit": {"header": "商品タグを編集", "subtitle": "商品タグの値を編集します。", "successToast": "商品タグ「{{value}}」が正常に更新されました。"}, "delete": {"confirmation": "商品タグ「{{value}}」を削除しようとしています。この操作は取り消せません。", "successToast": "商品タグ「{{value}}」が正常に削除されました。"}, "fields": {"value": "値"}}, "notifications": {"domain": "通知", "emptyState": {"title": "通知はありません", "description": "現在通知はありませんが、通知が届くとここに表示されます。"}, "accessibility": {"description": "Medusaの活動に関する通知がここに表示されます。"}}, "errors": {"serverError": "サーバーエラー - 後でもう一度お試しください。", "invalidCredentials": "Eメールまたはパスワードが間違っています"}, "statuses": {"scheduled": "スケジュール済み", "expired": "期限切れ", "active": "アクティブ", "enabled": "有効", "disabled": "無効"}, "labels": {"productVariant": "商品バリエーション", "prices": "価格", "available": "利用可能", "inStock": "在庫あり", "added": "追加済み", "removed": "削除済み", "from": "から", "to": "へ"}, "fields": {"amount": "金額", "refundAmount": "返金額", "name": "名前", "default": "デフォルト", "lastName": "姓", "firstName": "名", "title": "タイトル", "customTitle": "カスタムタイトル", "manageInventory": "在庫管理", "inventoryKit": "在庫キットあり", "inventoryItems": "在庫アイテム", "inventoryItem": "在庫アイテム", "requiredQuantity": "必要数量", "description": "説明", "email": "Eメール", "password": "パスワード", "repeatPassword": "パスワードを再入力", "confirmPassword": "パスワードを確認", "newPassword": "新しいパスワード", "repeatNewPassword": "新しいパスワードを再入力", "categories": "カテゴリー", "shippingMethod": "配送方法", "configurations": "設定", "conditions": "条件", "category": "カテゴリー", "collection": "コレクション", "discountable": "割引可能", "handle": "ハンドル", "subtitle": "サブタイトル", "by": "作成者", "item": "アイテム", "qty": "数量", "limit": "制限", "tags": "タグ", "type": "タイプ", "reason": "理由", "none": "なし", "all": "すべて", "search": "検索", "percentage": "割合", "sales_channels": "販売チャネル", "customer_groups": "顧客グループ", "product_tags": "商品タグ", "product_types": "商品タイプ", "product_collections": "商品コレクション", "status": "ステータス", "code": "コード", "value": "値", "disabled": "無効", "dynamic": "動的", "normal": "通常", "years": "年", "months": "月", "days": "日", "hours": "時間", "minutes": "分", "totalRedemptions": "総償還数", "countries": "国", "paymentProviders": "決済プロバイダー", "refundReason": "返金理由", "fulfillmentProviders": "フルフィルメントプロバイダー", "fulfillmentProvider": "フルフィルメントプロバイダー", "providers": "プロバイダー", "availability": "利用可能性", "inventory": "在庫", "optional": "任意", "note": "メモ", "automaticTaxes": "自動税計算", "taxInclusivePricing": "税込価格", "currency": "通貨", "address": "住所", "address2": "アパート、部屋番号など", "city": "市区町村", "postalCode": "郵便番号", "country": "国", "state": "州", "province": "都道府県", "company": "会社", "phone": "電話番号", "metadata": "メタデータ", "selectCountry": "国を選択", "products": "商品", "variants": "バリエーション", "orders": "注文", "account": "アカウント", "total": "注文合計", "paidTotal": "支払済み合計", "totalExclTax": "税抜合計", "subtotal": "小計", "shipping": "配送", "outboundShipping": "発送配送", "returnShipping": "返品配送", "tax": "税金", "created": "作成日", "key": "キー", "customer": "顧客", "date": "日付", "order": "注文", "fulfillment": "フルフィルメント", "provider": "プロバイダー", "payment": "支払い", "items": "アイテム", "salesChannel": "販売チャネル", "region": "地域", "discount": "割引", "role": "役割", "sent": "送信済み", "salesChannels": "販売チャネル", "product": "商品", "createdAt": "作成日時", "updatedAt": "更新日時", "revokedAt": "無効化日時", "true": "True", "false": "False", "giftCard": "ギフトカード", "tag": "タグ", "dateIssued": "発行日", "issuedDate": "発行日", "expiryDate": "有効期限", "price": "価格", "priceTemplate": "価格 {{regionOrCurrency}}", "height": "高さ", "width": "幅", "length": "長さ", "weight": "重量", "midCode": "MIDコード", "hsCode": "HSコード", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "在庫数量", "barcode": "バーコード", "countryOfOrigin": "原産国", "material": "素材", "thumbnail": "サムネイル", "sku": "SKU", "managedInventory": "管理在庫", "allowBackorder": "バックオーダー許可", "inStock": "在庫あり", "location": "拠点", "quantity": "数量", "variant": "バリエーション", "id": "ID", "parent": "親", "minSubtotal": "最小小計", "maxSubtotal": "最大小計", "shippingProfile": "配送プロファイル", "summary": "概要", "details": "詳細", "label": "ラベル", "rate": "料率", "requiresShipping": "配送必要", "unitPrice": "単価", "startDate": "開始日", "endDate": "終了日", "draft": "下書き", "values": "値"}, "dateTime": {"years_one": "年", "years_other": "年", "months_one": "月", "months_other": "月", "weeks_one": "週", "weeks_other": "週", "days_one": "日", "days_other": "日", "hours_one": "時", "hours_other": "時", "minutes_one": "分", "minutes_other": "分", "seconds_one": "秒", "seconds_other": "秒"}}