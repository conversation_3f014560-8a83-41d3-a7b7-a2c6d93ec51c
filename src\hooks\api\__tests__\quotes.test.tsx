import { renderHook } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactNode } from "react"
import { useQuotes, useQuote, useSendQuote, useRejectQuote, useCreateQuoteMessage } from "../quotes"

// 创建测试用的QueryClient
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

// 测试包装器
const createWrapper = () => {
  const queryClient = createTestQueryClient()
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe("Quotes API Hooks", () => {
  beforeEach(() => {
    // Mock fetch
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  describe("useQuotes", () => {
    it("should create query with correct key", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useQuotes(), { wrapper })
      
      // 验证hook能够正常创建，不会抛出错误
      expect(result.current).toBeDefined()
      expect(typeof result.current.data).toBe("undefined") // 初始状态
      expect(typeof result.current.isLoading).toBe("boolean")
      expect(typeof result.current.error).toBe("object")
    })

    it("should create query with parameters", () => {
      const wrapper = createWrapper()
      const query = { status: "pending_merchant" as const, limit: 10 }
      const { result } = renderHook(() => useQuotes(query), { wrapper })
      
      expect(result.current).toBeDefined()
    })
  })

  describe("useQuote", () => {
    it("should create query for single quote", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useQuote("quote-123"), { wrapper })
      
      expect(result.current).toBeDefined()
    })
  })

  describe("useSendQuote", () => {
    it("should create mutation for sending quote", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useSendQuote("quote-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
      expect(typeof result.current.mutateAsync).toBe("function")
    })
  })

  describe("useRejectQuote", () => {
    it("should create mutation for rejecting quote", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useRejectQuote("quote-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })

  describe("useCreateQuoteMessage", () => {
    it("should create mutation for creating quote message", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useCreateQuoteMessage("quote-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })
})