import { createColumnHelper } from "@tanstack/react-table"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { TextCell } from "../../../../../components/table/table-cells/common/text-cell"
import { CreatedAtCell } from "../../../../../components/table/table-cells/common/created-at-cell"
import { MoneyAmountCell } from "../../../../../components/table/table-cells/common/money-amount-cell"
import { ApprovalStatusBadge } from "../approval-status-badge"

// 基于实际API返回的数据结构定义类型
type CartWithApproval = {
  id: string
  email: string
  created_at: string
  updated_at: string
  items: Array<{
    unit_price: number
    quantity: number
  }>
  approvals: Array<{
    id: string
    type: string
    status: string
    created_at: string
    updated_at: string
  }>
  approval_status: {
    status: string
  }
  company: {
    name: string
  }
}

const columnHelper = createColumnHelper<CartWithApproval>()

export const useApprovalsTableColumns = () => {
  const { t } = useTranslation()

  return useMemo(
    () => [
      columnHelper.accessor("id", {
        header: t("approvals.table.id", "ID"),
        cell: ({ getValue }) => {
          const id = getValue()
          return (
            <span className="font-mono text-sm">
              {id.slice(-8).toUpperCase()}
            </span>
          )
        },
      }),
      columnHelper.accessor("approvals", {
        header: t("approvals.table.type", "Type"),
        cell: ({ getValue }) => {
          const approvals = getValue()
          const type = approvals?.[0]?.type || "-"
          return (
            <div className="capitalize">
              <TextCell text={type} />
            </div>
          )
        },
      }),
      columnHelper.accessor((row) => row.company?.name, {
        id: "company.name",
        header: t("approvals.table.company", "Company"),
        cell: ({ getValue }) => <TextCell text={getValue() || "-"} />,
      }),
      columnHelper.accessor("email", {
        header: t("approvals.table.customer", "Customer"),
        cell: ({ getValue }) => {
          const email = getValue()
          return <TextCell text={email || "-"} />
        },
      }),
      columnHelper.accessor("items", {
        header: t("approvals.table.amount", "Amount"),
        cell: ({ getValue }) => {
          const items = getValue()
          if (!items || items.length === 0) return <TextCell text="-" />

          // 计算总金额
          const totalAmount = items.reduce((sum, item) => {
            return sum + (item.unit_price * item.quantity)
          }, 0)

          return <MoneyAmountCell amount={totalAmount} currencyCode="EUR" />
        },
      }),
      columnHelper.accessor("approval_status.status", {
        header: t("approvals.table.status", "Status"),
        cell: ({ getValue }) => <ApprovalStatusBadge status={getValue()} />,
      }),
      columnHelper.accessor("created_at", {
        header: t("approvals.table.createdAt", "Created At"),
        cell: ({ getValue }) => <CreatedAtCell date={getValue()} />,
      }),
      columnHelper.accessor("updated_at", {
        header: t("approvals.table.updatedAt", "Updated At"),
        cell: ({ getValue }) => <CreatedAtCell date={getValue()} />,
      }),
    ],
    [t]
  )
}
