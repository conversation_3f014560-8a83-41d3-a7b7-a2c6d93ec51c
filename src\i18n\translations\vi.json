{"$schema": "./$schema.json", "general": {"ascending": "<PERSON><PERSON><PERSON>", "descending": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "end": "<PERSON><PERSON><PERSON>", "open": "Mở", "close": "Đ<PERSON><PERSON>", "apply": "<PERSON><PERSON>", "range": "Phạm vi", "search": "<PERSON><PERSON><PERSON>", "of": "c<PERSON>a", "results": "kế<PERSON> quả", "pages": "trang", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "prev": "<PERSON><PERSON><PERSON><PERSON><PERSON> đó", "is": "là", "timeline": "<PERSON><PERSON><PERSON> thời gian", "success": "<PERSON><PERSON><PERSON><PERSON> công", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "tip": "Mẹo", "error": "Lỗi", "select": "<PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON> ch<PERSON>n", "enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt", "expired": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "revoked": "<PERSON><PERSON> thu hồi", "new": "<PERSON><PERSON><PERSON>", "modified": "Đ<PERSON> sửa đổi", "added": "<PERSON><PERSON> thêm", "removed": "Đã xóa", "admin": "<PERSON><PERSON><PERSON><PERSON> trị", "store": "<PERSON><PERSON><PERSON> h<PERSON>", "details": "<PERSON> ti<PERSON>", "items_one": "{{count}} mục", "items_other": "{{count}} mục", "countSelected": "{{count}} đ<PERSON> ch<PERSON>n", "countOfTotalSelected": "{{count}} trong tổng số {{total}} đ<PERSON> chọn", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} nữa", "areYouSure": "Bạn có chắc không?", "areYouSureDescription": "Bạn sắp xóa {{entity}} {{title}}. <PERSON><PERSON><PERSON> động này không thể hoàn tác.", "noRecordsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bản ghi nào", "typeToConfirm": "<PERSON><PERSON> lòng nhập {val} để xác nhận:", "noResultsTitle": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "noResultsMessage": "<PERSON><PERSON><PERSON> thử thay đổi bộ lọc hoặc truy vấn tìm kiếm", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> có kết quả tìm kiếm", "noSearchResultsFor": "<PERSON><PERSON><PERSON><PERSON> có kết quả tìm kiếm cho <0>'{{query}}'</0>", "noRecordsTitle": "<PERSON><PERSON><PERSON><PERSON> có bản ghi", "noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có bản ghi nào để hiển thị", "unsavedChangesTitle": "Bạn có chắc muốn rời khỏi biểu mẫu này không?", "unsavedChangesDescription": "<PERSON>ạn có những thay đổi chưa lưu sẽ bị mất nếu thoát khỏi biểu mẫu này.", "includesTaxTooltip": "<PERSON><PERSON><PERSON> trong cột này đã bao gồm thuế.", "excludesTaxTooltip": "<PERSON><PERSON><PERSON> trong cột này chưa bao gồm thuế.", "noMoreData": "<PERSON><PERSON><PERSON><PERSON> còn dữ liệu"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} khóa", "numberOfKeys_other": "{{count}} khóa", "drawer": {"header_one": "JSON <0>· {{count}} khóa</0>", "header_other": "JSON <0>· {{count}} khóa</0>", "description": "<PERSON>em dữ liệu JSON của đối tượng này."}}, "metadata": {"header": "<PERSON><PERSON><PERSON> dữ liệu", "numberOfKeys_one": "{{count}} khóa", "numberOfKeys_other": "{{count}} khóa", "edit": {"header": "Chỉnh sửa <PERSON>u dữ liệu", "description": "Chỉnh sửa siêu dữ liệu cho đối tượng này.", "successToast": "<PERSON>êu dữ liệu đã đư<PERSON><PERSON> cập nhật thành công.", "actions": {"insertRowAbove": "<PERSON><PERSON>n dòng phía trên", "insertRowBelow": "<PERSON><PERSON>n dòng phía dưới", "deleteRow": "<PERSON><PERSON><PERSON> dòng"}, "labels": {"key": "Khóa", "value": "<PERSON><PERSON><PERSON> trị"}, "complexRow": {"label": "Một số dòng bị vô hiệu hóa", "description": "Đ<PERSON>i tượng này chứa siêu dữ liệu không nguyên thủy, chẳng hạn như mảng hoặc đối tượng, không thể chỉnh sửa tại đây. Đ<PERSON> chỉnh sửa các dòng bị vô hiệu hóa, hãy sử dụng API trực tiếp.", "tooltip": "<PERSON>òng này bị vô hiệu hóa vì nó chứa dữ liệu không nguyên thủy."}}}, "validation": {"mustBeInt": "<PERSON><PERSON><PERSON> trị phải là số nguyên.", "mustBePositive": "<PERSON><PERSON><PERSON> trị phải là số dương."}, "actions": {"save": "<PERSON><PERSON><PERSON>", "saveAsDraft": "<PERSON><PERSON><PERSON> dạng <PERSON>p", "copy": "Sao chép", "copied": "Đã sao chép", "duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "publish": "<PERSON><PERSON><PERSON>", "create": "Tạo", "delete": "Xóa", "remove": "Gỡ bỏ", "revoke": "<PERSON><PERSON> h<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "forceConfirm": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "continueEdit": "<PERSON><PERSON><PERSON><PERSON> tục chỉnh sửa", "enable": "<PERSON><PERSON><PERSON>", "disable": "Tắt", "undo": "<PERSON><PERSON><PERSON>", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "viewDetails": "<PERSON>em chi tiết", "back": "Quay lại", "close": "Đ<PERSON><PERSON>", "showMore": "<PERSON><PERSON><PERSON> thị thêm", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> v<PERSON><PERSON>", "idCopiedToClipboard": "ID đã đư<PERSON><PERSON> sao chép vào bộ nhớ tạm", "addReason": "<PERSON><PERSON><PERSON><PERSON> lý do", "addNote": "<PERSON><PERSON><PERSON><PERSON> ghi chú", "reset": "Đặt lại", "confirm": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "addItems": "<PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON> tất cả", "apply": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "browse": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "hide": "Ẩn", "export": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON><PERSON> động này không thể hoàn tác"}, "operators": {"in": "Trong"}, "app": {"search": {"label": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> kiếm toàn bộ cửa hàng của bạn, bao gồ<PERSON> đơn hàng, s<PERSON><PERSON> <PERSON>h<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> hàng và nhiều thứ kh<PERSON>.", "allAreas": "<PERSON><PERSON><PERSON> cả khu vực", "navigation": "<PERSON><PERSON><PERSON><PERSON>", "openResult": "Mở kết quả", "showMore": "<PERSON><PERSON><PERSON> thị thêm", "placeholder": "Chuyển đến hoặc tìm bất cứ thứ gì...", "noResultsTitle": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsMessage": "<PERSON><PERSON>g tôi không thể tìm thấy bất cứ thứ gì khớp với tìm kiếm của bạn.", "emptySearchTitle": "<PERSON><PERSON><PERSON><PERSON> để tìm kiếm", "emptySearchMessage": "<PERSON><PERSON><PERSON><PERSON> từ khóa hoặc cụm từ để khám phá.", "loadMore": "<PERSON><PERSON><PERSON> thêm {{count}}", "groups": {"all": "<PERSON><PERSON><PERSON> cả khu vực", "customer": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productVariant": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm", "inventory": "<PERSON><PERSON>", "reservation": "Đặt trước", "category": "<PERSON><PERSON>", "collection": "<PERSON><PERSON> s<PERSON>u tập", "order": "<PERSON><PERSON><PERSON> hàng", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "campaign": "<PERSON><PERSON><PERSON>", "priceList": "Bảng giá", "user": "<PERSON><PERSON><PERSON><PERSON> dùng", "region": "<PERSON><PERSON> v<PERSON>", "taxRegion": "<PERSON><PERSON> v<PERSON>c thuế", "returnReason": "Lý do trả hàng", "salesChannel": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "productType": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "productTag": "Thẻ sản phẩm", "location": "<PERSON><PERSON> trí", "shippingProfile": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "publishableApiKey": "Khóa API công khai", "secretApiKey": "Khóa API b<PERSON> mật", "command": "<PERSON><PERSON><PERSON>", "navigation": "<PERSON><PERSON><PERSON><PERSON>"}}, "keyboardShortcuts": {"pageShortcut": "<PERSON><PERSON><PERSON><PERSON> đến", "settingShortcut": "Cài đặt", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "sau đó", "navigation": {"goToOrders": "<PERSON><PERSON><PERSON> hàng", "goToProducts": "<PERSON><PERSON><PERSON> p<PERSON>m", "goToCollections": "<PERSON><PERSON> s<PERSON>u tập", "goToCategories": "<PERSON><PERSON>", "goToCustomers": "<PERSON><PERSON><PERSON><PERSON>", "goToCustomerGroups": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "goToInventory": "<PERSON><PERSON>", "goToReservations": "Đặt trước", "goToPriceLists": "Bảng giá", "goToPromotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "goToCampaigns": "<PERSON><PERSON><PERSON>"}, "settings": {"goToSettings": "Cài đặt", "goToStore": "<PERSON><PERSON><PERSON> h<PERSON>", "goToUsers": "<PERSON><PERSON><PERSON><PERSON> dùng", "goToRegions": "<PERSON><PERSON> v<PERSON>", "goToTaxRegions": "<PERSON><PERSON> v<PERSON>c thuế", "goToSalesChannels": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "goToProductTypes": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "goToLocations": "<PERSON><PERSON> trí", "goToPublishableApiKeys": "Khóa API công khai", "goToSecretApiKeys": "Khóa API b<PERSON> mật", "goToWorkflows": "<PERSON><PERSON><PERSON> công vi<PERSON>c", "goToProfile": "<PERSON><PERSON> sơ", "goToReturnReasons": "Lý do trả hàng"}}, "menus": {"user": {"documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "changelog": "<PERSON><PERSON><PERSON><PERSON> ký thay đổi", "shortcuts": "<PERSON><PERSON><PERSON>", "profileSettings": "<PERSON>ài đặt hồ sơ", "theme": {"label": "Chủ đề", "dark": "<PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>", "system": "<PERSON><PERSON> th<PERSON>"}}, "store": {"label": "<PERSON><PERSON><PERSON> h<PERSON>", "storeSettings": "<PERSON><PERSON><PERSON> đặt cửa hàng"}, "actions": {"logout": "<PERSON><PERSON><PERSON> xu<PERSON>"}}, "nav": {"accessibility": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> điều hướng cho bảng điều khiển."}, "common": {"extensions": "Tiện ích mở rộng"}, "main": {"store": "<PERSON><PERSON><PERSON> h<PERSON>", "storeSettings": "<PERSON><PERSON><PERSON> đặt cửa hàng"}, "settings": {"header": "Cài đặt", "general": "<PERSON>", "developer": "<PERSON><PERSON><PERSON> ph<PERSON>t triển", "myAccount": "<PERSON><PERSON><PERSON>n của tôi"}}}, "dataGrid": {"columns": {"view": "Xem", "resetToDefault": "Đặt lại về mặc định", "disabled": "<PERSON><PERSON><PERSON><PERSON> thay đổi cột nào hiển thị đã bị vô hiệu hóa."}, "shortcuts": {"label": "<PERSON><PERSON><PERSON>", "commands": {"undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "copy": "Sao chép", "paste": "Dán", "edit": "Chỉnh sửa", "delete": "Xóa", "clear": "<PERSON><PERSON><PERSON>", "moveUp": "<PERSON> chuyển lên", "moveDown": "<PERSON> xuống", "moveLeft": "<PERSON> sang trái", "moveRight": "<PERSON> sang phải", "moveTop": "<PERSON> chuyển lên đầu", "moveBottom": "<PERSON> chuyển xuống cuối", "selectDown": "<PERSON><PERSON><PERSON>", "selectUp": "<PERSON><PERSON><PERSON> l<PERSON>", "selectColumnDown": "<PERSON><PERSON><PERSON> c<PERSON>t xu<PERSON>ng", "selectColumnUp": "<PERSON><PERSON><PERSON> c<PERSON>t lên", "focusToolbar": "Tập trung vào thanh công cụ", "focusCancel": "<PERSON>ập trung vào hủy"}}, "errors": {"fixError": "<PERSON><PERSON><PERSON> lỗi", "count_one": "{{count}} lỗi", "count_other": "{{count}} lỗi"}}, "filters": {"sortLabel": "<PERSON><PERSON><PERSON>p", "filterLabel": "<PERSON><PERSON><PERSON>", "searchLabel": "<PERSON><PERSON><PERSON>", "date": {"today": "<PERSON><PERSON><PERSON> nay", "lastSevenDays": "7 ngày qua", "lastThirtyDays": "30 ngày qua", "lastNinetyDays": "90 ngày qua", "lastTwelveMonths": "12 tháng qua", "custom": "<PERSON><PERSON><PERSON> chỉnh", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "starting": "<PERSON><PERSON><PERSON> đ<PERSON>u", "ending": "<PERSON><PERSON><PERSON>"}, "compare": {"lessThan": "Nhỏ hơn", "greaterThan": "<PERSON><PERSON><PERSON>", "exact": "<PERSON><PERSON><PERSON>", "range": "Phạm vi", "lessThanLabel": "nhỏ hơn {{value}}", "greaterThanLabel": "lớn hơn {{value}}", "andLabel": "và"}, "sorting": {"alphabeticallyAsc": "T<PERSON> A đến Z", "alphabeticallyDesc": "T<PERSON> Z đến A", "dateAsc": "<PERSON><PERSON><PERSON> nh<PERSON>t tr<PERSON>c", "dateDesc": "<PERSON><PERSON> nh<PERSON>t tr<PERSON>"}, "radio": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "true": "<PERSON><PERSON><PERSON>", "false": "<PERSON>"}, "addFilter": "<PERSON><PERSON><PERSON><PERSON> bộ lọc"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON> c<PERSON><PERSON> không hợp lệ", "badRequestMessage": "<PERSON><PERSON><PERSON> chủ không thể hiểu yêu cầu do cú pháp không đúng.", "notFoundTitle": "404 - <PERSON><PERSON><PERSON><PERSON> có trang tại địa chỉ này", "notFoundMessage": "<PERSON><PERSON><PERSON> tra URL và thử lại, hoặc sử dụng thanh tìm kiếm để tìm kiếm thứ bạn đang cần.", "internalServerErrorTitle": "500 - Lỗi máy chủ nội bộ", "internalServerErrorMessage": "<PERSON><PERSON> xảy ra lỗi bất ngờ trên máy chủ. <PERSON><PERSON> lòng thử lại sau.", "defaultTitle": "Đ<PERSON> xảy ra lỗi", "defaultMessage": "<PERSON><PERSON> x<PERSON>y ra lỗi bất ngờ khi hiển thị trang này.", "noMatchMessage": "<PERSON>rang bạn đang tìm không tồn tại.", "backToDashboard": "Quay lại bảng đi<PERSON> k<PERSON>n"}, "addresses": {"title": "Địa chỉ", "shippingAddress": {"header": "Đ<PERSON>a chỉ giao hàng", "editHeader": "Chỉnh sửa địa chỉ giao hàng", "editLabel": "Đ<PERSON>a chỉ giao hàng", "label": "Đ<PERSON>a chỉ giao hàng"}, "billingAddress": {"header": "<PERSON><PERSON><PERSON> chỉ thanh toán", "editHeader": "Chỉnh sửa địa chỉ thanh toán", "editLabel": "<PERSON><PERSON><PERSON> chỉ thanh toán", "label": "<PERSON><PERSON><PERSON> chỉ thanh toán", "sameAsShipping": "G<PERSON><PERSON><PERSON> với địa chỉ giao hàng"}, "contactHeading": "<PERSON><PERSON><PERSON>", "locationHeading": "<PERSON><PERSON> trí"}, "email": {"editHeader": "Chỉnh sửa email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "<PERSON>y<PERSON><PERSON> quyền sở hữu", "label": "<PERSON>y<PERSON><PERSON> quyền sở hữu", "details": {"order": "<PERSON> tiết đơn hàng", "draft": "<PERSON> ti<PERSON>t bản n<PERSON>p"}, "currentOwner": {"label": "Ch<PERSON> sở hữu hiện tại", "hint": "Chủ sở hữu hiện tại của đơn hàng."}, "newOwner": {"label": "Chủ sở hữu mới", "hint": "Chủ sở hữu mới để chuyển giao đơn hàng."}, "validation": {"mustBeDifferent": "Chủ sở hữu mới phải khác với chủ sở hữu hiện tại.", "required": "<PERSON><PERSON> sở hữu mới là bắt buộc."}}, "sales_channels": {"availableIn": "C<PERSON> sẵn trong <0>{{x}}</0> trên <1>{{y}}</1> kê<PERSON> b<PERSON> hàng"}, "products": {"domain": "<PERSON><PERSON><PERSON> p<PERSON>m", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> sản phẩm đầu tiên của bạn để bắt đầu bán hàng."}, "edit": {"header": "Chỉnh s<PERSON>a sản phẩm", "description": "Chỉnh sửa chi tiết sản phẩm.", "successToast": "<PERSON><PERSON><PERSON> phẩm {{title}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "create": {"title": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "description": "<PERSON><PERSON><PERSON> một sản phẩm mới.", "header": "<PERSON>", "tabs": {"details": "<PERSON> ti<PERSON>", "organize": "<PERSON><PERSON><PERSON>p", "variants": "<PERSON><PERSON><PERSON><PERSON> thể", "inventory": "<PERSON><PERSON> thành phần trong kho"}, "errors": {"variants": "<PERSON><PERSON> lòng chọn ít nhất một biến thể.", "options": "<PERSON><PERSON> lòng tạo ít nhất một tùy chọn.", "uniqueSku": "SKU phải là duy nhất."}, "inventory": {"heading": "<PERSON><PERSON> thành phần trong kho", "label": "<PERSON><PERSON><PERSON><PERSON> các mục hàng từ kho để tạo thành bộ thành phần cho biến thể này.", "itemPlaceholder": "<PERSON><PERSON><PERSON> mục hàng trong kho", "quantityPlaceholder": "<PERSON><PERSON>n bao nhiêu cái cho bộ này?"}, "variants": {"header": "<PERSON><PERSON><PERSON><PERSON> thể", "subHeadingTitle": "<PERSON><PERSON>, đ<PERSON><PERSON> là sản phẩm có biến thể", "subHeadingDescription": "<PERSON><PERSON> bỏ chọn, chún<PERSON> tôi sẽ tạo một biến thể mặc định cho bạn", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "Nhỏ, Trung bình, Lớn"}, "productVariants": {"label": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm", "hint": "<PERSON><PERSON><PERSON> hạng này sẽ ảnh hưởng đến thứ tự của các biến thể trên giao diện cửa hàng của bạn.", "alert": "<PERSON><PERSON><PERSON><PERSON> tùy chọn để tạo biến thể.", "tip": "<PERSON><PERSON><PERSON> biến thể không được chọn sẽ không được tạo. Bạn luôn có thể tạo và chỉnh sửa biến thể sau, nhưng danh sách này phù hợp với các tùy chọn biến thể của sản phẩm của bạn."}, "productOptions": {"label": "<PERSON><PERSON><PERSON> ch<PERSON>n sản phẩm", "hint": "<PERSON><PERSON><PERSON>nh các tùy chọn cho sản phẩm, ví dụ: <PERSON><PERSON><PERSON>, k<PERSON><PERSON> th<PERSON>, v.v."}}, "successToast": "<PERSON><PERSON><PERSON> phẩm {{title}} đã đư<PERSON>c tạo thành công."}, "export": {"header": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sản ph<PERSON>m", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm ra file CSV.", "success": {"title": "<PERSON><PERSON>g tôi đang xử lý yêu cầu xuất của bạn", "description": "V<PERSON><PERSON><PERSON> xuất dữ liệu có thể mất vài phút. <PERSON><PERSON>g tôi sẽ thông báo khi hoàn tất."}, "filters": {"title": "<PERSON><PERSON> lọc", "description": "<PERSON><PERSON> dụng bộ lọc trong bảng tổng quan để điều chỉnh chế độ xem này"}, "columns": {"title": "<PERSON><PERSON><PERSON>", "description": "Tù<PERSON> chỉnh dữ liệu xuất để đáp ứng nhu cầu cụ thể"}}, "import": {"header": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm", "uploadLabel": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "uploadHint": "Kéo và thả file CSV hoặc nhấp để tải lên", "description": "<PERSON><PERSON><PERSON><PERSON> sản phẩm bằng cách cung cấp file CSV theo định dạng được xác định trước", "template": {"title": "<PERSON><PERSON><PERSON><PERSON> chắc chắn về cách sắp xếp danh sách của bạn?", "description": "<PERSON><PERSON><PERSON> xuống mẫu bên dưới để đảm bảo bạn đang tuân theo đúng định dạng."}, "upload": {"title": "T<PERSON>i lên file CSV", "description": "Thông qua việc nhập, bạn có thể thêm hoặc cập nhật sản phẩm. <PERSON><PERSON> cập nhật sản phẩm hiện có, bạn phải sử dụng handle và ID hiện tại; để cập nhật biến thể hiện có, bạn phải sử dụng ID hiện tại. Bạn sẽ được yêu cầu xác nhận trước khi chúng tôi nhập sản phẩm.", "preprocessing": "<PERSON><PERSON> xử lý trước...", "productsToCreate": "<PERSON><PERSON><PERSON> phẩm sẽ được tạo", "productsToUpdate": "<PERSON><PERSON><PERSON> phẩm sẽ được cập nhật"}, "success": {"title": "<PERSON><PERSON>g tôi đang xử lý yêu cầu nhập của bạn", "description": "<PERSON><PERSON><PERSON><PERSON> nhập dữ liệu có thể mất một chút thời gian. <PERSON>úng tôi sẽ thông báo khi hoàn tất."}}, "deleteWarning": "Bạn sắp x<PERSON>a sản phẩm {{title}}. <PERSON>ành động này không thể hoàn tác.", "variants": {"header": "<PERSON><PERSON><PERSON><PERSON> thể", "empty": {"heading": "<PERSON><PERSON><PERSON><PERSON> có biến thể", "description": "<PERSON><PERSON><PERSON><PERSON> có biến thể nào để hiển thị."}, "filtered": {"heading": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "description": "<PERSON><PERSON><PERSON><PERSON> có biến thể nào khớp với tiêu chí lọc hiện tại."}}, "attributes": "<PERSON><PERSON><PERSON><PERSON>", "editAttributes": "Chỉnh sửa thu<PERSON><PERSON> t<PERSON>h", "editOptions": "Chỉnh sửa tùy chọn", "editPrices": "Chỉnh sửa giá", "media": {"label": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n", "editHint": "Thêm phương tiện vào sản phẩm để hiển thị trên giao diện cửa hàng của bạn.", "makeThumbnail": "Đặt làm ảnh thu nhỏ", "uploadImagesLabel": "<PERSON><PERSON><PERSON> lên h<PERSON>", "uploadImagesHint": "<PERSON><PERSON><PERSON> và thả hình ảnh vào đây hoặc nhấp để tải lên.", "invalidFileType": "'{{name}}' không phải là loại file được hỗ trợ. Các loại file được hỗ trợ là: {{types}}.", "failedToUpload": "<PERSON><PERSON><PERSON><PERSON> thể tải lên phương tiện vừa thêm. <PERSON><PERSON> lòng thử lại.", "deleteWarning_one": "Bạn sắp xóa {{count}} hình ảnh. Hành động này không thể hoàn tác.", "deleteWarning_other": "Bạn sắp xóa {{count}} hình ảnh. Hành động này không thể hoàn tác.", "deleteWarningWithThumbnail_one": "Bạn sắp xóa {{count}} hình ảnh bao gồm ảnh thu nhỏ. Hành động này không thể hoàn tác.", "deleteWarningWithThumbnail_other": "Bạn sắp xóa {{count}} hình ảnh bao gồm ảnh thu nhỏ. Hành động này không thể hoàn tác.", "thumbnailTooltip": "Ảnh thu nhỏ", "galleryLabel": "<PERSON><PERSON><PERSON> vi<PERSON>n", "downloadImageLabel": "<PERSON><PERSON><PERSON> xuống hình <PERSON>nh hiện tại", "deleteImageLabel": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh hiện tại", "emptyState": {"header": "<PERSON><PERSON><PERSON> c<PERSON> ph<PERSON><PERSON><PERSON> tiện", "description": "Thêm phương tiện vào sản phẩm để hiển thị trên giao diện cửa hàng của bạn.", "action": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> tiện"}, "successToast": "<PERSON>ương tiện đã đư<PERSON><PERSON> cập nhật thành công."}, "discountableHint": "<PERSON><PERSON> bỏ chọn, c<PERSON><PERSON> chương trình giảm giá sẽ không áp dụng cho sản phẩm này.", "noSalesChannels": "<PERSON><PERSON><PERSON><PERSON> có sẵn trên bất kỳ kênh bán hàng nào", "variantCount_one": "{{count}} biến thể", "variantCount_other": "{{count}} biến thể", "deleteVariantWarning": "Bạn sắp xóa biến thể {{title}}. <PERSON><PERSON><PERSON> động này không thể hoàn tác.", "productStatus": {"draft": "Nháp", "published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "proposed": "<PERSON><PERSON> đề xuất", "rejected": "Đ<PERSON> từ chối"}, "fields": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "hint": "Đặt cho sản phẩm một tiêu đề ngắn gọn và rõ ràng.<0/><PERSON><PERSON> dài khuyến nghị là 50-60 ký tự để tối ưu hóa cho công cụ tìm kiếm."}, "subtitle": {"label": "<PERSON><PERSON> đề"}, "handle": {"label": "<PERSON><PERSON><PERSON> danh", "tooltip": "Đị<PERSON> danh được sử dụng để tham chiếu sản phẩm trên giao diện cửa hàng của bạn. <PERSON><PERSON><PERSON> không chỉ định, định danh sẽ được tạo từ tiêu đề sản phẩm."}, "description": {"label": "<PERSON><PERSON>", "hint": "Đặt cho sản phẩm một mô tả ngắn gọn và rõ ràng.<0/><PERSON><PERSON> dài khuyến nghị là 120-160 ký tự để tối ưu hóa cho công cụ tìm kiếm."}, "discountable": {"label": "<PERSON><PERSON> thể giảm giá", "hint": "<PERSON><PERSON> bỏ chọn, c<PERSON><PERSON> chương trình giảm giá sẽ không áp dụng cho sản phẩm này"}, "shipping_profile": {"label": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "hint": "<PERSON><PERSON>t nối sản phẩm với một hồ sơ vận chuyển"}, "type": {"label": "<PERSON><PERSON><PERSON>"}, "collection": {"label": "<PERSON><PERSON> s<PERSON>u tập"}, "categories": {"label": "<PERSON><PERSON>"}, "tags": {"label": "Thẻ"}, "sales_channels": {"label": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "hint": "<PERSON><PERSON>n phẩm này sẽ chỉ có sẵn trên kênh bán hàng mặc định nếu không thay đổi."}, "countryOrigin": {"label": "<PERSON><PERSON><PERSON>"}, "material": {"label": "<PERSON><PERSON><PERSON> l<PERSON>"}, "width": {"label": "<PERSON><PERSON><PERSON> r<PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON> dài"}, "height": {"label": "<PERSON><PERSON><PERSON> cao"}, "weight": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options": {"label": "<PERSON><PERSON><PERSON> ch<PERSON>n sản phẩm", "hint": "<PERSON><PERSON><PERSON> chọn được dùng để xác định màu sắ<PERSON>, <PERSON><PERSON><PERSON> th<PERSON>, v.v. c<PERSON><PERSON> sản phẩm", "add": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "optionTitle": "<PERSON>i<PERSON><PERSON> đề tùy chọn", "optionTitlePlaceholder": "<PERSON><PERSON><PERSON>", "variations": "<PERSON><PERSON><PERSON><PERSON> thể (phân tách bằng dấu phẩy)", "variantionsPlaceholder": "Đỏ, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> l<PERSON>"}, "variants": {"label": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm", "hint": "<PERSON><PERSON><PERSON> biến thể không được chọn sẽ không được tạo. Thứ hạng này sẽ ảnh hưởng đến cách sắp xếp biến thể trên giao diện người dùng."}, "mid_code": {"label": "Mã trung gian"}, "hs_code": {"label": "Mã HS"}}, "variant": {"edit": {"header": "Chỉnh sửa biến thể", "success": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm đã được chỉnh sửa thành công"}, "create": {"header": "<PERSON> tiết biến thể"}, "deleteWarning": "Bạn có chắc muốn xóa biến thể này không?", "pricesPagination": "1 - {{current}} trong số {{total}} giá", "tableItemAvailable": "{{availableCount}} có sẵn", "tableItem_one": "{{availableCount}} có sẵn tại {{locationCount}} vị trí", "tableItem_other": "{{availableCount}} có sẵn tại {{locationCount}} vị trí", "inventory": {"notManaged": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> quản lý", "manageItems": "<PERSON><PERSON><PERSON><PERSON> lý các mục kho hàng", "notManagedDesc": "<PERSON><PERSON> hàng không đư<PERSON><PERSON> quản lý cho biến thể này. Bật <PERSON>Quản lý kho’ để theo dõi kho của biến thể.", "manageKit": "<PERSON><PERSON><PERSON><PERSON> lý bộ thành phần trong kho", "navigateToItem": "<PERSON><PERSON> đ<PERSON>n mục kho hàng", "actions": {"inventoryItems": "<PERSON><PERSON> đ<PERSON>n mục kho hàng", "inventoryKit": "<PERSON><PERSON><PERSON> thị các mục kho hàng"}, "inventoryKit": "<PERSON><PERSON> thành phần trong kho", "inventoryKitHint": "<PERSON><PERSON><PERSON><PERSON> thể này có bao gồm nhiều mục kho hàng không?", "validation": {"itemId": "<PERSON><PERSON> lòng chọn mục kho hàng.", "quantity": "S<PERSON> lượng là bắt buộc. <PERSON><PERSON> lòng nhập một số dương."}, "header": "Tồn kho & <PERSON><PERSON><PERSON><PERSON> lý", "editItemDetails": "Chỉnh sửa chi tiết mục", "manageInventoryLabel": "<PERSON><PERSON><PERSON><PERSON> lý kho", "manageInventoryHint": "<PERSON><PERSON> b<PERSON>, ch<PERSON><PERSON> tôi sẽ tự động thay đổi số lượng kho cho bạn khi có đơn hàng hoặc trả hàng.", "allowBackordersLabel": "Cho phép đặt trước", "allowBackordersHint": "<PERSON><PERSON>, kh<PERSON><PERSON> hàng có thể mua biến thể này ngay cả khi không còn số lượng có sẵn.", "toast": {"levelsBatch": "<PERSON><PERSON><PERSON> tồn kho đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>.", "update": "<PERSON><PERSON><PERSON> kho hàng đã đư<PERSON><PERSON> cập nhật thành công.", "updateLevel": "<PERSON><PERSON><PERSON> tồn kho đã đư<PERSON><PERSON> cập nhật thành công.", "itemsManageSuccess": "<PERSON><PERSON><PERSON> mục kho hàng đã đư<PERSON><PERSON> cập nhật thành công."}}}, "options": {"header": "<PERSON><PERSON><PERSON>", "edit": {"header": "Chỉnh sửa tùy chọn", "successToast": "<PERSON><PERSON><PERSON> ch<PERSON> {{title}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "create": {"header": "<PERSON><PERSON><PERSON> t<PERSON>n", "successToast": "<PERSON><PERSON><PERSON> {{title}} đã đư<PERSON>c tạo thành công."}, "deleteWarning": "Bạn sắp xóa tùy chọn sản phẩm: {{title}}. Hành động này không thể hoàn tác."}, "organization": {"header": "<PERSON><PERSON><PERSON>p", "edit": {"header": "Chỉnh sửa sắp xếp", "toasts": {"success": "<PERSON><PERSON> cập nhật thành công cách sắp xếp của {{title}}."}}}, "stock": {"heading": "<PERSON><PERSON><PERSON><PERSON> lý mức tồn kho và vị trí sản phẩm", "description": "<PERSON><PERSON><PERSON> nhật mức tồn kho cho tất cả các biến thể của sản phẩm.", "loading": "<PERSON><PERSON> chút, vi<PERSON><PERSON> này có thể mất một lúc...", "tooltips": {"alreadyManaged": "<PERSON><PERSON><PERSON> kho hàng này đã có thể chỉnh sửa dưới {{title}}.", "alreadyManagedWithSku": "<PERSON><PERSON><PERSON> kho hàng này đã có thể chỉnh sửa dưới {{title}} ({{sku}})."}}, "shippingProfile": {"header": "<PERSON><PERSON><PERSON> hình vận chuyển", "edit": {"header": "<PERSON><PERSON><PERSON> hình vận chuyển", "toasts": {"success": "<PERSON><PERSON> cập nhật thành công hồ sơ vận chuyển cho {{title}}."}}, "create": {"errors": {"required": "<PERSON><PERSON> sơ vận chuyển là bắt buộc"}}}, "toasts": {"delete": {"success": {"header": "Đ<PERSON> x<PERSON>a sản phẩm", "description": "{{title}} đã đư<PERSON>c xóa thành công."}, "error": {"header": "<PERSON><PERSON><PERSON><PERSON> thể xóa sản phẩm"}}}}, "collections": {"domain": "<PERSON><PERSON> s<PERSON>u tập", "subtitle": "<PERSON><PERSON><PERSON> xếp sản phẩm v<PERSON>o các bộ sưu tập.", "createCollection": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập", "createCollectionHint": "<PERSON><PERSON><PERSON> một bộ sưu tập mới để sắp xếp sản phẩm của bạn.", "createSuccess": "<PERSON><PERSON> sưu tập đã đư<PERSON>c tạo thành công.", "editCollection": "Chỉnh sửa bộ sưu tập", "handleTooltip": "Định danh được sử dụng để tham chiếu bộ sưu tập trên giao diện cửa hàng của bạn. <PERSON><PERSON><PERSON> không chỉ định, định danh sẽ được tạo từ tiêu đề bộ sưu tập.", "deleteWarning": "<PERSON>ạn sắp xóa bộ sưu tập {{title}}. <PERSON>ành động này không thể hoàn tác.", "removeSingleProductWarning": "Bạn sắp x<PERSON>a sản phẩm {{title}} khỏi bộ sưu tập. Hành động này không thể hoàn tác.", "removeProductsWarning_one": "Bạn sắp xóa {{count}} sản phẩm khỏi bộ sưu tập. Hành động này không thể hoàn tác.", "removeProductsWarning_other": "Bạn sắp xóa {{count}} sản phẩm khỏi bộ sưu tập. Hành động này không thể hoàn tác.", "products": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong bộ sưu tập."}, "add": {"successToast_one": "<PERSON><PERSON><PERSON> phẩm đã được thêm thành công vào bộ sưu tập.", "successToast_other": "<PERSON><PERSON><PERSON> sản phẩm đã đư<PERSON>c thêm thành công vào bộ sưu tập."}, "remove": {"successToast_one": "<PERSON><PERSON>n phẩm đã được xóa thành công khỏi bộ sưu tập.", "successToast_other": "<PERSON><PERSON><PERSON> sản phẩm đã được xóa thành công khỏi bộ sưu tập."}}}, "categories": {"domain": "<PERSON><PERSON>", "subtitle": "<PERSON>ắ<PERSON> xếp sản phẩm vào các danh mục và quản lý thứ hạng cũng như phân cấp của các danh mục đó.", "create": {"header": "<PERSON><PERSON><PERSON> da<PERSON> mục", "hint": "<PERSON><PERSON><PERSON> một danh mục mới để sắp xếp sản phẩm của bạn.", "tabs": {"details": "<PERSON> ti<PERSON>", "organize": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> thứ hạng"}, "successToast": "<PERSON><PERSON> mục {{name}} đã đư<PERSON>c tạo thành công."}, "edit": {"header": "Chỉnh sửa danh mục", "description": "Chỉnh sửa danh mục để cập nhật thông tin chi tiết.", "successToast": "<PERSON><PERSON> mục đã đư<PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp x<PERSON>a danh mục {{name}}. <PERSON><PERSON>nh động này không thể hoàn tác.", "successToast": "<PERSON><PERSON> mục {{name}} đã được xóa thành công."}, "products": {"add": {"disabledTooltip": "<PERSON><PERSON><PERSON> phẩm này đã có trong danh mục.", "successToast_one": "<PERSON><PERSON> thêm {{count}} sản phẩm vào danh mục.", "successToast_other": "<PERSON><PERSON> thêm {{count}} sản phẩm vào danh mục."}, "remove": {"confirmation_one": "Bạn sắp xóa {{count}} sản phẩm khỏi danh mục. Hành động này không thể hoàn tác.", "confirmation_other": "Bạn sắp xóa {{count}} sản phẩm khỏi danh mục. Hành động này không thể hoàn tác.", "successToast_one": "<PERSON><PERSON> xóa {{count}} sản phẩm khỏi danh mục.", "successToast_other": "<PERSON><PERSON> xóa {{count}} sản phẩm khỏi danh mục."}, "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong danh mục."}}, "organize": {"header": "<PERSON><PERSON><PERSON>p", "action": "Chỉnh s<PERSON>a thứ hạng"}, "fields": {"visibility": {"label": "<PERSON><PERSON><PERSON> thị", "internal": "<PERSON><PERSON><PERSON> bộ", "public": "<PERSON><PERSON><PERSON> khai"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "path": {"label": "Đường dẫn", "tooltip": "Hiển thị toàn bộ đường dẫn của danh mục."}, "children": {"label": "<PERSON><PERSON> m<PERSON> con"}, "new": {"label": "<PERSON><PERSON><PERSON>"}}}, "inventory": {"domain": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý các mục kho hàng của bạn", "reserved": "Đã đặt trước", "available": "<PERSON><PERSON> sẵn", "locationLevels": "<PERSON><PERSON> trí", "associatedVariants": "<PERSON><PERSON><PERSON><PERSON> thể liên quan", "manageLocations": "<PERSON><PERSON><PERSON><PERSON> lý vị trí", "deleteWarning": "Bạn sắp xóa một mục kho hàng. <PERSON>ành động này không thể hoàn tác.", "editItemDetails": "Chỉnh sửa chi tiết mục", "create": {"title": "<PERSON><PERSON><PERSON> m<PERSON> kho hàng", "details": "<PERSON> ti<PERSON>", "availability": "Tình trạng sẵn có", "locations": "<PERSON><PERSON> trí", "attributes": "<PERSON><PERSON><PERSON><PERSON>", "requiresShipping": "<PERSON><PERSON><PERSON> c<PERSON>u vận chuyển", "requiresShippingHint": "<PERSON><PERSON><PERSON> kho hàng này có cần vận chuyển không?", "successToast": "<PERSON><PERSON><PERSON> kho hàng đã đư<PERSON><PERSON> tạo thành công."}, "reservation": {"header": "Đặt trước của {{itemName}}", "editItemDetails": "Chỉnh sửa đặt trước", "lineItemId": "<PERSON> mục hàng", "orderID": "<PERSON> đơn hàng", "description": "<PERSON><PERSON>", "location": "<PERSON><PERSON> trí", "inStockAtLocation": "<PERSON><PERSON>n kho tại vị trí này", "availableAtLocation": "<PERSON><PERSON> sẵn tại vị trí này", "reservedAtLocation": "Đã đặt trước tại vị trí này", "reservedAmount": "Số lượng đặt trước", "create": "Tạo đặt trước", "itemToReserve": "<PERSON><PERSON><PERSON> để đặt trước", "quantityPlaceholder": "Bạn muốn đặt trước bao nhiêu?", "descriptionPlaceholder": "Đ<PERSON>y là loại đặt trước nào?", "successToast": "Đặt trước đã được tạo thành công.", "updateSuccessToast": "Đặt trước đã được cập nhật thành công.", "deleteSuccessToast": "Đặt trước đã được xóa thành công.", "errors": {"noAvaliableQuantity": "<PERSON><PERSON> trí kho không có số lượng sẵn có.", "quantityOutOfRange": "Số lượng tối thiểu là 1 và tối đa là {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "<PERSON><PERSON> lượng tồn kho không thể cập nhật thấp hơn số lượng đã đặt trước là {{quantity}}."}}, "toast": {"updateLocations": "<PERSON><PERSON> trí đã đ<PERSON><PERSON><PERSON> cập nhật thành công.", "updateLevel": "<PERSON><PERSON><PERSON> tồn kho đã đư<PERSON><PERSON> cập nhật thành công.", "updateItem": "<PERSON><PERSON><PERSON> kho hàng đã đư<PERSON><PERSON> cập nhật thành công."}, "stock": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t mức tồn kho", "description": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> mức tồn kho cho các mục kho hàng đã chọn.", "action": "Chỉnh sửa mức tồn kho", "placeholder": "<PERSON><PERSON><PERSON> b<PERSON>", "disablePrompt_one": "Bạn sắp tắt {{count}} mức vị trí. <PERSON><PERSON>nh động này không thể hoàn tác.", "disablePrompt_other": "Bạn sắp tắt {{count}} mức vị trí. <PERSON><PERSON>nh động này không thể hoàn tác.", "disabledToggleTooltip": "Không thể tắt: h<PERSON><PERSON> x<PERSON><PERSON> số lượng đang nhập hoặc đã đặt trước trước khi tắt.", "successToast": "<PERSON><PERSON><PERSON> tồn kho đã đư<PERSON><PERSON> cập nhật thành công."}}, "giftCards": {"domain": "Thẻ quà tặng", "editGiftCard": "Chỉnh sửa thẻ quà tặng", "createGiftCard": "Tạo thẻ quà tặng", "createGiftCardHint": "Tạo thủ công một thẻ quà tặng có thể dùng làm phương thức thanh toán trong cửa hàng của bạn.", "selectRegionFirst": "<PERSON><PERSON><PERSON> ch<PERSON> khu vực trước", "deleteGiftCardWarning": "Bạn sắp xóa thẻ quà tặng {{code}}. Hành động này không thể hoàn tác.", "balanceHigherThanValue": "<PERSON><PERSON> dư không thể cao hơn số tiền ban đầu.", "balanceLowerThanZero": "<PERSON><PERSON> dư không thể âm.", "expiryDateHint": "<PERSON><PERSON><PERSON> quốc gia có luật khác nhau về ngày hết hạn của thẻ quà tặng. H<PERSON>y kiểm tra quy định địa phương trước khi đặt ngày hết hạn.", "regionHint": "Việc thay đổi khu vực của thẻ quà tặng cũng sẽ thay đổi đơn vị tiền tệ, có thể ảnh hưởng đến giá trị tiền tệ của nó.", "enabledHint": "<PERSON><PERSON><PERSON> định xem thẻ quà tặng được bật hay tắt.", "balance": "Số dư", "currentBalance": "Số dư hiện tại", "initialBalance": "Số dư ban đầu", "personalMessage": "Tin nhắn cá nhân", "recipient": "<PERSON><PERSON><PERSON><PERSON>n"}, "customers": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> hàng của bạn sẽ hiển thị ở đây."}, "create": {"header": "<PERSON><PERSON><PERSON> h<PERSON>ng", "hint": "<PERSON><PERSON><PERSON> một khách hàng mới và quản lý thông tin chi tiết của họ.", "successToast": "<PERSON><PERSON><PERSON><PERSON> hàng {{email}} đã đư<PERSON><PERSON> tạo thành công."}, "groups": {"label": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "remove": "Bạn có chắc muốn xóa khách hàng khỏi nhóm khách hàng \"{{name}}\" không?", "removeMany": "Bạn có chắc muốn xóa khách hàng khỏi các nhóm khách hàng sau: {{groups}} không?", "alreadyAddedTooltip": "<PERSON>h<PERSON>ch hàng này đã có trong nhóm khách hàng.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> hàng này không thuộc nhóm nào."}, "add": {"success": "<PERSON><PERSON><PERSON><PERSON> hàng đã đư<PERSON>c thêm vào: {{groups}}.", "list": {"noRecordsMessage": "<PERSON><PERSON> lòng tạo một nhóm khách hàng trước."}}, "removed": {"success": "<PERSON><PERSON><PERSON><PERSON> hàng đã được xóa khỏi: {{groups}}.", "list": {"noRecordsMessage": "<PERSON><PERSON> lòng tạo một nhóm khách hàng trước."}}}, "edit": {"header": "Chỉnh sửa kh<PERSON>ch hàng", "emailDisabledTooltip": "Địa chỉ email không thể thay đổi đối với khách hàng đã đăng ký.", "successToast": "<PERSON><PERSON><PERSON><PERSON> hàng {{email}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"title": "<PERSON><PERSON><PERSON> h<PERSON>ng", "description": "Bạn sắp xóa khách hàng {{email}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON><PERSON> hàng {{email}} đã đư<PERSON>c xóa thành công."}, "fields": {"guest": "<PERSON><PERSON><PERSON><PERSON> vãng lai", "registered": "Đ<PERSON> đăng ký", "groups": "Nhóm"}, "registered": "Đ<PERSON> đăng ký", "guest": "<PERSON><PERSON><PERSON><PERSON> vãng lai", "hasAccount": "<PERSON><PERSON> t<PERSON>", "addresses": {"title": "Địa chỉ", "fields": {"addressName": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chỉ", "address1": "Địa chỉ 1", "address2": "Địa chỉ 2", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "province": "Tỉnh/Thành", "postalCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "company": "<PERSON><PERSON>ng ty", "countryCode": "Mã quốc gia", "provinceCode": "Mã tỉnh/thành"}, "create": {"header": "<PERSON><PERSON><PERSON> địa chỉ", "hint": "Tạo một địa chỉ mới cho khách hàng.", "successToast": "Địa chỉ đã được tạo thành công."}}}, "customerGroups": {"domain": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "subtitle": "Sắp xếp khách hàng vào các nhóm. Các nhóm có thể có khuyến mãi và giá khác nhau.", "list": {"empty": {"heading": "<PERSON><PERSON><PERSON><PERSON> có nhóm khách hàng", "description": "<PERSON><PERSON><PERSON><PERSON> có nhóm khách hàng nào để hiển thị."}, "filtered": {"heading": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "description": "<PERSON><PERSON><PERSON><PERSON> có nhóm khách hàng nào khớp với tiêu chí lọc hiện tại."}}, "create": {"header": "<PERSON><PERSON><PERSON> nhó<PERSON> kh<PERSON>ch hàng", "hint": "T<PERSON>o một nhóm khách hàng mới để phân đoạn khách hàng của bạn.", "successToast": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng {{name}} đã đư<PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh sửa nhóm khách hàng", "successToast": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch hàng {{name}} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"title": "Xóa nhóm khách hàng", "description": "Bạn sắp xóa nhóm khách hàng {{name}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON><PERSON> khách hàng {{name}} đã đư<PERSON><PERSON> xóa thành công."}, "customers": {"alreadyAddedTooltip": "<PERSON><PERSON><PERSON><PERSON> hàng này đã đư<PERSON><PERSON> thêm vào nhóm.", "add": {"successToast_one": "<PERSON><PERSON><PERSON><PERSON> hàng đã đư<PERSON><PERSON> thêm thành công vào nhóm.", "successToast_other": "<PERSON><PERSON><PERSON> khách hàng đã đ<PERSON><PERSON><PERSON> thêm thành công vào nhóm.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> t<PERSON>o một khách hàng trước."}}, "remove": {"title_one": "<PERSON><PERSON><PERSON> h<PERSON>ng", "title_other": "<PERSON><PERSON><PERSON> h<PERSON>ng", "description_one": "Bạn sắp xóa {{count}} khách hàng khỏi nhóm khách hàng. Hành động này không thể hoàn tác.", "description_other": "Bạn sắp xóa {{count}} khách hàng khỏi nhóm khách hàng. Hành động này không thể hoàn tác."}, "list": {"noRecordsMessage": "Nhóm này chưa có khách hàng."}}}, "orders": {"domain": "<PERSON><PERSON><PERSON> hàng", "claim": "<PERSON><PERSON><PERSON><PERSON>", "exchange": "<PERSON><PERSON><PERSON> hàng", "return": "<PERSON><PERSON><PERSON> h<PERSON>", "cancelWarning": "<PERSON><PERSON>n sắp hủy đơn hàng {{id}}. Hành động này không thể hoàn tác.", "orderCanceled": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> hủy thành công", "onDateFromSalesChannel": "{{date}} từ {{salesChannel}}", "list": {"noRecordsMessage": "Đơn hàng của bạn sẽ hiển thị ở đây."}, "status": {"not_paid": "<PERSON><PERSON><PERSON> to<PERSON>", "pending": "<PERSON><PERSON> chờ xử lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "draft": "Nháp", "archived": "<PERSON><PERSON> lưu trữ", "canceled": "<PERSON><PERSON> hủy", "requires_action": "<PERSON><PERSON><PERSON> c<PERSON>u hành động"}, "summary": {"requestReturn": "<PERSON><PERSON><PERSON> c<PERSON>u tr<PERSON> hàng", "allocateItems": "<PERSON><PERSON> bổ mục hàng", "editOrder": "Chỉnh sửa đơn hàng", "editOrderContinue": "<PERSON><PERSON><PERSON><PERSON> tục chỉnh sửa đơn hàng", "inventoryKit": "<PERSON><PERSON> gồm {{count}} mục kho hàng", "itemTotal": "<PERSON><PERSON><PERSON> mụ<PERSON> hàng", "shippingTotal": "<PERSON>ổng phí vận chuyển", "discountTotal": "Tổng giảm giá", "taxTotalIncl": "<PERSON><PERSON><PERSON> thu<PERSON> (đ<PERSON> bao g<PERSON>)", "itemSubtotal": "<PERSON><PERSON><PERSON> t<PERSON> mục hàng", "shippingSubtotal": "<PERSON><PERSON><PERSON> t<PERSON>h phí vận chuyển", "discountSubtotal": "<PERSON><PERSON><PERSON> t<PERSON> gi<PERSON>m giá", "taxTotal": "<PERSON><PERSON><PERSON> thuế"}, "transfer": {"title": "<PERSON>y<PERSON><PERSON> quyền sở hữu", "requestSuccess": "<PERSON><PERSON><PERSON> cầu chuyển đơn hàng đã đư<PERSON><PERSON> gửi đến: {{email}}.", "currentOwner": "Ch<PERSON> sở hữu hiện tại", "newOwner": "Chủ sở hữu mới", "currentOwnerDescription": "<PERSON><PERSON><PERSON><PERSON> hàng hiện đang liên quan đến đơn hàng này.", "newOwnerDescription": "<PERSON><PERSON><PERSON><PERSON> hàng mà đơn hàng này sẽ được chuyển đến."}, "payment": {"title": "<PERSON><PERSON> toán", "isReadyToBeCaptured": "<PERSON><PERSON> <0/> đã sẵn sàng để ghi nhận.", "totalPaidByCustomer": "Tổng số tiền khách hàng đã thanh toán", "capture": "<PERSON><PERSON> nh<PERSON>n thanh toán", "capture_short": "<PERSON><PERSON>n", "refund": "<PERSON><PERSON><PERSON> ti<PERSON>n", "markAsPaid": "<PERSON><PERSON><PERSON> dấu đã thanh toán", "statusLabel": "<PERSON>r<PERSON><PERSON> thái thanh toán", "statusTitle": "Trạng T<PERSON>", "status": {"notPaid": "<PERSON><PERSON><PERSON> to<PERSON>", "authorized": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "partiallyAuthorized": "<PERSON><PERSON><PERSON>n một phần", "awaiting": "<PERSON><PERSON> chờ", "captured": "<PERSON><PERSON> ghi nhận", "partiallyRefunded": "<PERSON><PERSON><PERSON> tiền một phần", "partiallyCaptured": "<PERSON><PERSON> <PERSON>h<PERSON>n một phần", "refunded": "<PERSON><PERSON> hoàn tiền", "canceled": "<PERSON><PERSON> hủy", "requiresAction": "<PERSON><PERSON><PERSON> c<PERSON>u hành động"}, "capturePayment": "<PERSON><PERSON> toán {{amount}} sẽ được ghi nhận.", "capturePaymentSuccess": "<PERSON>h toán {{amount}} đã đư<PERSON>c ghi nhận thành công", "markAsPaidPayment": "<PERSON>h toán {{amount}} sẽ được đánh dấu là đã thanh toán.", "markAsPaidPaymentSuccess": "<PERSON>h toán {{amount}} đã được đánh dấu thành công là đã thanh toán", "createRefund": "<PERSON><PERSON><PERSON> ho<PERSON>n tiền", "refundPaymentSuccess": "<PERSON><PERSON>n tiền số tiền {{amount}} thành công", "createRefundWrongQuantity": "S<PERSON> lượng phải là một số từ 1 đến {{number}}", "refundAmount": "<PERSON><PERSON><PERSON> tiền {{amount}}", "paymentLink": "<PERSON>o chép liên kết thanh toán cho {{amount}}", "selectPaymentToRefund": "<PERSON><PERSON><PERSON> thanh toán để hoàn tiền"}, "edits": {"title": "Chỉnh sửa đơn hàng", "confirm": "<PERSON><PERSON><PERSON> n<PERSON>n chỉnh sửa", "confirmText": "Bạn sắp xác nhận một chỉnh sửa đơn hàng. Hành động này không thể hoàn tác.", "cancel": "<PERSON><PERSON>y chỉnh sửa", "currentItems": "<PERSON><PERSON><PERSON> hàng hiện tại", "currentItemsDescription": "<PERSON>i<PERSON>u chỉnh số lượng mục hàng hoặc xóa bỏ.", "addItemsDescription": "<PERSON><PERSON><PERSON> có thể thêm các mục hàng mới vào đơn hàng.", "addItems": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> hàng", "amountPaid": "Số tiền đã thanh toán", "newTotal": "Tổng mới", "differenceDue": "<PERSON><PERSON><PERSON> l<PERSON>ch phải trả", "create": "Chỉnh sửa đơn hàng", "currentTotal": "<PERSON><PERSON><PERSON> hiện tại", "noteHint": "<PERSON><PERSON><PERSON><PERSON> ghi chú nội bộ cho chỉnh sửa", "cancelSuccessToast": "Chỉnh sửa đơn hàng đã bị hủy", "createSuccessToast": "<PERSON><PERSON><PERSON> cầu chỉnh sửa đơn hàng đã đư<PERSON><PERSON> tạo", "activeChangeError": "<PERSON><PERSON> có một thay đổi đang hoạt động trên đơn hàng (tr<PERSON> hàng, khi<PERSON><PERSON> n<PERSON>, đổ<PERSON> hàng, v.v.). <PERSON><PERSON> lòng hoàn tất hoặc hủy thay đổi trước khi chỉnh sửa đơn hàng.", "panel": {"title": "<PERSON><PERSON><PERSON> cầu chỉnh sửa đơn hàng", "titlePending": "Chỉnh sửa đơn hàng đang chờ"}, "toast": {"canceledSuccessfully": "Chỉnh sửa đơn hàng đã đư<PERSON><PERSON> hủy", "confirmedSuccessfully": "Chỉnh sửa đơn hàng đã được xác nhận"}, "validation": {"quantityLowerThanFulfillment": "<PERSON><PERSON><PERSON>ng thể đặt số lượng nhỏ hơn hoặc bằng số lượng đã hoàn tất"}}, "edit": {"email": {"title": "Chỉnh sửa email", "requestSuccess": "Email đơn hàng đã đư<PERSON><PERSON> cập nhật thành {{email}}."}, "shippingAddress": {"title": "Chỉnh sửa địa chỉ giao hàng", "requestSuccess": "Địa chỉ giao hàng của đơn hàng đã đư<PERSON><PERSON> cập nhật."}, "billingAddress": {"title": "Chỉnh sửa địa chỉ thanh toán", "requestSuccess": "<PERSON><PERSON><PERSON> chỉ thanh toán của đơn hàng đã đư<PERSON><PERSON> cập nh<PERSON>t."}}, "returns": {"create": "<PERSON><PERSON><PERSON> y<PERSON>u cầu tr<PERSON> hàng", "confirm": "<PERSON><PERSON><PERSON>n tr<PERSON> hàng", "confirmText": "Bạn sắp xác nhận một yêu cầu trả hàng. Hành động này không thể hoàn tác.", "inbound": "<PERSON><PERSON><PERSON> tr<PERSON> về", "outbound": "<PERSON><PERSON><PERSON> g<PERSON>i đi", "sendNotification": "<PERSON><PERSON><PERSON> thông báo", "sendNotificationHint": "<PERSON><PERSON><PERSON><PERSON> báo cho khách hàng về việc trả hàng.", "returnTotal": "<PERSON><PERSON><PERSON> tr<PERSON> hàng", "inboundTotal": "<PERSON><PERSON><PERSON> hàng trả về", "refundAmount": "<PERSON><PERSON> tiền hoàn lại", "outstandingAmount": "Số tiền còn lại", "reason": "Lý do", "reasonHint": "<PERSON><PERSON>n lý do khách hàng muốn trả hàng.", "note": "<PERSON><PERSON><PERSON>", "noInventoryLevel": "<PERSON><PERSON><PERSON> c<PERSON> mức tồn kho", "noInventoryLevelDesc": "<PERSON>ị trí bạn chọn hiện không có thông tin tồn kho cho các mục đã chọn. Bạn vẫn có thể tạo yêu cầu trả hàng, nhưng không thể nhận hàng cho đến khi mức tồn kho được thiết lập cho vị trí này.", "noteHint": "<PERSON>ạn có thể nhập tự do nếu muốn ghi chú điều gì đó cụ thể.", "location": "<PERSON><PERSON> trí", "locationHint": "<PERSON><PERSON><PERSON> vị trí bạn muốn trả các mục hàng về.", "inboundShipping": "<PERSON><PERSON><PERSON> chuyển trả hàng", "inboundShippingHint": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức bạn muốn sử dụng.", "returnableQuantityLabel": "S<PERSON> lư<PERSON>ng có thể trả", "refundableAmountLabel": "<PERSON><PERSON> tiền có thể hoàn lại", "returnRequestedInfo": "<PERSON><PERSON><PERSON> c<PERSON>u trả {{requestedItemsCount}} mụ<PERSON> hàng", "returnReceivedInfo": "<PERSON><PERSON> nhận trả {{requestedItemsCount}} mụ<PERSON> hàng", "itemReceived": "<PERSON><PERSON><PERSON> hàng đã nhận", "returnRequested": "<PERSON><PERSON> yêu cầu tr<PERSON> hàng", "damagedItemReceived": "<PERSON><PERSON><PERSON> hàng hư hỏng đã nhận", "damagedItemsReturned": "<PERSON><PERSON> trả lại {{quantity}} mục hàng hư hỏng", "activeChangeError": "<PERSON><PERSON> một thay đổi đang hoạt động trên đơn hàng này. <PERSON><PERSON> lòng hoàn tất hoặc hủy thay đổi trước.", "cancel": {"title": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u tr<PERSON> hàng", "description": "Bạn có chắc muốn hủy yêu cầu trả hàng không?"}, "placeholders": {"noReturnShippingOptions": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tùy chọn vận chuyển trả hàng", "hint": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn vận chuyển trả hàng nào được tạo cho vị trí này. Bạn có thể tạo một tùy chọn tại <LinkComponent>Vị trí & <PERSON><PERSON><PERSON> chuyển</LinkComponent>."}, "outboundShippingOptions": {"title": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tùy chọn vận chuyển gửi đi", "hint": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn vận chuyển gửi đi nào được tạo cho vị trí này. Bạn có thể tạo một tùy chọn tại <LinkComponent>Vị trí & <PERSON><PERSON><PERSON> chuyển</LinkComponent>."}}, "receive": {"action": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> hàng", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "<PERSON><PERSON><PERSON><PERSON> kho lại tất cả mục hàng", "itemsLabel": "<PERSON><PERSON><PERSON> hàng đã nhận", "title": "<PERSON><PERSON><PERSON><PERSON> mục hàng cho #{{returnId}}", "sendNotificationHint": "<PERSON><PERSON><PERSON><PERSON> báo cho khách hàng về việc nhận trả hàng.", "inventoryWarning": "<PERSON><PERSON><PERSON> ý rằng chúng tôi sẽ tự động điều chỉnh mức tồn kho dựa trên thông tin bạn nhập ở trên.", "writeOffInputLabel": "<PERSON><PERSON> bao nhiêu mục hàng bị hư hỏng?", "toast": {"success": "<PERSON><PERSON><PERSON> hàng đã được nhận thành công.", "errorLargeValue": "Số lượng vư<PERSON><PERSON> quá số lượng mục hàng đã yêu cầu.", "errorNegativeValue": "Số lượng không thể là số âm.", "errorLargeDamagedValue": "<PERSON>ố lượng mục hư hỏng + số lượng không hư hỏng đã nhận vượt quá tổng số lượng mục hàng trong yêu cầu trả. Vui lòng giảm số lượng mục không hư hỏng."}}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> cầu trả hàng đã đư<PERSON><PERSON> hủy thành công", "confirmedSuccessfully": "<PERSON><PERSON><PERSON> cầu trả hàng đã đư<PERSON>c xác nhận thành công"}, "panel": {"title": "Đã khởi tạo trả hàng", "description": "<PERSON><PERSON> một yêu cầu trả hàng đang mở cần được hoàn tất"}}, "claims": {"create": "<PERSON><PERSON><PERSON> n<PERSON>", "confirm": "<PERSON><PERSON><PERSON> k<PERSON> n<PERSON>i", "confirmText": "Bạn sắp xác nhận một khiếu nại. <PERSON>ành động này không thể hoàn tác.", "manage": "<PERSON><PERSON><PERSON><PERSON> lý khi<PERSON>u n<PERSON>i", "outbound": "<PERSON><PERSON><PERSON> g<PERSON>i đi", "outboundItemAdded": "<PERSON><PERSON> thêm {{itemsCount}} mục qua khiếu nại", "outboundTotal": "<PERSON><PERSON><PERSON> hàng gửi đi", "outboundShipping": "<PERSON><PERSON><PERSON> chuyển gửi đi", "outboundShippingHint": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức bạn muốn sử dụng.", "refundAmount": "<PERSON><PERSON><PERSON> l<PERSON>", "activeChangeError": "<PERSON><PERSON> một thay đổi đang hoạt động trên đơn hàng này. <PERSON><PERSON> lòng hoàn tất hoặc hủy thay đổi trước đó.", "actions": {"cancelClaim": {"successToast": "<PERSON><PERSON><PERSON><PERSON> nại đã đư<PERSON><PERSON> hủy thành công."}}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc muốn hủy khiếu nại không?"}, "tooltips": {"onlyReturnShippingOptions": "<PERSON><PERSON> s<PERSON>ch này chỉ bao gồm các tùy chọn vận chuyển trả hàng."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON><PERSON> nại đã đư<PERSON><PERSON> hủy thành công", "confirmedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> nại đã được xác nhận thành công"}, "panel": {"title": "Đã khởi tạo khi<PERSON>u nại", "description": "<PERSON><PERSON> một yêu cầu khiếu nại đang mở cần được hoàn tất"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON> đ<PERSON> hàng", "manage": "<PERSON><PERSON><PERSON><PERSON> lý đổi hàng", "confirm": "<PERSON><PERSON><PERSON> nh<PERSON>n đổi hàng", "confirmText": "Bạn sắp xác nhận một đổi hàng. <PERSON><PERSON>nh động này không thể hoàn tác.", "outbound": "<PERSON><PERSON><PERSON> g<PERSON>i đi", "outboundItemAdded": "<PERSON><PERSON> thêm {{itemsCount}} mục qua đổi hàng", "outboundTotal": "<PERSON><PERSON><PERSON> hàng gửi đi", "outboundShipping": "<PERSON><PERSON><PERSON> chuyển gửi đi", "outboundShippingHint": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức bạn muốn sử dụng.", "refundAmount": "<PERSON><PERSON><PERSON> l<PERSON>", "activeChangeError": "<PERSON><PERSON> một thay đổi đang hoạt động trên đơn hàng này. <PERSON><PERSON> lòng hoàn tất hoặc hủy thay đổi trước đó.", "actions": {"cancelExchange": {"successToast": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> hủy thành công."}}, "cancel": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>i hàng", "description": "Bạn có chắc muốn hủy đổi hàng không?"}, "tooltips": {"onlyReturnShippingOptions": "<PERSON><PERSON> s<PERSON>ch này chỉ bao gồm các tùy chọn vận chuyển trả hàng."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> hủy thành công", "confirmedSuccessfully": "<PERSON><PERSON><PERSON> hàng đã đư<PERSON><PERSON> xác nhận thành công"}, "panel": {"title": "Đ<PERSON> khởi tạo đổi hàng", "description": "<PERSON><PERSON> một yêu cầu đổi hàng đang mở cần được hoàn tất"}}, "reservations": {"allocatedLabel": "<PERSON><PERSON> phân bổ", "notAllocatedLabel": "<PERSON><PERSON><PERSON> ph<PERSON> bổ"}, "allocateItems": {"action": "<PERSON><PERSON> bổ mục hàng", "title": "<PERSON><PERSON> bổ các mục trong đơn hàng", "locationDescription": "<PERSON><PERSON><PERSON> vị trí bạn muốn phân bổ từ đó.", "itemsToAllocate": "<PERSON><PERSON><PERSON> hàng c<PERSON>n phân bổ", "itemsToAllocateDesc": "<PERSON><PERSON>n số lượng mục hàng bạn muốn phân bổ", "search": "<PERSON><PERSON><PERSON> ki<PERSON>m mụ<PERSON> hàng", "consistsOf": "<PERSON><PERSON> gồm {{num}} mục kho hàng", "requires": "<PERSON><PERSON><PERSON> cầu {{num}} cho mỗi biến thể", "toast": {"created": "<PERSON><PERSON><PERSON> hàng đã đư<PERSON><PERSON> phân bổ thành công"}, "error": {"quantityNotAllocated": "Vẫn còn các mục hàng chưa được phân bổ."}}, "shipment": {"title": "<PERSON><PERSON><PERSON> dấu giao hàng đã gửi", "trackingNumber": "Số theo dõi", "addTracking": "<PERSON><PERSON><PERSON><PERSON> số theo dõi", "sendNotification": "<PERSON><PERSON><PERSON> thông báo", "sendNotificationHint": "<PERSON><PERSON><PERSON><PERSON> báo cho khách hàng về lô hàng này.", "toastCreated": "<PERSON><PERSON> hàng đã đư<PERSON><PERSON> tạo thành công."}, "fulfillment": {"cancelWarning": "Bạn sắp hủy một quá trình giao hàng. Hành động này không thể hoàn tác.", "markAsDeliveredWarning": "Bạn sắp đánh dấu giao hàng là đã giao. Hành động này không thể hoàn tác.", "differentOptionSelected": "Tùy chọn vận chuyển đã chọn khác với tùy chọn khách hàng chọn.", "disabledItemTooltip": "<PERSON><PERSON><PERSON> chọn vận chuyển bạn chọn không hỗ trợ giao hàng cho mục này", "unfulfilledItems": "<PERSON><PERSON><PERSON> hàng ch<PERSON>a đ<PERSON><PERSON><PERSON> giao", "statusLabel": "<PERSON><PERSON><PERSON><PERSON> thái giao hàng", "statusTitle": "Trạng Thái G<PERSON>o <PERSON>", "fulfillItems": "<PERSON>ử lý giao hàng cho mục hàng", "awaitingFulfillmentBadge": "<PERSON><PERSON> chờ giao hàng", "requiresShipping": "<PERSON><PERSON><PERSON> c<PERSON>u vận chuyển", "number": "Giao hàng #{{number}}", "itemsToFulfill": "<PERSON><PERSON><PERSON> h<PERSON>ng c<PERSON>n giao", "create": "<PERSON><PERSON><PERSON> lý giao hàng", "available": "<PERSON><PERSON> sẵn", "inStock": "Trong kho", "markAsShipped": "<PERSON><PERSON><PERSON> dấu đã gửi", "markAsPickedUp": "<PERSON><PERSON><PERSON> dấu đã nhận tại chỗ", "markAsDelivered": "<PERSON><PERSON><PERSON> dấu đã giao", "itemsToFulfillDesc": "<PERSON><PERSON><PERSON> mục hàng và số lượng để giao", "locationDescription": "<PERSON><PERSON><PERSON> vị trí bạn muốn giao hàng từ đó.", "sendNotificationHint": "<PERSON>h<PERSON><PERSON> báo cho khách hàng về quá trình giao hàng đã tạo.", "methodDescription": "<PERSON><PERSON>n phương thức vận chuyển khác với phương thức khách hàng đã chọn", "error": {"wrongQuantity": "Chỉ có một mục hàng sẵn sàng để giao", "wrongQuantity_other": "S<PERSON> lượng phải là một số từ 1 đến {{number}}", "noItems": "<PERSON><PERSON><PERSON><PERSON> có mục hàng để giao.", "noShippingOption": "<PERSON><PERSON><PERSON> c<PERSON>u chọn ph<PERSON><PERSON><PERSON> thức vận chuyển", "noLocation": "<PERSON><PERSON><PERSON> c<PERSON>u chọn vị trí"}, "status": {"notFulfilled": "Chưa giao", "partiallyFulfilled": "<PERSON><PERSON><PERSON>n", "fulfilled": "Đã xử lý giao hàng", "partiallyShipped": "<PERSON><PERSON> gửi một phần", "shipped": "Đ<PERSON> gửi", "delivered": "Đã giao", "partiallyDelivered": "Đ<PERSON> giao một phần", "partiallyReturned": "<PERSON><PERSON><PERSON> m<PERSON>t ph<PERSON>n", "returned": "Đã trả", "canceled": "<PERSON><PERSON> hủy", "requiresAction": "<PERSON><PERSON><PERSON> c<PERSON>u hành động"}, "toast": {"created": "<PERSON>ử lý giao hàng đã đư<PERSON><PERSON> tạo thành công", "canceled": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> hủy thành công", "fulfillmentShipped": "<PERSON><PERSON><PERSON><PERSON> thể hủy giao hàng đã đư<PERSON><PERSON> gửi đi", "fulfillmentDelivered": "Giao hàng đã đư<PERSON><PERSON> đánh dấu là đã giao thành công", "fulfillmentPickedUp": "<PERSON>iao hàng đã được đánh dấu là đã nhận tại chỗ thành công"}, "trackingLabel": "<PERSON>", "shippingFromLabel": "<PERSON><PERSON><PERSON> từ", "itemsLabel": "<PERSON><PERSON><PERSON>"}, "refund": {"title": "<PERSON><PERSON><PERSON> ho<PERSON>n tiền", "sendNotificationHint": "<PERSON><PERSON><PERSON><PERSON> báo cho khách hàng về hoàn tiền đã tạo.", "systemPayment": "<PERSON><PERSON> <PERSON><PERSON> hệ thống", "systemPaymentDesc": "Một hoặc nhiều khoản thanh toán của bạn là thanh toán hệ thống. Lưu ý rằng việc ghi nhận và hoàn tiền không được Medusa xử lý cho các khoản thanh toán này.", "error": {"amountToLarge": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tiền nhiều hơn số tiền đơn hàng ban đầu.", "amountNegative": "S<PERSON> tiền hoàn lại phải là một số dương.", "reasonRequired": "<PERSON><PERSON> lòng chọn lý do hoàn tiền."}}, "customer": {"contactLabel": "<PERSON><PERSON><PERSON>", "editEmail": "Chỉnh sửa email", "transferOwnership": "<PERSON>y<PERSON><PERSON> quyền sở hữu", "editBillingAddress": "Chỉnh sửa địa chỉ thanh toán", "editShippingAddress": "Chỉnh sửa địa chỉ giao hàng"}, "activity": {"header": "<PERSON><PERSON><PERSON> đ<PERSON>", "showMoreActivities_one": "<PERSON><PERSON><PERSON> thị thêm {{count}} hoạt động", "showMoreActivities_other": "<PERSON><PERSON><PERSON> thị thêm {{count}} hoạt động", "comment": {"label": "<PERSON><PERSON><PERSON> lu<PERSON>", "placeholder": "<PERSON><PERSON> lại bình luận", "addButtonText": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "deleteButtonText": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n"}, "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "events": {"common": {"toReturn": "<PERSON><PERSON><PERSON> tr<PERSON> lại", "toSend": "<PERSON><PERSON><PERSON> g<PERSON>i đi"}, "placed": {"title": "Đ<PERSON>n hàng đã đặt", "fromSalesChannel": "từ {{salesChannel}}"}, "canceled": {"title": "Đ<PERSON><PERSON> hàng đã hủy"}, "payment": {"awaiting": "<PERSON><PERSON> chờ thanh toán", "captured": "<PERSON><PERSON> to<PERSON> đã ghi nhận", "canceled": "<PERSON><PERSON> to<PERSON> đã hủy", "refunded": "<PERSON><PERSON> toán đã hoàn tiền"}, "fulfillment": {"created": "<PERSON><PERSON><PERSON> hàng đã đư<PERSON><PERSON> xử lý giao", "canceled": "<PERSON><PERSON><PERSON> hàng đã hủy", "shipped": "<PERSON><PERSON><PERSON> hàng đã gửi", "delivered": "<PERSON><PERSON><PERSON> hàng đã giao", "items_one": "{{count}} mục", "items_other": "{{count}} mục"}, "return": {"created": "<PERSON><PERSON><PERSON> c<PERSON>u trả hàng #{{returnId}} đã tạo", "canceled": "Tr<PERSON> hàng #{{returnId}} đ<PERSON> hủy", "received": "T<PERSON><PERSON> hàng #{{returnId}} đ<PERSON> nhận", "items_one": "<PERSON><PERSON> trả {{count}} mục", "items_other": "<PERSON><PERSON> trả {{count}} mục"}, "note": {"comment": "<PERSON><PERSON><PERSON> lu<PERSON>", "byLine": "bởi {{author}}"}, "claim": {"created": "<PERSON><PERSON><PERSON> c<PERSON>u khiếu nại #{{claimId}} đã tạo", "canceled": "<PERSON><PERSON><PERSON><PERSON> nại #{{claimId}} đã hủy", "itemsInbound": "{{count}} m<PERSON><PERSON> c<PERSON>n tr<PERSON> lại", "itemsOutbound": "{{count}} mụ<PERSON> cần g<PERSON>i đi"}, "exchange": {"created": "<PERSON><PERSON><PERSON> cầu đổi hàng #{{exchangeId}} đã tạo", "canceled": "<PERSON><PERSON><PERSON> hàng #{{exchangeId}} đ<PERSON> hủy", "itemsInbound": "{{count}} m<PERSON><PERSON> c<PERSON>n tr<PERSON> lại", "itemsOutbound": "{{count}} mụ<PERSON> cần g<PERSON>i đi"}, "edit": {"requested": "<PERSON><PERSON><PERSON> cầu chỉnh sửa đơn hàng #{{editId}} đã tạo", "confirmed": "Chỉnh sửa đơn hàng #{{editId}} đã xác nhận"}, "transfer": {"requested": "<PERSON><PERSON><PERSON> cầu chuyển đơn hàng #{{transferId}} đã tạo", "confirmed": "<PERSON>yển đơn hàng #{{transferId}} đã xác nhận", "declined": "<PERSON>yển đơn hàng #{{transferId}} đã bị từ chối"}, "update_order": {"shipping_address": "Địa chỉ giao hàng đã cập nhật", "billing_address": "<PERSON><PERSON><PERSON> chỉ thanh toán đã cập nhật", "email": "<PERSON><PERSON> cập nh<PERSON>t"}}}, "fields": {"displayId": "<PERSON> hiển thị", "refundableAmount": "<PERSON><PERSON> tiền có thể hoàn lại", "returnableQuantity": "Số lượng có thể trả lại"}}, "draftOrders": {"domain": "<PERSON><PERSON><PERSON> hàng <PERSON>", "deleteWarning": "Bạn sắp xóa đơn hàng nháp {{id}}. Hành động này không thể hoàn tác.", "paymentLinkLabel": "<PERSON><PERSON><PERSON> kết <PERSON>h toán", "cartIdLabel": "ID giỏ hàng", "markAsPaid": {"label": "<PERSON><PERSON><PERSON> dấu đã thanh toán", "warningTitle": "<PERSON><PERSON><PERSON> dấu đã thanh toán", "warningDescription": "Bạn sắp đánh dấu đơn hàng nháp là đã thanh toán. Hành động này không thể hoàn tác, và việc thu tiền sau đó sẽ không thể thực hiện."}, "status": {"open": "<PERSON><PERSON> mở", "completed": "<PERSON><PERSON> hoàn tất"}, "create": {"createDraftOrder": "<PERSON><PERSON><PERSON> đơn hàng nh<PERSON>p", "createDraftOrderHint": "T<PERSON><PERSON> một đơn hàng nháp mới để admin quản lý chi tiết trước khi chính thức đặt hàng.", "chooseRegionHint": "<PERSON><PERSON><PERSON> khu vực", "existingItemsLabel": "<PERSON><PERSON><PERSON> hàng hi<PERSON>n có", "existingItemsHint": "<PERSON><PERSON><PERSON><PERSON> các sản phẩm hiện có vào đơn hàng nh<PERSON>p.", "customItemsLabel": "<PERSON><PERSON><PERSON> hàng tùy chỉnh", "customItemsHint": "<PERSON><PERSON><PERSON><PERSON> các mục hàng tùy chỉnh vào đơn hàng nháp.", "addExistingItemsAction": "<PERSON><PERSON><PERSON><PERSON> mục hàng hiện có", "addCustomItemAction": "<PERSON><PERSON><PERSON><PERSON> mục hàng tùy chỉnh", "noCustomItemsAddedLabel": "<PERSON><PERSON><PERSON> có mục hàng tùy chỉnh nào được thêm", "noExistingItemsAddedLabel": "<PERSON><PERSON><PERSON> có mục hàng hiện có nào đ<PERSON><PERSON><PERSON> thêm", "chooseRegionTooltip": "<PERSON><PERSON><PERSON> ch<PERSON> khu vực trước", "useExistingCustomerLabel": "S<PERSON> dụng kh<PERSON>ch hàng hiện có", "addShippingMethodsAction": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức vận chuyển", "unitPriceOverrideLabel": "<PERSON><PERSON> đè giá đơn vị", "shippingOptionLabel": "<PERSON><PERSON><PERSON> chọn vận chuyển", "shippingOptionHint": "<PERSON><PERSON><PERSON> tùy chọn vận chuyển cho đơn hàng nh<PERSON>p.", "shippingPriceOverrideLabel": "<PERSON><PERSON> đè giá vận chuyển", "shippingPriceOverrideHint": "<PERSON><PERSON> đè giá vận chuyển cho đơn hàng nh<PERSON>p.", "sendNotificationLabel": "<PERSON><PERSON><PERSON> thông báo", "sendNotificationHint": "<PERSON><PERSON><PERSON> thông báo cho khách hàng khi đơn hàng nháp đ<PERSON><PERSON><PERSON> tạo."}, "validation": {"requiredEmailOrCustomer": "Email hoặc khách hàng là bắt buộc.", "requiredItems": "<PERSON><PERSON><PERSON> ít nhất một mục hàng.", "invalidEmail": "Email phải là địa chỉ email hợp lệ."}}, "stockLocations": {"domain": "<PERSON><PERSON> t<PERSON> & <PERSON><PERSON><PERSON> ch<PERSON>n", "list": {"description": "<PERSON><PERSON><PERSON><PERSON> lý các vị trí kho hàng và tùy chọn vận chuyển của cửa hàng bạn."}, "create": {"header": "Tạo vị trí kho hàng", "hint": "<PERSON><PERSON> trí kho hàng là nơi thực tế lưu trữ và gửi đi các sản phẩm.", "successToast": "Vị trí {{name}} đã đư<PERSON>c tạo thành công."}, "edit": {"header": "Chỉnh sửa vị trí kho hàng", "viewInventory": "<PERSON><PERSON> kho h<PERSON>ng", "successToast": "V<PERSON> trí {{name}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa vị trí kho hàng {{name}}. Hành động này không thể hoàn tác."}, "fulfillmentProviders": {"header": "<PERSON><PERSON><PERSON> cung cấp giao hàng", "shippingOptionsTooltip": "<PERSON>h sách này chỉ bao gồm các nhà cung cấp đã bật cho vị trí này. Thêm họ vào vị trí nếu danh sách bị tắt.", "label": "<PERSON><PERSON><PERSON> cung cấp giao hàng đã kết nối", "connectedTo": "<PERSON><PERSON> kết nối với {{count}} trong số {{total}} nhà cung cấp giao hàng", "noProviders": "V<PERSON> trí kho hàng này chưa kết nối với bất kỳ nhà cung cấp giao hàng nào.", "action": "<PERSON><PERSON><PERSON> n<PERSON>i nhà cung cấp", "successToast": "<PERSON><PERSON>à cung cấp giao hàng cho vị trí kho hàng đã đư<PERSON><PERSON> cập nhật thành công."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON><PERSON> tại chỗ"}, "shipping": {"header": "<PERSON><PERSON><PERSON> ch<PERSON>"}, "disable": {"confirmation": "Bạn có chắc muốn tắt {{name}} không? Thao tác này sẽ xóa tất cả vùng dịch vụ và tùy chọn vận chuyển liên quan, và không thể hoàn tác.", "pickup": "<PERSON><PERSON><PERSON><PERSON> tại chỗ đã được tắt thành công.", "shipping": "<PERSON><PERSON><PERSON> chuyển đã đư<PERSON>c tắt thành công."}, "enable": {"pickup": "<PERSON><PERSON><PERSON><PERSON> tại chỗ đã được bật thành công.", "shipping": "<PERSON><PERSON>n chuyển đã đư<PERSON><PERSON> bật thành công."}}, "sidebar": {"header": "<PERSON><PERSON><PERSON> hình vận chuyển", "shippingProfiles": {"label": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "description": "<PERSON><PERSON><PERSON><PERSON> các sản phẩm theo yêu cầu vận chuyển"}}, "salesChannels": {"header": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "hint": "<PERSON><PERSON><PERSON><PERSON> lý các kênh bán hàng đư<PERSON><PERSON> kết nối với vị trí này.", "label": "<PERSON><PERSON><PERSON> bán hàng đã kết nối", "connectedTo": "<PERSON><PERSON> kết nối với {{count}} trong số {{total}} kê<PERSON> b<PERSON> hàng", "noChannels": "<PERSON><PERSON> trí này chưa kết nối với bất kỳ kênh bán hàng nào.", "action": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> kênh bán hàng", "successToast": "<PERSON><PERSON><PERSON> bán hàng đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "pickupOptions": {"edit": {"header": "Chỉnh sửa tùy chọn nhận tại chỗ"}}, "shippingOptions": {"create": {"shipping": {"header": "<PERSON><PERSON><PERSON> tùy chọn vận chuyển cho {{zone}}", "hint": "T<PERSON><PERSON> một tùy chọn vận chuyển mới để xác định cách sản phẩm được vận chuyển từ vị trí này.", "label": "<PERSON><PERSON><PERSON> chọn vận chuyển", "successToast": "<PERSON><PERSON><PERSON> chọn vận chuyển {{name}} đã đượ<PERSON> tạo thành công."}, "pickup": {"header": "Tạ<PERSON> tùy chọn nhận tại chỗ cho {{zone}}", "hint": "Tạo một tùy chọn nhận tại chỗ mới để xác định cách sản phẩm được nhận từ vị trí này.", "label": "<PERSON><PERSON><PERSON> chọn nhận tại chỗ", "successToast": "<PERSON><PERSON><PERSON> chọn nhận tại chỗ {{name}} đã được tạo thành công."}, "returns": {"header": "<PERSON><PERSON><PERSON> tùy chọn trả hàng cho {{zone}}", "hint": "<PERSON><PERSON><PERSON> một tùy chọn trả hàng mới để xác định cách sản phẩm được trả lại vị trí này.", "label": "<PERSON><PERSON><PERSON> ch<PERSON>n tr<PERSON> hàng", "successToast": "<PERSON><PERSON><PERSON> chọn trả hàng {{name}} đã đư<PERSON><PERSON> tạo thành công."}, "tabs": {"details": "<PERSON> ti<PERSON>", "prices": "<PERSON><PERSON><PERSON> c<PERSON>"}, "action": "<PERSON><PERSON><PERSON> t<PERSON>n"}, "delete": {"confirmation": "Bạn sắp xóa tùy chọn vận chuyển {{name}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON> chọn vận chuyển {{name}} đã đượ<PERSON> xóa thành công."}, "edit": {"header": "Chỉnh sửa tùy chọn vận chuyển", "action": "Chỉnh sửa tùy chọn", "successToast": "<PERSON><PERSON><PERSON> chọn vận chuyển {{name}} đã đư<PERSON><PERSON> cập nhật thành công."}, "pricing": {"action": "Chỉnh sửa giá"}, "conditionalPrices": {"header": "<PERSON><PERSON><PERSON> theo điều kiện cho {{name}}", "description": "<PERSON><PERSON><PERSON>n lý giá theo điều kiện cho tùy chọn vận chuyển này dựa trên tổng giá trị mục hàng trong giỏ.", "attributes": {"cartItemTotal": "Tổng giá trị mục hàng trong giỏ"}, "summaries": {"range": "Nếu <0>{{attribute}}</0> nằm trong khoảng từ <1>{{gte}}</1> đế<PERSON> <2>{{lte}}</2>", "greaterThan": "Nếu <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Nếu <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON><PERSON><PERSON> giá", "manageConditionalPrices": "<PERSON><PERSON><PERSON><PERSON> lý giá theo điều kiện"}, "rules": {"amount": "<PERSON><PERSON><PERSON> t<PERSON>y chọn vận chuyển", "gte": "Tổng giá trị tối thiểu của giỏ hàng", "lte": "Tổng giá trị tối đa của giỏ hàng"}, "customRules": {"label": "<PERSON>uy tắc tùy chỉnh", "tooltip": "<PERSON><PERSON><PERSON> theo điều kiện này có các quy tắc không thể quản lý trong bảng điều khiển.", "eq": "Tổng giá trị giỏ hàng phải bằng", "gt": "Tổng giá trị giỏ hàng phải lớn hơn", "lt": "Tổng giá trị giỏ hàng phải nhỏ hơn"}, "errors": {"amountRequired": "<PERSON><PERSON><PERSON> tùy chọn vận chuyển là bắt buộc", "minOrMaxRequired": "<PERSON><PERSON>i cung cấp ít nhất một trong hai giá trị: tổng tối thiểu hoặc tối đa của giỏ hàng", "minGreaterThanMax": "Tổng giá trị tối thiểu của giỏ hàng phải nhỏ hơn hoặc bằng tổng giá trị tối đa", "duplicateAmount": "<PERSON><PERSON><PERSON> tùy chọn vận chuyển phải duy nhất cho mỗi điều kiện", "overlappingConditions": "<PERSON><PERSON><PERSON> điều kiện phải duy nhất trong tất cả quy tắc giá"}}, "fields": {"count": {"shipping_one": "{{count}} t<PERSON><PERSON> chọn vận chuyển", "shipping_other": "{{count}} t<PERSON><PERSON> chọn vận chuyển", "pickup_one": "{{count}} tù<PERSON> chọn nhận tại chỗ", "pickup_other": "{{count}} tù<PERSON> chọn nhận tại chỗ", "returns_one": "{{count}} t<PERSON><PERSON> chọn tr<PERSON> hàng", "returns_other": "{{count}} t<PERSON><PERSON> chọn tr<PERSON> hàng"}, "priceType": {"label": "Loại giá", "options": {"fixed": {"label": "<PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> của tùy chọn vận chuyển là cố định và không thay đổi dựa trên nội dung đơn hàng."}, "calculated": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> của tùy chọn vận chuyển đư<PERSON><PERSON> nhà cung cấp giao hàng tính toán trong quá trình thanh toán."}}}, "enableInStore": {"label": "<PERSON><PERSON><PERSON> trong c<PERSON>a hàng", "hint": "<PERSON><PERSON> đ<PERSON>nh liệu khách hàng có thể sử dụng tùy chọn này khi thanh toán hay không."}, "provider": "<PERSON><PERSON><PERSON> cung cấp giao hàng", "profile": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "fulfillmentOption": "<PERSON><PERSON><PERSON> ch<PERSON>n giao hàng"}}, "serviceZones": {"create": {"headerPickup": "Tạ<PERSON> vùng dịch vụ cho việc nhận tại chỗ từ {{location}}", "headerShipping": "<PERSON><PERSON><PERSON> vùng dịch vụ cho việc vận chuyển từ {{location}}", "action": "Tạo v<PERSON>ng d<PERSON>ch vụ", "successToast": "<PERSON><PERSON><PERSON> dịch vụ {{name}} đã đ<PERSON><PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh sửa vùng dịch vụ", "successToast": "<PERSON><PERSON><PERSON> dịch vụ {{name}} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa vùng dịch vụ {{name}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON> dịch vụ {{name}} đã đư<PERSON><PERSON> xóa thành công."}, "manageAreas": {"header": "<PERSON><PERSON><PERSON><PERSON> lý khu vực cho {{name}}", "action": "<PERSON><PERSON><PERSON><PERSON> lý khu vực", "label": "<PERSON><PERSON> v<PERSON>", "hint": "<PERSON><PERSON><PERSON> các khu vực địa lý mà vùng dịch vụ này bao phủ.", "successToast": "<PERSON><PERSON> vực cho {{name}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "fields": {"noRecords": "<PERSON><PERSON><PERSON><PERSON> có vùng dịch vụ nào để thêm tùy chọn vận chuyển.", "tip": "Vùng dịch vụ là tập hợp các khu vực hoặc vùng địa lý. <PERSON><PERSON> đư<PERSON> dùng để giới hạn các tùy chọn vận chuyển khả dụng cho một tập hợp vị trí đã xác định."}}}, "shippingProfile": {"domain": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "subtitle": "<PERSON><PERSON><PERSON><PERSON> các sản phẩm có yêu cầu vận chuyển tương tự vào các hồ sơ.", "create": {"header": "<PERSON><PERSON><PERSON> hồ sơ vận chuyển", "hint": "<PERSON><PERSON><PERSON> một hồ sơ vận chuyển mới để nhóm các sản phẩm có yêu cầu vận chuyển tương tự.", "successToast": "<PERSON><PERSON> sơ vận chuyển {{name}} đã đư<PERSON><PERSON> tạo thành công."}, "delete": {"title": "<PERSON><PERSON><PERSON> hồ sơ vận chuyển", "description": "Bạn sắp x<PERSON><PERSON> hồ sơ vận chuyển {{name}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON> sơ vận chuyển {{name}} đã đư<PERSON><PERSON> xóa thành công."}, "tooltip": {"type": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON><PERSON> hồ sơ vận chuyển, ví dụ: Nặng, <PERSON><PERSON><PERSON>, Chỉ vận chuyển hàng hóa, v.v."}}, "taxRegions": {"domain": "<PERSON><PERSON> v<PERSON>c thuế", "list": {"hint": "<PERSON>u<PERSON>n lý số thuế bạn áp dụng cho khách hàng khi họ mua sắm từ các quốc gia và khu vực khác nhau."}, "delete": {"confirmation": "Bạn sắp xóa một khu vực thuế. <PERSON>ành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON> vực thuế đã được xóa thành công."}, "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế", "hint": "T<PERSON>o một khu vực thuế mới để xác định thuế suất cho một quốc gia cụ thể.", "errors": {"rateIsRequired": "<PERSON><PERSON><PERSON> suất là bắt buộc khi tạo thuế suất mặc định.", "nameIsRequired": "<PERSON><PERSON><PERSON> là bắt buộc khi tạo thuế suất mặc định."}, "successToast": "<PERSON><PERSON> vực thuế đã được tạo thành công."}, "province": {"header": "Tỉnh/Thành", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế tỉnh/thành", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một tỉnh/thành cụ thể."}}, "state": {"header": "<PERSON>", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế bang", "hint": "<PERSON><PERSON><PERSON> một khu vực thuế mới để xác định thuế suất cho một bang cụ thể."}}, "stateOrTerritory": {"header": "<PERSON> hoặc Lãnh thổ", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế bang/lãnh thổ", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một bang hoặc lãnh thổ cụ thể."}}, "county": {"header": "Hạ<PERSON>", "create": {"header": "<PERSON><PERSON><PERSON> khu vự<PERSON> thu<PERSON> hạt", "hint": "T<PERSON>o một khu vực thuế mới để xác định thuế suất cho một hạt cụ thể."}}, "region": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế vùng", "hint": "T<PERSON>o một khu vực thuế mới để xác định thuế suất cho một vùng cụ thể."}}, "department": {"header": "<PERSON><PERSON> v<PERSON><PERSON> h<PERSON>nh ch<PERSON>h", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế khu vực hành ch<PERSON>h", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một khu vực hành chính cụ thể."}}, "territory": {"header": "<PERSON><PERSON><PERSON> thổ", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế lãnh thổ", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một lãnh thổ cụ thể."}}, "prefecture": {"header": "Tỉnh (<PERSON><PERSON><PERSON><PERSON>)", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế tỉnh (Nhật Bản)", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một tỉnh cụ thể (Nhật <PERSON>)."}}, "district": {"header": "Quận/Huyện", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế quận/huyện", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một quận/huyện cụ thể."}}, "governorate": {"header": "Tỉnh (Ả Rập)", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế tỉnh (Ả Rập)", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một tỉnh cụ thể (Ả Rập)."}}, "canton": {"header": "<PERSON> (T<PERSON><PERSON>y <PERSON>)", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế bang (Thụy Sĩ)", "hint": "T<PERSON>o một khu vực thuế mới để xác định thuế suất cho một bang cụ thể (<PERSON>hụy <PERSON>ĩ)."}}, "emirate": {"header": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quốc", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế tiểu v<PERSON><PERSON><PERSON> quốc", "hint": "Tạo một khu vực thuế mới để xác định thuế suất cho một tiểu vương quốc cụ thể."}}, "sublevel": {"header": "<PERSON><PERSON><PERSON> phụ", "create": {"header": "<PERSON><PERSON><PERSON> khu vực thuế cấp phụ", "hint": "T<PERSON>o một khu vực thuế mới để xác định thuế suất cho một cấp phụ cụ thể."}}, "taxOverrides": {"header": "<PERSON><PERSON> đè thuế", "create": {"header": "<PERSON><PERSON><PERSON> ghi đè thuế", "hint": "T<PERSON><PERSON> một thuế suất ghi đè các thuế suất mặc định cho các điều kiện đã chọn."}, "edit": {"header": "Chỉnh sửa ghi đè thuế", "hint": "Chỉnh sửa thuế suất ghi đè các thuế suất mặc định cho các điều kiện đã chọn."}}, "taxRates": {"create": {"header": "<PERSON><PERSON><PERSON> thu<PERSON> su<PERSON>t", "hint": "<PERSON><PERSON><PERSON> một thuế suất mới để xác định thuế suất cho một khu vực.", "successToast": "<PERSON><PERSON><PERSON> suất đã đư<PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh sửa thuế suất", "hint": "Chỉnh sửa thuế suất để xác định thuế suất cho một khu vực.", "successToast": "<PERSON><PERSON><PERSON> suất đã đư<PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa thuế suất {{name}}. <PERSON>ành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON> suất đã đư<PERSON>c xóa thành công."}}, "fields": {"isCombinable": {"label": "<PERSON><PERSON> thể kết hợp", "hint": "<PERSON><PERSON> định liệu thuế suất này có thể kết hợp với thuế suất mặc định của khu vực thuế hay không.", "true": "<PERSON><PERSON> thể kết hợp", "false": "<PERSON><PERSON><PERSON><PERSON> thể kết hợp"}, "defaultTaxRate": {"label": "<PERSON><PERSON><PERSON> suất mặc định", "tooltip": "<PERSON>hu<PERSON> suất mặc định cho khu vực này. <PERSON><PERSON> dụ như thuế VAT tiêu chuẩn cho một quốc gia hoặc khu vực.", "action": "<PERSON><PERSON><PERSON> thuế suất mặc định"}, "taxRate": "<PERSON><PERSON><PERSON>", "taxCode": "<PERSON><PERSON> thuế", "targets": {"label": "<PERSON><PERSON><PERSON> t<PERSON> dụng", "hint": "<PERSON><PERSON><PERSON> các đối tượng mà thuế suất này sẽ áp dụng.", "options": {"product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productCollection": "<PERSON><PERSON> s<PERSON>u tập sản phẩm", "productTag": "Thẻ sản phẩm", "productType": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "customerGroup": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng"}, "operators": {"in": "trong", "on": "trên", "and": "và"}, "placeholders": {"product": "<PERSON><PERSON><PERSON> k<PERSON>m sản phẩm", "productCollection": "<PERSON><PERSON><PERSON> k<PERSON>m bộ sưu tập sản phẩm", "productTag": "<PERSON><PERSON><PERSON> kiếm thẻ sản phẩm", "productType": "<PERSON><PERSON><PERSON> kiếm lo<PERSON>i sản phẩm", "customerGroup": "<PERSON><PERSON><PERSON> kiếm nhóm khách hàng"}, "tags": {"product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productCollection": "<PERSON><PERSON> s<PERSON>u tập sản phẩm", "productTag": "Thẻ sản phẩm", "productType": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "customerGroup": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng"}, "modal": {"header": "<PERSON>h<PERSON><PERSON> đối tư<PERSON> áp dụng"}, "values_one": "{{count}} giá trị", "values_other": "{{count}} giá trị", "numberOfTargets_one": "{{count}} <PERSON><PERSON><PERSON>", "numberOfTargets_other": "{{count}} <PERSON><PERSON><PERSON>", "additionalValues_one": "và {{count}} gi<PERSON> trị nữa", "additionalValues_other": "và {{count}} gi<PERSON> trị nữa", "action": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>"}, "sublevels": {"labels": {"province": "Tỉnh/Thành", "state": "<PERSON>", "region": "<PERSON><PERSON><PERSON>", "stateOrTerritory": "Bang/Lãnh thổ", "department": "<PERSON><PERSON> v<PERSON><PERSON> h<PERSON>nh ch<PERSON>h", "county": "Hạ<PERSON>", "territory": "<PERSON><PERSON><PERSON> thổ", "prefecture": "Tỉnh (<PERSON><PERSON><PERSON><PERSON>)", "district": "Quận/Huyện", "governorate": "Tỉnh (Ả Rập)", "emirate": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quốc", "canton": "<PERSON> (T<PERSON><PERSON>y <PERSON>)", "sublevel": "Mã cấp phụ"}, "placeholders": {"province": "Chọn tỉnh/thành", "state": "<PERSON><PERSON><PERSON> bang", "region": "<PERSON><PERSON><PERSON> v<PERSON>ng", "stateOrTerritory": "<PERSON><PERSON><PERSON> bang/lãnh thổ", "department": "<PERSON><PERSON><PERSON> khu v<PERSON><PERSON> hành ch<PERSON>h", "county": "<PERSON><PERSON><PERSON>", "territory": "<PERSON><PERSON><PERSON> lãnh thổ", "prefecture": "<PERSON><PERSON><PERSON> tỉnh (<PERSON><PERSON><PERSON><PERSON>)", "district": "<PERSON><PERSON><PERSON> quận/huyện", "governorate": "<PERSON><PERSON><PERSON> tỉnh (Ả Rập)", "emirate": "<PERSON><PERSON><PERSON> ti<PERSON>u v<PERSON><PERSON><PERSON> quốc", "canton": "<PERSON><PERSON><PERSON> (Thụy Sĩ)"}, "tooltips": {"sublevel": "Nhập mã ISO 3166-2 cho khu vực thuế cấp phụ.", "notPartOfCountry": "{{province}} dư<PERSON><PERSON> <PERSON>h<PERSON> không thuộc {{country}}. <PERSON>ui lòng kiểm tra lại xem thông tin này có chính xác không."}, "alert": {"header": "<PERSON><PERSON><PERSON> khu vực cấp phụ bị tắt cho khu vực thuế này", "description": "<PERSON><PERSON><PERSON> khu vực cấp phụ mặc định bị tắt cho khu vực này. Bạn có thể bật chúng để tạo các khu vực cấp phụ như tỉnh, bang hoặc lãnh thổ.", "action": "<PERSON><PERSON><PERSON> khu v<PERSON>c cấp phụ"}}, "noDefaultRate": {"label": "<PERSON><PERSON><PERSON><PERSON> có thuế suất mặc định", "tooltip": "<PERSON>hu vực thuế này chưa có thuế suất mặc định. Nếu có thuế suất tiê<PERSON> chuẩ<PERSON>, như VAT củ<PERSON> quốc gia, vui lòng thêm vào khu vực này."}}}, "promotions": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "sections": {"details": "<PERSON> tiết k<PERSON>n mãi"}, "tabs": {"template": "<PERSON><PERSON><PERSON>", "details": "<PERSON> ti<PERSON>", "campaign": "<PERSON><PERSON><PERSON>"}, "fields": {"type": "<PERSON><PERSON><PERSON>", "value_type": "Loại giá trị", "value": "<PERSON><PERSON><PERSON> trị", "campaign": "<PERSON><PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON> thức", "allocation": "<PERSON><PERSON> bổ", "addCondition": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện", "clearAll": "<PERSON><PERSON><PERSON> tất cả", "amount": {"tooltip": "<PERSON>ọn mã tiền tệ để kích hoạt cài đặt số tiền"}, "conditions": {"rules": {"title": "Ai có thể sử dụng mã này?", "description": "Khách hàng nào được phép sử dụng mã khuyến mãi? Mã khuyến mãi sẽ áp dụng cho tất cả khách hàng nếu không thay đổi."}, "target-rules": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi sẽ áp dụng cho mục hàng nào?", "description": "Khuyến mãi sẽ áp dụng cho các mục hàng đáp ứng các điều kiện sau."}, "buy-rules": {"title": "Giỏ hàng cần có gì để kích hoạt khuyến mãi?", "description": "<PERSON><PERSON><PERSON> các điều kiện này khớ<PERSON>, chúng tôi sẽ kích hoạt khuyến mãi cho các mục hàng mục tiêu."}}}, "tooltips": {"campaignType": "<PERSON><PERSON>i chọn mã tiền tệ trong khuyến mãi để đặt ngân sách chi tiêu."}, "errors": {"requiredField": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "promotionTabError": "Sửa lỗi trong tab <PERSON><PERSON><PERSON>ến mãi trước khi tiếp tục"}, "toasts": {"promotionCreateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi ({{code}}) đã đư<PERSON><PERSON> tạo thành công."}, "create": {}, "edit": {"title": "Chỉnh sửa chi tiết khu<PERSON>ến mãi", "rules": {"title": "Chỉnh sửa điều kiện sử dụng"}, "target-rules": {"title": "Chỉnh sửa điều kiện mục hàng"}, "buy-rules": {"title": "Chỉnh sửa quy tắc mua hàng"}}, "campaign": {"header": "<PERSON><PERSON><PERSON>", "edit": {"header": "Chỉnh s<PERSON>a chiến dịch", "successToast": "<PERSON><PERSON> cập nhật thành công chiến dịch của khu<PERSON>ến mãi."}, "actions": {"goToCampaign": "<PERSON><PERSON> đến chiến dịch"}}, "campaign_currency": {"tooltip": "<PERSON><PERSON><PERSON> là đơn vị tiền tệ của khuyến mãi. Thay đổi nó từ tab Chi tiết."}, "form": {"required": "<PERSON><PERSON><PERSON> b<PERSON>", "and": "VÀ", "selectAttribute": "<PERSON><PERSON><PERSON>", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON> dịch hiện có", "description": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ến mãi vào một chiến dịch hiện có.", "placeholder": {"title": "<PERSON><PERSON><PERSON><PERSON> có chiến dịch hiện có", "desc": "Bạn có thể tạo một chiến dịch để theo dõi nhiều khuyến mãi và đặt giới hạn ngân sách."}}, "new": {"title": "<PERSON><PERSON><PERSON> d<PERSON> mới", "description": "<PERSON><PERSON><PERSON> một chiến dịch mới cho khuyến mãi này."}, "none": {"title": "<PERSON><PERSON><PERSON><PERSON> có chiến dịch", "description": "<PERSON><PERSON><PERSON><PERSON> tục mà không liên kết khuyến mãi với chiến dịch"}}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "draft": {"title": "Nháp", "description": "<PERSON><PERSON><PERSON><PERSON> hàng chưa thể sử dụng mã này"}, "active": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> hàng có thể sử dụng mã này"}, "inactive": {"title": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "description": "<PERSON><PERSON><PERSON><PERSON> hàng không còn có thể sử dụng mã này"}}, "method": {"label": "<PERSON><PERSON><PERSON><PERSON> thức", "code": {"title": "<PERSON><PERSON> k<PERSON>ến mãi", "description": "<PERSON><PERSON><PERSON><PERSON> hàng ph<PERSON>i nhập mã này khi thanh toán"}, "automatic": {"title": "<PERSON><PERSON> động", "description": "<PERSON><PERSON><PERSON><PERSON> hàng sẽ thấy khuyến mãi này khi thanh toán"}}, "max_quantity": {"title": "<PERSON><PERSON> l<PERSON><PERSON> tối đa", "description": "<PERSON><PERSON> lượ<PERSON> mục hàng tối đa mà khuyến mãi này áp dụng."}, "type": {"standard": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>n mãi tiêu chuẩn"}, "buyget": {"title": "Mua X tặng Y", "description": "<PERSON><PERSON><PERSON><PERSON>n mãi kiểu mua X được tặng Y"}}, "allocation": {"each": {"title": "<PERSON><PERSON><PERSON> m<PERSON>c", "description": "<PERSON><PERSON> dụng giá trị cho từng mục hàng"}, "across": {"title": "<PERSON><PERSON><PERSON> bộ", "description": "<PERSON><PERSON> dụng giá trị cho tất cả mục hàng"}}, "code": {"title": "Mã", "description": "<PERSON><PERSON> mà khách hàng sẽ nhập khi thanh toán."}, "value": {"title": "<PERSON><PERSON><PERSON> trị k<PERSON>ến mãi"}, "value_type": {"fixed": {"title": "<PERSON><PERSON><PERSON> trị k<PERSON>ến mãi", "description": "Số tiền đ<PERSON><PERSON><PERSON>. <PERSON><PERSON> dụ: 100"}, "percentage": {"title": "<PERSON><PERSON><PERSON> trị k<PERSON>ến mãi", "description": "<PERSON>ần trăm đư<PERSON> giảm trên số tiền. Ví dụ: 8%"}}}, "deleteWarning": "Bạn sắp x<PERSON>a khu<PERSON>ến mãi {{code}}. Hành động này không thể hoàn tác.", "createPromotionTitle": "<PERSON><PERSON><PERSON> mãi", "type": "<PERSON><PERSON><PERSON> k<PERSON> mãi", "conditions": {"add": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện", "list": {"noRecordsMessage": "Thêm điều kiện để giới hạn các mục hàng mà khuyến mãi áp dụng."}}}, "campaigns": {"domain": "<PERSON><PERSON><PERSON>", "details": "<PERSON> tiết chiến dịch", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "expired": "<PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON> lên l<PERSON>ch"}, "delete": {"title": "Bạn có chắc không?", "description": "Bạn sắp xóa chiến dịch '{{name}}'. <PERSON><PERSON>nh động này không thể hoàn tác.", "successToast": "<PERSON>ến dịch '{{name}}' đã đư<PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh s<PERSON>a chiến dịch", "description": "Chỉnh sửa chi tiết của chiến dịch.", "successToast": "<PERSON>ến dịch '{{name}}' đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "configuration": {"header": "<PERSON><PERSON><PERSON> h<PERSON>nh", "edit": {"header": "Chỉnh sửa cấu hình chiến dịch", "description": "Chỉnh sửa cấu hình của chiến dịch.", "successToast": "<PERSON><PERSON><PERSON> hình chiến dịch đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}}, "create": {"title": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "description": "<PERSON><PERSON><PERSON> một chiến dị<PERSON> k<PERSON>ến mãi.", "hint": "<PERSON><PERSON><PERSON> một chiến dị<PERSON> k<PERSON>ến mãi.", "header": "<PERSON><PERSON><PERSON> ch<PERSON> d<PERSON>ch", "successToast": "<PERSON>ến dịch '{{name}}' đã đư<PERSON><PERSON> tạo thành công."}, "fields": {"name": "<PERSON><PERSON><PERSON>", "identifier": "<PERSON><PERSON><PERSON> danh", "start_date": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "end_date": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "total_spend": "<PERSON><PERSON> s<PERSON>ch đã chi", "total_used": "<PERSON><PERSON> sách đã sử dụng", "budget_limit": "<PERSON><PERSON><PERSON><PERSON> hạn ngân s<PERSON>ch", "campaign_id": {"hint": "Chỉ các chiến dịch có cùng mã tiền tệ với khuyến mãi mới được hiển thị trong danh sách này."}}, "budget": {"create": {"hint": "<PERSON><PERSON><PERSON> ngân sách cho chiến dịch.", "header": "<PERSON><PERSON> s<PERSON>ch chi<PERSON>n d<PERSON>ch"}, "details": "<PERSON><PERSON> s<PERSON>ch chi<PERSON>n d<PERSON>ch", "fields": {"type": "<PERSON><PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "limit": "<PERSON><PERSON><PERSON><PERSON> hạn", "used": "Đã sử dụng"}, "type": {"spend": {"title": "<PERSON> tiêu", "description": "Đặt giới hạn cho tổng số tiền giảm giá của tất cả lượt sử dụng khuyến mãi."}, "usage": {"title": "Sử dụng", "description": "Đặt giới hạn cho số lần khuyến mãi có thể được sử dụng."}}, "edit": {"header": "Chỉnh sửa ngân sách chiến dịch"}}, "promotions": {"remove": {"title": "<PERSON><PERSON><PERSON> k<PERSON>ến mãi khỏi chiến dịch", "description": "Bạn sắp xóa {{count}} khuyến mãi khỏi chiến dịch. Hành động này không thể hoàn tác."}, "alreadyAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi này đã đư<PERSON><PERSON> thêm vào chiến dịch.", "alreadyAddedDiffCampaign": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi này đã đư<PERSON>c thêm vào một chiến dịch khác ({{name}}).", "currencyMismatch": "Đơn vị tiền tệ của khuyến mãi và chiến dịch không khớp", "toast": {"success": "<PERSON><PERSON> thêm thành công {{count}} khuyến mãi vào chiến dịch"}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> tạo một khu<PERSON>ến mãi trước."}}, "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có khu<PERSON>ến mãi nào trong chiến dịch."}}, "deleteCampaignWarning": "Bạn sắp xóa chiến dịch {{name}}. Hành động này không thể hoàn tác.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Bảng giá", "subtitle": "T<PERSON><PERSON> chương trình giảm giá hoặc ghi đè giá cho các điều kiện cụ thể.", "delete": {"confirmation": "Bạn sắp x<PERSON>a bảng giá {{title}}. <PERSON>ành động này không thể hoàn tác.", "successToast": "B<PERSON>ng giá {{title}} đã đư<PERSON>c xóa thành công."}, "create": {"header": "<PERSON><PERSON>o bảng giá", "subheader": "<PERSON><PERSON><PERSON> một bảng giá mới để quản lý giá của sản phẩm.", "tabs": {"details": "<PERSON> ti<PERSON>", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "prices": "<PERSON><PERSON><PERSON> c<PERSON>"}, "successToast": "B<PERSON>ng giá {{title}} đã đư<PERSON><PERSON> tạo thành công.", "products": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> t<PERSON>o một sản phẩm trước."}}}, "edit": {"header": "Chỉnh sửa bảng giá", "successToast": "<PERSON><PERSON><PERSON> giá {{title}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "configuration": {"header": "<PERSON><PERSON><PERSON> h<PERSON>nh", "edit": {"header": "Chỉnh sửa cấu hình bảng giá", "description": "Chỉnh sửa cấu hình của bảng giá.", "successToast": "<PERSON><PERSON><PERSON> hình bảng giá đã đư<PERSON><PERSON> cập nhật thành công."}}, "products": {"header": "<PERSON><PERSON><PERSON> p<PERSON>m", "actions": {"addProducts": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "editPrices": "Chỉnh sửa giá"}, "delete": {"confirmation_one": "Bạn sắp xóa giá của {{count}} sản phẩm trong bảng giá. Hành động này không thể hoàn tác.", "confirmation_other": "Bạn sắp xóa giá của {{count}} sản phẩm trong bảng giá. Hành động này không thể hoàn tác.", "successToast_one": "<PERSON><PERSON> xóa thành công giá của {{count}} sản phẩm.", "successToast_other": "<PERSON><PERSON> xóa thành công giá của {{count}} sản phẩm."}, "add": {"successToast": "<PERSON><PERSON><PERSON> đã đư<PERSON><PERSON> thêm thành công vào bảng giá."}, "edit": {"successToast": "<PERSON><PERSON><PERSON> đã đư<PERSON><PERSON> cập nhật thành công."}}, "fields": {"priceOverrides": {"label": "<PERSON><PERSON> đè giá", "header": "<PERSON><PERSON> đè giá"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "options": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "draft": "Nháp", "expired": "<PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON> lên l<PERSON>ch"}}, "type": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON> loại bảng giá bạn muốn tạo.", "options": {"sale": {"label": "G<PERSON>ảm giá", "description": "<PERSON><PERSON><PERSON> giảm là những thay đổi giá tạm thời cho sản phẩm."}, "override": {"label": "<PERSON><PERSON>", "description": "<PERSON><PERSON> đè thườ<PERSON> đư<PERSON><PERSON> dùng để tạo giá dành riêng cho khách hàng."}}}, "startsAt": {"label": "Bảng giá có ngày bắt đầu không?", "hint": "<PERSON><PERSON><PERSON> lịch để bảng giá kích hoạt trong tương lai."}, "endsAt": {"label": "Bảng giá có ngày hết hạn không?", "hint": "<PERSON><PERSON><PERSON> lị<PERSON> để bảng giá ngừng hoạt động trong tương lai."}, "customerAvailability": {"header": "<PERSON><PERSON><PERSON> nh<PERSON>m kh<PERSON>ch hàng", "label": "<PERSON><PERSON><PERSON> dụng cho khách hàng", "hint": "<PERSON><PERSON><PERSON> nhóm khách hàng mà bảng giá sẽ áp dụng.", "placeholder": "<PERSON><PERSON><PERSON> kiếm nhóm khách hàng", "attribute": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng"}}}, "profile": {"domain": "<PERSON><PERSON> sơ", "manageYourProfileDetails": "<PERSON><PERSON><PERSON><PERSON> lý chi tiết hồ sơ của bạn.", "fields": {"languageLabel": "<PERSON><PERSON><PERSON>", "usageInsightsLabel": "Thông tin sử dụng"}, "edit": {"header": "Chỉnh s<PERSON><PERSON> hồ sơ", "languageHint": "Ngôn ngữ bạn muốn sử dụng trong bảng điều khiển quản trị. Đi<PERSON>u này không thay đổi ngôn ngữ của cửa hàng.", "languagePlaceholder": "<PERSON><PERSON><PERSON> ngôn ngữ", "usageInsightsHint": "<PERSON>a sẻ thông tin sử dụng để giúp chúng tôi cải thiện Medusa. Bạn có thể đọc thêm về những gì chúng tôi thu thập và cách sử dụng trong <0>tài liệu</0> củ<PERSON> chúng tôi."}, "toast": {"edit": "<PERSON>hay đổi hồ sơ đã đ<PERSON><PERSON><PERSON> lưu"}}, "users": {"domain": "<PERSON><PERSON><PERSON><PERSON> dùng", "editUser": "Chỉnh sửa người dùng", "inviteUser": "<PERSON><PERSON><PERSON> người dùng", "inviteUserHint": "<PERSON><PERSON><PERSON> một người dùng mới đến cửa hàng của bạn.", "sendInvite": "<PERSON><PERSON><PERSON> lời mời", "pendingInvites": "<PERSON><PERSON><PERSON> mời đang chờ", "deleteInviteWarning": "Bạn sắp xóa lời mời cho {{email}}. Hành động này không thể hoàn tác.", "resendInvite": "<PERSON><PERSON><PERSON> lại lời mời", "copyInviteLink": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết mời", "expiredOnDate": "<PERSON><PERSON><PERSON> hạn vào {{date}}", "validFromUntil": "<PERSON><PERSON><PERSON> l<PERSON> từ <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "<PERSON><PERSON> chấp nhận vào {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON> chấp n<PERSON>n", "pending": "<PERSON><PERSON> chờ", "expired": "<PERSON><PERSON><PERSON>"}, "roles": {"admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "developer": "<PERSON><PERSON><PERSON> ph<PERSON>t triển", "member": "<PERSON><PERSON><PERSON><PERSON> viên"}, "list": {"empty": {"heading": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng", "description": "<PERSON><PERSON> một người dùng đư<PERSON><PERSON> mời, họ sẽ xuất hiện ở đây."}, "filtered": {"heading": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "description": "<PERSON><PERSON><PERSON>ng có người dùng nào khớp với tiêu chí lọc hiện tại."}}, "deleteUserWarning": "Bạn sắp xóa người dùng {{name}}. Hành động này không thể hoàn tác.", "deleteUserSuccess": "<PERSON><PERSON><PERSON><PERSON> dùng {{name}} đã đư<PERSON>c xóa thành công", "invite": "<PERSON><PERSON><PERSON>"}, "store": {"domain": "<PERSON><PERSON><PERSON> h<PERSON>", "manageYourStoresDetails": "<PERSON><PERSON><PERSON><PERSON> lý chi tiết cửa hàng của bạn", "editStore": "Chỉnh sửa cửa hàng", "defaultCurrency": "<PERSON><PERSON><PERSON><PERSON> tệ mặc định", "defaultRegion": "<PERSON><PERSON> vực mặc định", "defaultSalesChannel": "<PERSON><PERSON><PERSON> bán hàng mặc định", "defaultLocation": "<PERSON><PERSON> trí mặc định", "swapLinkTemplate": "Mẫu liên kết đổi hàng", "paymentLinkTemplate": "Mẫu liên kết thanh toán", "inviteLinkTemplate": "Mẫu liên kết mời", "currencies": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "addCurrencies": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n tệ", "enableTaxInclusivePricing": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm thuế", "disableTaxInclusivePricing": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm thuế", "removeCurrencyWarning_one": "Bạn sắp xóa {{count}} tiền tệ khỏi cửa hàng. H<PERSON><PERSON> đảm bảo bạn đã xóa tất cả giá sử dụng tiền tệ này trước khi tiếp tục.", "removeCurrencyWarning_other": "Bạn sắp xóa {{count}} tiền tệ khỏi cửa hàng. H<PERSON><PERSON> đảm bảo bạn đã xóa tất cả giá sử dụng các tiền tệ này trước khi tiếp tục.", "currencyAlreadyAdded": "<PERSON><PERSON><PERSON><PERSON> tệ này đã đư<PERSON><PERSON> thêm vào cửa hàng của bạn.", "edit": {"header": "Chỉnh sửa cửa hàng"}, "toast": {"update": "<PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "currenciesUpdated": "<PERSON><PERSON><PERSON><PERSON> tệ đã đư<PERSON><PERSON> cập nhật thành công", "currenciesRemoved": "<PERSON>ã xóa tiền tệ khỏi cửa hàng thành công", "updatedTaxInclusivitySuccessfully": "<PERSON><PERSON><PERSON> bao gồm thuế đã đư<PERSON><PERSON> cập nhật thành công"}}, "regions": {"domain": "<PERSON><PERSON> v<PERSON>", "subtitle": "<PERSON>hu vực là một phạm vi bạn bán sản phẩm, có thể bao gồm nhiều quốc gia, với các thuế suất, nhà cung cấp và tiền tệ khác nhau.", "createRegion": "Tạo khu vực", "createRegionHint": "<PERSON><PERSON><PERSON><PERSON> lý thuế suất và nhà cung cấp cho một nhóm quốc gia.", "addCountries": "<PERSON><PERSON><PERSON><PERSON> quốc gia", "editRegion": "Chỉnh sửa khu vực", "countriesHint": "<PERSON><PERSON><PERSON><PERSON> các quốc gia thuộc khu vực nà<PERSON>.", "deleteRegionWarning": "Bạn sắp xóa khu vực {{name}}. <PERSON>ành động này không thể hoàn tác.", "removeCountriesWarning_one": "Bạn sắp xóa {{count}} quốc gia khỏi khu vực. Hành động này không thể hoàn tác.", "removeCountriesWarning_other": "Bạn sắp xóa {{count}} quốc gia khỏi khu vực. Hành động này không thể hoàn tác.", "removeCountryWarning": "Bạn sắp xóa quốc gia {{name}} khỏi khu vực. Hành động này không thể hoàn tác.", "automaticTaxesHint": "<PERSON><PERSON>, thuế sẽ chỉ được tính lúc thanh toán dựa trên địa chỉ giao hàng.", "taxInclusiveHint": "<PERSON><PERSON>, g<PERSON><PERSON> trong khu vực sẽ bao gồm thuế.", "providersHint": "<PERSON><PERSON><PERSON><PERSON> các nhà cung cấp thanh toán khả dụng trong khu vực này.", "shippingOptions": "<PERSON><PERSON><PERSON> chọn vận chuyển", "deleteShippingOptionWarning": "Bạn sắp xóa tùy chọn vận chuyển {{name}}. Hành động này không thể hoàn tác.", "return": "<PERSON><PERSON><PERSON> h<PERSON>", "outbound": "<PERSON><PERSON><PERSON> đi", "priceType": "Loại giá", "flatRate": "<PERSON><PERSON><PERSON> cố định", "calculated": "<PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "<PERSON><PERSON>o một khu vực cho các vùng bạn bán hàng."}, "toast": {"delete": "<PERSON><PERSON> vực đã đư<PERSON>c xóa thành công", "edit": "Chỉnh sửa khu vực đã đư<PERSON><PERSON> lưu", "create": "<PERSON><PERSON> vực đã đư<PERSON><PERSON> tạo thành công", "countries": "<PERSON>uốc gia trong khu vực đã đ<PERSON><PERSON><PERSON> cập nhật thành công"}, "shippingOption": {"createShippingOption": "<PERSON><PERSON><PERSON> t<PERSON>y chọn vận chuyển", "createShippingOptionHint": "<PERSON><PERSON><PERSON> một tùy chọn vận chuyển mới cho khu vực.", "editShippingOption": "Chỉnh sửa tùy chọn vận chuyển", "fulfillmentMethod": "<PERSON><PERSON><PERSON><PERSON> thức giao hàng", "type": {"outbound": "<PERSON><PERSON><PERSON> đi", "outboundHint": "Sử dụng nếu bạn đang tạo tùy chọn vận chuyển để gửi sản phẩm đến khách hàng.", "return": "<PERSON><PERSON><PERSON> h<PERSON>", "returnHint": "Sử dụng nếu bạn đang tạo tùy chọn vận chuyển để khách hàng trả sản phẩm cho bạn."}, "priceType": {"label": "Loại giá", "flatRate": "<PERSON><PERSON><PERSON> cố định", "calculated": "<PERSON><PERSON><PERSON>"}, "availability": {"adminOnly": "Chỉ quản trị viên", "adminOnlyHint": "<PERSON><PERSON><PERSON><PERSON>, t<PERSON><PERSON> chọn vận chuyển sẽ chỉ khả dụng trong bảng điều khiển quản trị, không hiển thị trên giao diện cửa hàng."}, "taxInclusiveHint": "<PERSON><PERSON>, g<PERSON><PERSON> của tùy chọn vận chuyển sẽ bao gồm thuế.", "requirements": {"label": "<PERSON><PERSON><PERSON> c<PERSON>", "hint": "<PERSON><PERSON><PERSON>nh các yêu cầu cho tùy chọn vận chuyển."}}}, "taxes": {"domain": "<PERSON><PERSON> v<PERSON>c thuế", "domainDescription": "<PERSON><PERSON><PERSON><PERSON> lý khu vực thuế của bạn", "countries": {"taxCountriesHint": "<PERSON>à<PERSON> đặt thuế áp dụng cho các quốc gia được liệt kê."}, "settings": {"editTaxSettings": "Chỉnh sửa cài đặt thuế", "taxProviderLabel": "<PERSON><PERSON><PERSON> cung cấp thuế", "systemTaxProviderLabel": "<PERSON><PERSON><PERSON> cung cấp thuế hệ thống", "calculateTaxesAutomaticallyLabel": "<PERSON><PERSON><PERSON> thuế tự động", "calculateTaxesAutomaticallyHint": "<PERSON><PERSON> bậ<PERSON>, thuế suất sẽ được tính tự động và áp dụng cho giỏ hàng. <PERSON><PERSON> tắ<PERSON>, thuế phải được tính thủ công lúc thanh toán. Thuế thủ công được khuyến nghị khi sử dụng nhà cung cấp thuế bên thứ ba.", "applyTaxesOnGiftCardsLabel": "<PERSON><PERSON> dụng thuế cho thẻ quà tặng", "applyTaxesOnGiftCardsHint": "<PERSON><PERSON> b<PERSON><PERSON>, thuế sẽ được áp dụng cho thẻ quà tặng lúc thanh toán. Ở một số quốc gia, quy định thuế yêu cầu áp dụng thuế cho thẻ quà tặng khi mua.", "defaultTaxRateLabel": "<PERSON><PERSON><PERSON> suất mặc định", "defaultTaxCodeLabel": "<PERSON><PERSON> thuế mặc định"}, "defaultRate": {"sectionTitle": "<PERSON><PERSON><PERSON> suất mặc định"}, "taxRate": {"sectionTitle": "<PERSON><PERSON><PERSON>", "createTaxRate": "<PERSON><PERSON><PERSON> thu<PERSON> su<PERSON>t", "createTaxRateHint": "<PERSON><PERSON><PERSON> một thuế suất mới cho khu vực.", "deleteRateDescription": "Bạn sắp xóa thuế suất {{name}}. <PERSON>ành động này không thể hoàn tác.", "editTaxRate": "Chỉnh sửa thuế suất", "editRateAction": "Chỉnh sửa thuế suất", "editOverridesAction": "Chỉnh sửa ghi đè", "editOverridesTitle": "Chỉnh sửa ghi đè thuế suất", "editOverridesHint": "<PERSON><PERSON><PERSON> đ<PERSON>nh các ghi đè cho thuế suất.", "deleteTaxRateWarning": "Bạn sắp xóa thuế suất {{name}}. <PERSON>ành động này không thể hoàn tác.", "productOverridesLabel": "<PERSON><PERSON> <PERSON><PERSON> sản phẩm", "productOverridesHint": "<PERSON><PERSON><PERSON> đ<PERSON> các ghi đè sản phẩm cho thuế suất.", "addProductOverridesAction": "<PERSON><PERSON><PERSON><PERSON> ghi đè sản phẩm", "productTypeOverridesLabel": "<PERSON><PERSON> <PERSON><PERSON> lo<PERSON>i sản phẩm", "productTypeOverridesHint": "<PERSON><PERSON><PERSON> đ<PERSON> các ghi đè loại sản phẩm cho thuế suất.", "addProductTypeOverridesAction": "<PERSON>h<PERSON><PERSON> ghi đè lo<PERSON>i sản phẩm", "shippingOptionOverridesLabel": "<PERSON><PERSON> đ<PERSON> tùy chọn vận chuyển", "shippingOptionOverridesHint": "<PERSON><PERSON><PERSON> định các ghi đè tùy chọn vận chuyển cho thuế suất.", "addShippingOptionOverridesAction": "<PERSON><PERSON><PERSON><PERSON> ghi đè tùy chọn vận chuyển", "productOverridesHeader": "<PERSON><PERSON><PERSON> p<PERSON>m", "productTypeOverridesHeader": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "shippingOptionOverridesHeader": "<PERSON><PERSON><PERSON> chọn vận chuyển"}}, "locations": {"domain": "<PERSON><PERSON> trí", "editLocation": "Chỉnh sửa vị trí", "addSalesChannels": "<PERSON><PERSON><PERSON><PERSON> kênh b<PERSON> hàng", "noLocationsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vị trí", "selectLocations": "<PERSON><PERSON><PERSON> các vị trí lưu trữ mục hàng.", "deleteLocationWarning": "Bạn sắp xóa vị trí {{name}}. <PERSON>ành động này không thể hoàn tác.", "removeSalesChannelsWarning_one": "<PERSON><PERSON>n sắp xóa {{count}} kênh bán hàng khỏi vị trí.", "removeSalesChannelsWarning_other": "<PERSON><PERSON>n sắp xóa {{count}} kênh bán hàng khỏi vị trí.", "toast": {"create": "<PERSON><PERSON> trí đã đ<PERSON><PERSON><PERSON> tạo thành công", "update": "<PERSON><PERSON> trí đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "removeChannel": "<PERSON><PERSON><PERSON> bán hàng đã đư<PERSON>c xóa thành công"}}, "reservations": {"domain": "Đặt trước", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý số lượng đặt trước của các mục hàng trong kho.", "deleteWarning": "Bạn sắp xóa một đặt trước. Hành động này không thể hoàn tác."}, "salesChannels": {"domain": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý các kênh bán hàng trực tuyến và ngoại tuyến mà bạn bán sản phẩm.", "list": {"empty": {"heading": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kênh bán hàng", "description": "<PERSON><PERSON> một kênh bán hàng đư<PERSON> tạo, nó sẽ xuất hiện ở đây."}, "filtered": {"heading": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "description": "<PERSON><PERSON><PERSON><PERSON> có kênh bán hàng nào khớp với tiêu chí lọc hiện tại."}}, "createSalesChannel": "<PERSON><PERSON><PERSON> k<PERSON> b<PERSON> hàng", "createSalesChannelHint": "<PERSON><PERSON><PERSON> một kênh bán hàng mới để bán sản phẩm của bạn.", "enabledHint": "<PERSON><PERSON><PERSON> định liệu kênh bán hàng có đư<PERSON><PERSON> bật hay không.", "removeProductsWarning_one": "Bạn sắp xóa {{count}} sản phẩm khỏi {{sales_channel}}.", "removeProductsWarning_other": "Bạn sắp xóa {{count}} sản phẩm khỏi {{sales_channel}}.", "addProducts": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "editSalesChannel": "Chỉnh sửa kênh b<PERSON> hàng", "productAlreadyAdded": "<PERSON><PERSON><PERSON> phẩm này đã đư<PERSON>c thêm vào kênh bán hàng.", "deleteSalesChannelWarning": "Bạn sắp x<PERSON><PERSON> kênh bán hàng {{name}}. Hành động này không thể hoàn tác.", "toast": {"create": "<PERSON><PERSON><PERSON> bán hàng đã đư<PERSON><PERSON> tạo thành công", "update": "<PERSON><PERSON><PERSON> bán hàng đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "delete": "<PERSON><PERSON><PERSON> bán hàng đã đư<PERSON>c xóa thành công"}, "tooltip": {"cannotDeleteDefault": "<PERSON><PERSON><PERSON><PERSON> thể xóa kênh bán hàng mặc định"}, "products": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào trong kênh bán hàng."}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> t<PERSON>o một sản phẩm trước."}}}}, "apiKeyManagement": {"domain": {"publishable": "Khóa API công khai", "secret": "Khóa API b<PERSON> mật"}, "subtitle": {"publishable": "Quản lý khóa API dùng trong giao diện cửa hàng để giới hạn phạm vi yêu cầu cho các kênh bán hàng cụ thể.", "secret": "Qu<PERSON>n lý khóa API dùng để xác thực người dùng quản trị trong các ứng dụng quản trị."}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "revoked": "<PERSON><PERSON> thu hồi"}, "type": {"publishable": "<PERSON><PERSON><PERSON> khai", "secret": "<PERSON><PERSON>"}, "create": {"createPublishableHeader": "Tạo khóa API công khai", "createPublishableHint": "Tạo một khóa API công khai mới để giới hạn phạm vi yêu cầu cho các kênh bán hàng cụ thể.", "createSecretHeader": "Tạo khóa API bí mật", "createSecretHint": "Tạo một khóa API bí mật mới để truy cập API Medusa với tư cách người dùng quản trị đã xác thực.", "secretKeyCreatedHeader": "<PERSON><PERSON><PERSON><PERSON> bí mật đã đ<PERSON><PERSON><PERSON> tạo", "secretKeyCreatedHint": "<PERSON><PERSON><PERSON><PERSON> bí mật mới của bạn đã được tạo. <PERSON>o chép và lưu trữ an toàn ngay bây giờ. Đ<PERSON>y là lần duy nhất nó sẽ được hiển thị.", "copySecretTokenSuccess": "<PERSON><PERSON><PERSON><PERSON> bí mật đã được sao chép vào bộ nhớ tạm.", "copySecretTokenFailure": "<PERSON><PERSON><PERSON><PERSON> thể sao chép khóa bí mật vào bộ nhớ tạm.", "successToast": "Khóa API đã được tạo thành công."}, "edit": {"header": "Chỉnh sửa khóa API", "description": "Chỉnh sửa tiêu đề của khóa API.", "successToast": "Khóa API {{title}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "salesChannels": {"title": "<PERSON><PERSON><PERSON><PERSON> kênh b<PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> các kênh bán hàng mà khóa API nên được giới hạn.", "successToast_one": "{{count}} kê<PERSON> bán hàng đã đư<PERSON><PERSON> thêm thành công vào khóa API.", "successToast_other": "{{count}} kê<PERSON> bán hàng đã đư<PERSON><PERSON> thêm thành công vào khóa API.", "alreadyAddedTooltip": "<PERSON><PERSON><PERSON> bán hàng này đã đư<PERSON>c thêm vào khóa API.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON><PERSON> có kênh bán hàng nào trong phạm vi của khóa API công khai."}}, "delete": {"warning": "Bạn sắp xóa khóa API {{title}}. Hành động này không thể hoàn tác.", "successToast": "Khóa API {{title}} đã được xóa thành công."}, "revoke": {"warning": "Bạn sắp thu hồi khóa API {{title}}. Hành động này không thể hoàn tác.", "successToast": "Khóa API {{title}} đã đ<PERSON><PERSON>c thu hồi thành công."}, "addSalesChannels": {"list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> t<PERSON>o một kênh bán hàng trước."}}, "removeSalesChannel": {"warning": "Bạn sắp xóa kênh bán hàng {{name}} khỏi khóa API. Hành động này không thể hoàn tác.", "warningBatch_one": "Bạn sắp xóa {{count}} kênh bán hàng khỏi khóa API. Hành động này không thể hoàn tác.", "warningBatch_other": "Bạn sắp xóa {{count}} kênh bán hàng khỏi khóa API. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON> bán hàng đã được xóa khỏi khóa API thành công.", "successToastBatch_one": "{{count}} kê<PERSON> bán hàng đã được xóa khỏi khóa API thành công.", "successToastBatch_other": "{{count}} kê<PERSON> bán hàng đã được xóa khỏi khóa API thành công."}, "actions": {"revoke": "<PERSON><PERSON> hồi khó<PERSON> API", "copy": "Sao chép khóa API", "copySuccessToast": "Khóa API đã được sao chép vào bộ nhớ tạm."}, "table": {"lastUsedAtHeader": "Lần sử dụng cuối", "createdAtHeader": "<PERSON><PERSON><PERSON><PERSON> gian thu hồi"}, "fields": {"lastUsedAtLabel": "Lần sử dụng cuối", "revokedByLabel": "<PERSON><PERSON><PERSON><PERSON> thu hồi bởi", "revokedAtLabel": "<PERSON><PERSON><PERSON><PERSON> gian thu hồi", "createdByLabel": "<PERSON><PERSON><PERSON><PERSON> tạo bởi"}}, "returnReasons": {"domain": "Lý do trả hàng", "subtitle": "<PERSON><PERSON><PERSON><PERSON> lý lý do cho các mục hàng trả lại.", "calloutHint": "<PERSON><PERSON><PERSON><PERSON> lý các lý do để phân loại trả hàng.", "editReason": "Chỉnh sửa lý do trả hàng", "create": {"header": "<PERSON><PERSON><PERSON><PERSON> lý do trả hàng", "subtitle": "<PERSON><PERSON><PERSON> các lý do phổ biến nhất cho việc trả hàng.", "hint": "Tạo một lý do trả hàng mới để phân loại trả hàng.", "successToast": "<PERSON>ý do trả hàng {{label}} đã đư<PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh sửa lý do trả hàng", "subtitle": "Chỉnh sửa giá trị của lý do trả hàng.", "successToast": "<PERSON><PERSON> do trả hàng {{label}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa lý do trả hàng {{label}}. Hành động này không thể hoàn tác.", "successToast": "Lý do trả hàng {{label}} đã đư<PERSON>c xóa thành công."}, "fields": {"value": {"label": "<PERSON><PERSON><PERSON> trị", "placeholder": "wrong_size", "tooltip": "<PERSON><PERSON><PERSON> trị nên là một định danh duy nhất cho lý do trả hàng."}, "label": {"label": "<PERSON><PERSON>ã<PERSON>", "placeholder": "<PERSON><PERSON><PERSON>i"}, "description": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> hàng nhận nh<PERSON>m kích thước"}}}, "login": {"forgotPassword": "<PERSON>u<PERSON>n mật khẩu? - <0>Đặt lại</0>", "title": "<PERSON><PERSON>o mừng đến với <PERSON>", "hint": "<PERSON><PERSON><PERSON> nhập để truy cập khu vực tài k<PERSON>n"}, "invite": {"title": "<PERSON><PERSON>o mừng đến với <PERSON>", "hint": "<PERSON><PERSON><PERSON> tài kho<PERSON>n của bạn dưới đây", "backToLogin": "Quay lại đăng nh<PERSON>p", "createAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "alreadyHaveAccount": "Đã có tài k<PERSON>? - <0><PERSON><PERSON><PERSON> nh<PERSON>p</0>", "emailTooltip": "Email của bạn không thể thay đổi. <PERSON><PERSON>u muốn dùng email kh<PERSON><PERSON>, cần gửi một lời mời mới.", "invalidInvite": "<PERSON><PERSON><PERSON> mời không hợp lệ hoặc đã hết hạn.", "successTitle": "<PERSON><PERSON><PERSON> kho<PERSON>n của bạn đã đư<PERSON><PERSON> đăng ký", "successHint": "<PERSON><PERSON><PERSON> đầu ngay với <PERSON>a <PERSON>min.", "successAction": "Khởi động <PERSON><PERSON>", "invalidTokenTitle": "<PERSON><PERSON> mời của bạn không hợp lệ", "invalidTokenHint": "<PERSON><PERSON><PERSON> thử yêu cầu một liên kết mời mới.", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "toast": {"accepted": "<PERSON><PERSON>i mời đã đ<PERSON><PERSON><PERSON> chấp nhận thành công"}}, "resetPassword": {"title": "Đặt lại mật khẩu", "hint": "Nhập email của bạn dưới đây, chúng tôi sẽ gửi hướng dẫn cách đặt lại mật khẩu.", "email": "Email", "sendResetInstructions": "<PERSON><PERSON><PERSON> hướng dẫn đặt lại", "backToLogin": "<0><PERSON>uay lại đăng nhập</0>", "newPasswordHint": "<PERSON><PERSON><PERSON> mật khẩu mới dưới đây.", "invalidTokenTitle": "<PERSON>ã đặt lại của bạn không hợp lệ", "invalidTokenHint": "<PERSON><PERSON><PERSON> thử yêu cầu một liên kết đặt lại mới.", "expiredTokenTitle": "<PERSON>ã đặt lại của bạn đã hết hạn", "goToResetPassword": "<PERSON><PERSON> đến đặt lại mật khẩu", "resetPassword": "Đặt lại mật khẩu", "newPassword": "<PERSON><PERSON><PERSON> mới", "repeatNewPassword": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu mới", "tokenExpiresIn": "<PERSON><PERSON> hết hạn sau <0>{{time}}</0> phút", "successfulRequestTitle": "Đã gửi email thành công", "successfulRequest": "<PERSON><PERSON>g tôi đã gửi một email để bạn có thể đặt lại mật khẩu. <PERSON>ểm tra thư mục spam nếu bạn không nhận được sau vài phút.", "successfulResetTitle": "Đặt lại mật khẩu thành công", "successfulReset": "<PERSON><PERSON> lòng đăng nhập trên trang đăng nhập.", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "invalidLinkTitle": "<PERSON><PERSON><PERSON> kết đặt lại của bạn không hợp lệ", "invalidLinkHint": "<PERSON><PERSON><PERSON> thử đặt lại mật khẩu một lần nữa."}, "workflowExecutions": {"domain": "<PERSON><PERSON><PERSON> công vi<PERSON>c", "subtitle": "<PERSON>em và theo dõi các lần thực thi luồng công việc trong ứng dụng Me<PERSON>a của bạn.", "transactionIdLabel": "ID giao d<PERSON>ch", "workflowIdLabel": "ID luồng công việc", "progressLabel": "<PERSON><PERSON><PERSON><PERSON> độ", "stepsCompletedLabel_one": "{{completed}} trong {{count}} b<PERSON><PERSON>c", "stepsCompletedLabel_other": "{{completed}} trong {{count}} b<PERSON><PERSON>c", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> có luồng công việc nào đ<PERSON><PERSON><PERSON> thực thi."}, "history": {"sectionTitle": "<PERSON><PERSON><PERSON> s<PERSON>", "runningState": "<PERSON><PERSON> ch<PERSON>...", "awaitingState": "<PERSON><PERSON> chờ", "failedState": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "skippedState": "Đã bỏ qua", "skippedFailureState": "Đ<PERSON> bỏ qua (Thất bại)", "definitionLabel": "<PERSON><PERSON><PERSON>", "outputLabel": "<PERSON><PERSON><PERSON> qu<PERSON>", "compensateInputLabel": "<PERSON><PERSON><PERSON> vào bù đắp", "revertedLabel": "<PERSON><PERSON> hoàn tác", "errorLabel": "Lỗi"}, "state": {"done": "<PERSON><PERSON><PERSON> t<PERSON>t", "failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "reverted": "<PERSON><PERSON> hoàn tác", "invoking": "<PERSON><PERSON>", "compensating": "<PERSON><PERSON> bù đ<PERSON>p", "notStarted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu"}, "transaction": {"state": {"waitingToCompensate": "<PERSON><PERSON> chờ bù đắp"}}, "step": {"state": {"skipped": "Đã bỏ qua", "skippedFailure": "Đ<PERSON> bỏ qua (Thất bại)", "dormant": "Tạm nghỉ", "timeout": "<PERSON><PERSON><PERSON> thời gian"}}}, "productTypes": {"domain": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "subtitle": "<PERSON><PERSON><PERSON> xếp sản phẩm của bạn theo lo<PERSON>.", "create": {"header": "<PERSON><PERSON><PERSON> lo<PERSON> sản phẩm", "hint": "<PERSON><PERSON><PERSON> một loại sản phẩm mới để phân loại sản phẩm của bạn.", "successToast": "<PERSON><PERSON><PERSON> sản phẩm {{value}} đã đư<PERSON><PERSON> tạo thành công."}, "edit": {"header": "Chỉnh sửa lo<PERSON>i sản phẩm", "successToast": "<PERSON><PERSON><PERSON> sản phẩm {{value}} đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa loại sản phẩm {{value}}. Hành động này không thể hoàn tác.", "successToast": "<PERSON><PERSON><PERSON> sản phẩm {{value}} đã đư<PERSON><PERSON> xóa thành công."}, "fields": {"value": "<PERSON><PERSON><PERSON> trị"}}, "productTags": {"domain": "Thẻ sản phẩm", "create": {"header": "<PERSON><PERSON>o thẻ sản phẩm", "subtitle": "<PERSON><PERSON><PERSON> một thẻ sản phẩm mới để phân loại sản phẩm của bạn.", "successToast": "Thẻ sản phẩm {{value}} đã được tạo thành công."}, "edit": {"header": "Chỉnh sửa thẻ sản phẩm", "subtitle": "Chỉnh sửa giá trị của thẻ sản phẩm.", "successToast": "Thẻ sản phẩm {{value}} đã đư<PERSON><PERSON> cập nhật thành công."}, "delete": {"confirmation": "Bạn sắp xóa thẻ sản phẩm {{value}}. Hành động này không thể hoàn tác.", "successToast": "Thẻ sản phẩm {{value}} đã được xóa thành công."}, "fields": {"value": "<PERSON><PERSON><PERSON> trị"}}, "notifications": {"domain": "<PERSON><PERSON><PERSON><PERSON> báo", "emptyState": {"title": "<PERSON><PERSON><PERSON>ng có thông báo", "description": "Hiện tại bạn không có thông báo nào, nh<PERSON><PERSON> khi có, chúng sẽ xuất hiện ở đây."}, "accessibility": {"description": "<PERSON>h<PERSON><PERSON> báo về các hoạt động của Medusa sẽ được liệt kê ở đây."}}, "errors": {"serverError": "Lỗi máy chủ - Thử lại sau.", "invalidCredentials": "Email hoặc mật khẩu sai"}, "statuses": {"scheduled": "<PERSON><PERSON> lên l<PERSON>ch", "expired": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "Nháp", "enabled": "<PERSON><PERSON> bật", "disabled": "Đã tắt"}, "labels": {"productVariant": "<PERSON><PERSON><PERSON><PERSON> thể sản phẩm", "prices": "Giá", "available": "<PERSON><PERSON> sẵn", "inStock": "Trong kho", "added": "<PERSON><PERSON> thêm", "removed": "Đã xóa", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "beaware": "<PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON> t<PERSON>"}, "fields": {"amount": "<PERSON><PERSON> tiền", "refundAmount": "<PERSON><PERSON> tiền hoàn lại", "name": "<PERSON><PERSON><PERSON>", "default": "Mặc định", "lastName": "Họ", "firstName": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "customTitle": "Tiêu đề tùy chỉnh", "manageInventory": "<PERSON><PERSON><PERSON><PERSON> lý kho", "inventoryKit": "<PERSON><PERSON> bộ thành phần trong kho", "inventoryItems": "<PERSON><PERSON><PERSON> mụ<PERSON> kho hàng", "inventoryItem": "<PERSON><PERSON><PERSON> kho hàng", "requiredQuantity": "<PERSON><PERSON> l<PERSON> yêu cầu", "description": "<PERSON><PERSON>", "email": "Email", "password": "<PERSON><PERSON><PERSON>", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "newPassword": "<PERSON><PERSON><PERSON> mới", "repeatNewPassword": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu mới", "categories": "<PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON><PERSON><PERSON> thức vận chuy<PERSON>n", "configurations": "<PERSON><PERSON><PERSON> h<PERSON>nh", "conditions": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "category": "<PERSON><PERSON>", "collection": "<PERSON><PERSON> s<PERSON>u tập", "discountable": "<PERSON><PERSON> thể giảm giá", "handle": "<PERSON><PERSON><PERSON> danh", "subtitle": "<PERSON><PERSON> đề", "by": "Bởi", "item": "<PERSON><PERSON><PERSON>", "qty": "SL", "limit": "<PERSON><PERSON><PERSON><PERSON> hạn", "tags": "Thẻ", "type": "<PERSON><PERSON><PERSON>", "reason": "Lý do", "none": "<PERSON><PERSON><PERSON><PERSON> có", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "search": "<PERSON><PERSON><PERSON>", "percentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "sales_channels": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "customer_groups": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "product_tags": "Thẻ sản phẩm", "product_types": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "product_collections": "<PERSON><PERSON> s<PERSON>u tập sản phẩm", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "code": "Mã", "value": "<PERSON><PERSON><PERSON> trị", "disabled": "Đã tắt", "dynamic": "Động", "normal": "<PERSON><PERSON><PERSON>", "years": "Năm", "months": "<PERSON><PERSON><PERSON><PERSON>", "days": "<PERSON><PERSON><PERSON>", "hours": "Giờ", "minutes": "<PERSON><PERSON><PERSON>", "totalRedemptions": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> sử dụng", "countries": "Quốc gia", "paymentProviders": "<PERSON><PERSON><PERSON> cung cấp thanh toán", "refundReason": "Lý do hoàn tiền", "fulfillmentProviders": "<PERSON><PERSON><PERSON> cung cấp giao hàng", "fulfillmentProvider": "<PERSON><PERSON><PERSON> cung cấp giao hàng", "providers": "<PERSON><PERSON><PERSON> cung cấp", "availability": "Tình trạng sẵn có", "inventory": "<PERSON><PERSON>", "optional": "<PERSON><PERSON><PERSON>", "note": "<PERSON><PERSON><PERSON>", "automaticTaxes": "<PERSON><PERSON><PERSON> tự động", "taxInclusivePricing": "<PERSON><PERSON><PERSON> bao gồm thuế", "currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "address": "Địa chỉ", "address2": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, v.v.", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "postalCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia", "state": "<PERSON>", "province": "Tỉnh/Thành", "company": "<PERSON><PERSON>ng ty", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "metadata": "<PERSON><PERSON><PERSON> dữ liệu", "selectCountry": "<PERSON><PERSON><PERSON> quốc gia", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "variants": "<PERSON><PERSON><PERSON><PERSON> thể", "orders": "<PERSON><PERSON><PERSON> hàng", "account": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> đơn hàng", "paidTotal": "<PERSON>ổng đã ghi nhận", "totalExclTax": "<PERSON><PERSON><PERSON> ch<PERSON>a gồm thuế", "subtotal": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "outboundShipping": "<PERSON><PERSON><PERSON> chuyển gửi đi", "returnShipping": "<PERSON><PERSON><PERSON> chuyển trả hàng", "tax": "<PERSON><PERSON><PERSON>", "created": "Đã tạo", "key": "Khóa", "customer": "<PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON> hàng", "fulfillment": "<PERSON>ử lý giao hàng", "provider": "<PERSON><PERSON><PERSON> cung cấp", "payment": "<PERSON><PERSON> toán", "items": "<PERSON><PERSON><PERSON>", "salesChannel": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "region": "<PERSON><PERSON> v<PERSON>", "discount": "G<PERSON>ảm giá", "role": "<PERSON>ai trò", "sent": "Đ<PERSON> gửi", "salesChannels": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "createdAt": "Đã tạo", "updatedAt": "<PERSON><PERSON> cập nh<PERSON>t", "revokedAt": "<PERSON><PERSON> thu hồi", "true": "<PERSON><PERSON><PERSON>", "false": "<PERSON>", "giftCard": "Thẻ quà tặng", "tag": "Thẻ", "dateIssued": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> h<PERSON>nh", "issuedDate": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> h<PERSON>nh", "expiryDate": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "price": "Giá", "priceTemplate": "Giá {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON> cao", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "length": "<PERSON><PERSON><PERSON> dài", "weight": "<PERSON><PERSON><PERSON><PERSON>", "midCode": "Mã MID", "hsCode": "Mã HS", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Số lượng kho", "barcode": "Mã vạch", "countryOfOrigin": "<PERSON><PERSON><PERSON>", "material": "<PERSON><PERSON><PERSON> l<PERSON>", "thumbnail": "Ảnh thu nhỏ", "sku": "SKU", "managedInventory": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> quản lý", "allowBackorder": "Cho phép đặt trước", "inStock": "Trong kho", "location": "<PERSON><PERSON> trí", "quantity": "Số lượng", "variant": "<PERSON><PERSON><PERSON><PERSON> thể", "id": "ID", "parent": "Cha", "minSubtotal": "<PERSON><PERSON><PERSON> t<PERSON>h tối thiểu", "maxSubtotal": "<PERSON><PERSON><PERSON> t<PERSON>h tối đa", "shippingProfile": "<PERSON><PERSON> sơ vận chuy<PERSON>n", "summary": "<PERSON><PERSON><PERSON>", "details": "<PERSON> ti<PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "rate": "Tỷ lệ", "requiresShipping": "<PERSON><PERSON><PERSON> c<PERSON>u vận chuyển", "unitPrice": "Đơn giá", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "draft": "Nháp", "values": "<PERSON><PERSON><PERSON> trị"}, "dateTime": {"years_one": "Năm", "years_other": "Năm", "months_one": "<PERSON><PERSON><PERSON><PERSON>", "months_other": "<PERSON><PERSON><PERSON><PERSON>", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "<PERSON><PERSON><PERSON>", "days_one": "<PERSON><PERSON><PERSON>", "days_other": "<PERSON><PERSON><PERSON>", "hours_one": "Giờ", "hours_other": "Giờ", "minutes_one": "<PERSON><PERSON><PERSON>", "minutes_other": "<PERSON><PERSON><PERSON>", "seconds_one": "Giây", "seconds_other": "Giây"}}