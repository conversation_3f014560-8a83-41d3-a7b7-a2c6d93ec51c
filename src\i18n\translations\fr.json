{"$schema": "./$schema.json", "general": {"ascending": "Ascendant", "descending": "Descendant", "add": "Ajouter", "start": "D<PERSON>but", "end": "Fin", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "apply": "Appliquer", "range": "Plage", "search": "<PERSON><PERSON><PERSON>", "of": "de", "results": "résultats", "pages": "pages", "next": "Suivant", "prev": "Précédent", "is": "est", "timeline": "Chronologie", "success": "Su<PERSON>ès", "warning": "Attention", "tip": "Astuce", "error": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected": "Sélectionné", "enabled": "Activé", "disabled": "Désactivé", "expired": "Expiré", "active": "Actif", "revoked": "<PERSON><PERSON><PERSON><PERSON>", "new": "Nouveau", "modified": "<PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON>", "removed": "Supprimé", "admin": "Administrateur", "store": "Boutique", "details": "Détails", "items_one": "{{count}} item", "items_other": "{{count}} items", "countSelected": "{{count}} sélectionné", "countOfTotalSelected": "{{count}} sur {{total}} sélectionnés", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} plus", "areYouSure": "Êtes-vous sûr ?", "noRecordsFound": "Aucun enregistrement trouvé", "typeToConfirm": "Veuillez entrer {{val}} pour confirmer :", "noResultsTitle": "Aucun résultat", "noResultsMessage": "Essayez de changer les filtres ou la requête de recherche", "noSearchResults": "Aucun résultat de recherche", "noSearchResultsFor": "Aucun résultat de recherche pour <0>'{{query}}'</0>", "noRecordsTitle": "Aucun enregistrement", "noRecordsMessage": "Il n'y a pas d'enregistrement à afficher", "unsavedChangesTitle": "Êtes-vous sûr de vouloir quitter ce formulaire ?", "unsavedChangesDescription": "Vous avez des modifications non enregistrées qui seront perdues si vous quittez ce formulaire.", "includesTaxTooltip": "Les prix de cette colonne sont TTC (toutes taxes comprises).", "excludesTaxTooltip": "Les prix de cette colonne sont HT (Hors Taxes).", "noMoreData": "Aucune donnée supplémentaire"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} clé", "numberOfKeys_other": "{{count}} clés", "drawer": {"header_one": "JSON <0>· {{count}} clé</0>", "header_other": "JSON <0>· {{count}} clés</0>", "description": "Voir les données JSON pour cet objet."}}, "metadata": {"header": "<PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} clé", "numberOfKeys_other": "{{count}} clés", "edit": {"header": "Modifier les métadonnées", "description": "Modifier les métadonnées pour cet objet.", "successToast": "Les métadonnées ont été mises à jour avec succès.", "actions": {"insertRowAbove": "Insérer une ligne au-dessus", "insertRowBelow": "Insérer une ligne en dessous", "deleteRow": "Supprimer la ligne"}, "labels": {"key": "Clé", "value": "<PERSON><PERSON>"}, "complexRow": {"label": "Certaines lignes sont désactivées", "description": "Cet objet contient des métadonnées non-primitives, telles que des tableaux ou des objets, qui ne peuvent pas être modifiés ici. Pour modifier les lignes désactivées, utilisez l'API directement.", "tooltip": "Cette ligne est désactivée car elle contient des données non primitives."}}}, "validation": {"mustBeInt": "La valeur doit être un nombre entier.", "mustBePositive": "La valeur doit être un nombre positif."}, "actions": {"save": "Enregistrer", "saveAsDraft": "Enregistrer comme brouillon", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "publish": "Publier", "create": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "revoke": "Révoquer", "cancel": "Annuler", "forceConfirm": "Forcer la <PERSON>", "continueEdit": "Continuer l'édition", "enable": "Activer", "disable": "Désactiver", "undo": "Annuler", "complete": "<PERSON><PERSON><PERSON>", "viewDetails": "Voir les détails", "back": "Retour", "close": "<PERSON><PERSON><PERSON>", "showMore": "Voir plus", "continue": "<PERSON><PERSON><PERSON>", "continueWithEmail": "Continuer avec l'email", "idCopiedToClipboard": "ID copié dans le presse-papier", "addReason": "Ajouter une raison", "addNote": "Ajouter une note", "reset": "Réinitialiser", "confirm": "Confirmer", "edit": "Modifier", "addItems": "Ajouter des éléments", "download": "Télécharger", "clear": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "Tout effacer", "apply": "Appliquer", "add": "Ajouter", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "browse": "Parcourir", "logout": "Déconnexion", "hide": "Masquer", "export": "Exporter", "import": "Importer", "cannotUndo": "Cette action ne peut pas être annulée"}, "operators": {"in": "inclus"}, "app": {"search": {"label": "<PERSON><PERSON><PERSON>", "title": "Recherche", "description": "Recherchez dans l'ensemble de votre boutique, y compris les commandes, les produits, les clients et plus encore.", "allAreas": "Toutes les zones", "navigation": "Navigation", "openResult": "<PERSON><PERSON><PERSON><PERSON><PERSON> le résultat", "showMore": "Voir plus", "placeholder": "Accéder à ou rechercher n'importe quoi...", "noResultsTitle": "Aucun résultat trouvé", "noResultsMessage": "Nous n'avons trouvé aucun élément correspondant à votre recherche.", "emptySearchTitle": "Tapez pour rechercher", "emptySearchMessage": "Entrez un mot-clé ou une phrase pour explorer.", "loadMore": "Charger {{count}} de plus", "groups": {"all": "Toutes les zones", "customer": "Clients", "customerGroup": "Groupes de clients", "product": "Produits", "productVariant": "Variantes de produits", "inventory": "Inventaire", "reservation": "Réservations", "category": "Catégories", "collection": "Collections", "order": "Commandes", "promotion": "Promotions", "campaign": "Campagnes", "priceList": "Listes de prix", "user": "Utilisateurs", "region": "Régions", "taxRegion": "Régions fiscales", "returnReason": "<PERSON><PERSON><PERSON> de retour", "salesChannel": "Canaux de vente", "productType": "Types de produits", "productTag": "Tags de produits", "location": "Emplacements", "shippingProfile": "Profils d'expédition", "publishableApiKey": "Clés API publiques", "secretApiKey": "Clés API secrètes", "command": "Commandes", "navigation": "Navigation"}}, "keyboardShortcuts": {"pageShortcut": "Sauter à", "settingShortcut": "Paramètres", "commandShortcut": "Commandes", "then": "alors", "navigation": {"goToOrders": "Commandes", "goToProducts": "Produits", "goToCollections": "Collections", "goToCategories": "Catégories", "goToCustomers": "Clients", "goToCustomerGroups": "Groupes de clients", "goToInventory": "Inventaire", "goToReservations": "Réservations", "goToPriceLists": "Listes de prix", "goToPromotions": "Promotions", "goToCampaigns": "Campagnes"}, "settings": {"goToSettings": "Paramètres", "goToStore": "Boutique", "goToUsers": "Utilisateurs", "goToRegions": "Régions", "goToTaxRegions": "Régions fiscales", "goToSalesChannels": "Canaux de vente", "goToProductTypes": "Types de produits", "goToLocations": "Emplacements", "goToPublishableApiKeys": "Clés API publiques", "goToSecretApiKeys": "Clés API secrètes", "goToWorkflows": "Workflows", "goToProfile": "Profil", "goToReturnReasons": "<PERSON><PERSON><PERSON> de retour"}}, "menus": {"user": {"documentation": "Documentation", "changelog": "Changelog", "shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "profileSettings": "Paramètres du profil", "theme": {"label": "Thème", "dark": "Dark", "light": "Light", "system": "Système"}}, "store": {"label": "Boutique", "storeSettings": "Paramètres de la boutique"}, "actions": {"logout": "Déconnexion"}}, "nav": {"accessibility": {"title": "Navigation", "description": "Menu de navigation pour le tableau de bord."}, "common": {"extensions": "Extensions"}, "main": {"store": "Boutique", "storeSettings": "Paramètres de la boutique"}, "settings": {"header": "Paramètres", "general": "General", "developer": "Développeur", "myAccount": "Mon compte"}}}, "dataGrid": {"columns": {"view": "Affichage", "resetToDefault": "Réinitialiser à la valeur par défaut", "disabled": "Le changement de colonnes visibles est désactivé."}, "shortcuts": {"label": "<PERSON><PERSON><PERSON><PERSON>", "commands": {"undo": "Annuler", "redo": "Retour", "copy": "<PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "moveUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "moveDown": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "moveLeft": "<PERSON><PERSON><PERSON>r vers la gauche", "moveRight": "<PERSON><PERSON><PERSON>r vers la droite", "moveTop": "<PERSON>éplacer en haut", "moveBottom": "Déplacer en bas", "selectDown": "Sélectionner vers le bas", "selectUp": "Sélectionner vers le haut", "selectColumnDown": "Sélectionner la colonne vers le bas", "selectColumnUp": "Sélectionner la colonne vers le haut", "focusToolbar": "Focus barre d'outils", "focusCancel": "Ann<PERSON>r le focus"}}, "errors": {"fixError": "Corriger l'erreur", "count_one": "{{count}} erreur", "count_other": "{{count}} erreurs"}}, "filters": {"date": {"today": "<PERSON><PERSON><PERSON>'hui", "lastSevenDays": "7 derniers jours", "lastThirtyDays": "30 derniers jours", "lastNinetyDays": "90 derniers jours", "lastTwelveMonths": "12 derniers mois", "custom": "<PERSON><PERSON><PERSON><PERSON>", "from": "De", "to": "À"}, "compare": {"lessThan": "<PERSON><PERSON> de", "greaterThan": "Plus grand que", "exact": "Exact", "range": "Plage", "lessThanLabel": "moins de {{value}}", "greaterThanLabel": "plus grand que {{value}}", "andLabel": "et"}, "radio": {"yes": "O<PERSON>", "no": "Non", "true": "Vrai", "false": "Faux"}, "addFilter": "Ajouter un filtre"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "badRequestMessage": "La requête n'a pas pu être interprétée par le serveur en raison d'une syntaxe incorrecte.", "notFoundTitle": "404 - Il n'y a pas de page à cette adresse", "notFoundMessage": "Vérifiez l'URL et réessayez, ou utilisez la barre de recherche pour trouver ce que vous cherchez.", "internalServerErrorTitle": "500 - E<PERSON>ur interne du serveur", "internalServerErrorMessage": "Une erreur inattendue est survenue sur le serveur. Veuillez réessayer plus tard.", "defaultTitle": "Une erreur est survenue", "defaultMessage": "Une erreur inattendue est survenue lors du rendu de cette page.", "noMatchMessage": "La page que vous recherchez n'existe pas.", "backToDashboard": "Retour au tableau de bord"}, "addresses": {"title": "Adresses", "shippingAddress": {"header": "<PERSON><PERSON><PERSON>", "editHeader": "Modifier l'adresse de livraison", "editLabel": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, "billingAddress": {"header": "Adresse de facturation", "editHeader": "Modifier l'adresse de facturation", "editLabel": "Adresse de facturation", "label": "Adresse de facturation", "sameAsShipping": "Identique à l'adresse de livraison"}, "contactHeading": "Contact", "locationHeading": "Emplacement"}, "email": {"editHeader": "Modifier l'email", "editLabel": "Email", "label": "Email"}, "transferOwnership": {"header": "Transférer la propriété", "label": "Transférer la propriété", "details": {"order": "<PERSON><PERSON><PERSON> de la commande", "draft": "<PERSON><PERSON><PERSON> du brouillon"}, "currentOwner": {"label": "Propriétaire actuel", "hint": "Le propriétaire actuel de la commande."}, "newOwner": {"label": "Nouveau propriétaire", "hint": "Le nouveau propriétaire à transférer la commande à."}, "validation": {"mustBeDifferent": "Le nouveau propriétaire doit être différent du propriétaire actuel.", "required": "Le nouveau propriétaire est requis."}}, "sales_channels": {"availableIn": "Disponible dans <0>{{x}}</0> canaux de vente sur <1>{{y}}</1>"}, "products": {"domain": "Produits", "list": {"noRecordsMessage": "<PERSON><PERSON>ez votre premier produit pour commencer à vendre."}, "edit": {"header": "Modifier le produit", "description": "Modifiez les détails du produit.", "successToast": "Le produit {{title}} a été mis à jour avec succès."}, "create": {"title": "Créer un produit", "description": "Créer un nouveau produit.", "header": "General", "tabs": {"details": "Détails", "organize": "Organiser", "variants": "Variants", "inventory": "Inventaires"}, "errors": {"variants": "Veuillez sélectionner au moins une variante.", "options": "Veuillez créer au moins une option.", "uniqueSku": "Le SKU doit être unique."}, "inventory": {"heading": "Inventaires", "label": "Ajouter des articles d'inventaire à l'inventaire du kit de la variante.", "itemPlaceholder": "Sélectionner un article d'inventaire", "quantityPlaceholder": "Combien de ces articles sont nécessaires pour le kit ?"}, "variants": {"header": "Variants", "subHeadingTitle": "<PERSON><PERSON>, c'est un produit avec des variantes", "subHeadingDescription": "Lorsque la case n'est pas cochée, nous créerons une variante par défaut pour vous", "optionTitle": {"placeholder": "<PERSON><PERSON>"}, "optionValues": {"placeholder": "<PERSON>, <PERSON><PERSON><PERSON>, Grand"}, "productVariants": {"label": "Variantes de produit", "hint": "Cette classification affectera l'ordre des variantes dans votre boutique.", "alert": "Ajoutez des options pour créer des variantes.", "tip": "Les variantes non cochées ne seront pas créées. Vous pouvez toujours créer et modifier des variantes par la suite, mais cette liste correspond aux variations dans vos options de produit."}, "productOptions": {"label": "Options de produit", "hint": "Définissez les options du produit, par exemple la couleur, la taille, etc."}}, "successToast": "Le produit {{title}} a été créé avec succès."}, "export": {"header": "Exporter la liste des produits", "description": "Exportez la liste des produits vers un fichier CSV.", "success": {"title": "Nous traitons votre exportation", "description": "L'exportation peut prendre quelques minutes. Nous vous notifierons lorsque nous aurons terminé."}, "filters": {"title": "Filtres", "description": "Appliquez les filtres dans l'aperçu de la table pour ajuster cette vue"}, "columns": {"title": "Colonnes", "description": "Personnalisez les données exportées pour répondre à des besoins spécifiques"}}, "import": {"header": "Importer la liste des produits", "uploadLabel": "Importer des produits", "uploadHint": "Glisser-déposer un fichier CSV ou cliquer pour télécharger", "description": "Importez des produits en fournissant un fichier CSV dans un format prédéfini", "template": {"title": "Vous n'êtes pas sûr de la façon d'organiser votre liste ?", "description": "Téléchargez le modèle ci-dessous pour vous assurer de suivre le format correct."}, "upload": {"title": "Télécharger un fichier CSV", "description": "Grâce aux imports, vous pouvez ajouter ou mettre à jour des produits. Pour mettre à jour des produits existants, vous devez utiliser le handle et l'ID existants, pour mettre à jour des variantes existantes, vous devez utiliser l'ID existant. Vous serez invité à confirmer avant que nous importions les produits.", "preprocessing": "Traitement préalable...", "productsToCreate": "Les produits seront créés", "productsToUpdate": "Les produits seront mis à jour"}, "success": {"title": "Nous traitons votre importation", "description": "L'importation peut prendre quelques minutes. Nous vous notifierons lorsque nous aurons terminé."}}, "deleteWarning": "Vous êtes sur le point de supprimer le produit {{title}}. Cette action ne peut être annulée.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "Aucune variantes", "description": "Il n'y a aucune variantes à afficher."}, "filtered": {"heading": "Aucun résultat", "description": "Aucune variantes ne correspond aux critères de filtre actuel."}}, "attributes": "Attributs", "editAttributes": "Modifier les attributs", "editOptions": "Modifier les options", "editPrices": "Modifier les prix", "media": {"label": "Media", "editHint": "Ajoutez des médias au produit pour le mettre en avant dans votre boutique.", "makeThumbnail": "Faire de la vignette", "uploadImagesLabel": "Télécharger des images", "uploadImagesHint": "Glisser-déposer des images ici ou cliquer pour télécharger.", "invalidFileType": "'{{name}}' n'est pas un type de fichier supporté. Les types de fichiers supportés sont : {{types}}.", "failedToUpload": "Erreur lors du téléchargement des médias ajoutés. Veuillez réessayer.", "deleteWarning_one": "Vous êtes sur le point de supprimer {{count}} image. Cette action ne peut être annulée.", "deleteWarning_other": "Vous êtes sur le point de supprimer {{count}} images. Cette action ne peut être annulée.", "deleteWarningWithThumbnail_one": "Vous êtes sur le point de supprimer {{count}} image incluant la vignette. Cette action ne peut être annulée.", "deleteWarningWithThumbnail_other": "Vous êtes sur le point de supprimer {{count}} images incluant la vignette. Cette action ne peut être annulée.", "thumbnailTooltip": "Vignette", "galleryLabel": "Galerie", "downloadImageLabel": "Télécharger l'image actuelle", "deleteImageLabel": "Supprimer l'image actuelle", "emptyState": {"header": "Aucun média pour le moment", "description": "Ajoutez des médias au produit pour le mettre en avant dans votre boutique.", "action": "Ajouter des médias"}, "successToast": "Le média à été mis à jour avec succès."}, "discountableHint": "Lorsque la case n'est pas cochée, les remises ne seront pas appliquées à ce produit.", "noSalesChannels": "Non disponible dans aucun canal de vente", "variantCount_one": "{{count}} variante", "variantCount_other": "{{count}} variantes", "deleteVariantWarning": "Vous êtes sur le point de supprimer la variante {{title}}. Cette action ne peut être annulée.", "productStatus": {"draft": "Brouillon", "published": "<PERSON><PERSON><PERSON>", "proposed": "Propos<PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "fields": {"title": {"label": "Titre", "hint": "Donnez à votre produit un titre court et clair.<0/>50-60 caractères est la longueur recommandée pour les moteurs de recherche.", "placeholder": "Veste d'hiver"}, "subtitle": {"label": "Sous-titre", "placeholder": "Chaud et confortable"}, "handle": {"label": "<PERSON><PERSON>", "tooltip": "Le handle est utilisé pour faire référence au produit dans votre boutique. Si non spécifié, le handle sera généré à partir du titre du produit.", "placeholder": "veste-dhiver"}, "description": {"label": "Description", "hint": "Don<PERSON>z à votre produit une description courte et claire.<0/>120-160 caractères est la longueur recommandée pour les moteurs de recherche.", "placeholder": "Une veste chaude et confortable"}, "discountable": {"label": "Discountable", "hint": "Lorsque la case n'est pas cochée, les remises ne seront pas appliquées à ce produit"}, "type": {"label": "Type"}, "collection": {"label": "Collection"}, "categories": {"label": "Catégories"}, "tags": {"label": "Tags"}, "sales_channels": {"label": "Canaux de vente", "hint": "Ce produit ne sera disponible que dans le canal de vente par défaut si vous ne le modifiez pas."}, "countryOrigin": {"label": "Pays d'origine"}, "material": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "width": {"label": "<PERSON><PERSON>"}, "length": {"label": "<PERSON><PERSON><PERSON>"}, "height": {"label": "<PERSON><PERSON>"}, "weight": {"label": "Poids"}, "options": {"label": "Options de produit", "hint": "Les options sont utilisées pour définir la couleur, la taille, etc. du produit", "add": "Ajouter une option", "optionTitle": "Titre de l'option", "optionTitlePlaceholder": "<PERSON><PERSON><PERSON>", "variations": "Variations (séparées par des virgules)", "variantionsPlaceholder": "Rouge, Bleu, Vert"}, "variants": {"label": "Variantes de produit", "hint": "Les variantes non cochées ne seront pas créées, ce classement affectera l'ordre des variantes dans votre boutique."}, "mid_code": {"label": "Code MID"}, "hs_code": {"label": "Code HS"}}, "variant": {"edit": {"header": "Modifier la variante", "success": "Variante de produit modifiée avec succès"}, "create": {"header": "Variant details"}, "deleteWarning": "Êtes-vous sûr de vouloir supprimer cette variante ?", "pricesPagination": "1 - {{current}} de {{total}} prix", "tableItemAvailable": "{{availableCount}} disponible", "tableItem_one": "{{availableCount}} disponible à {{locationCount}} emplacement", "tableItem_other": "{{availableCount}} disponible à {{locationCount}} emplacements", "inventory": {"notManaged": "Not managed", "manageItems": "<PERSON><PERSON><PERSON> les articles d'inventaire", "notManagedDesc": "L'inventaire n'est pas géré pour cette variante. Activez ‘G<PERSON>rer l'inventaire’ pour suivre l'inventaire de la variante.", "manageKit": "Gérer l'inventaire du kit", "navigateToItem": "Aller à l'article d'inventaire", "actions": {"inventoryItems": "Aller à l'article d'inventaire", "inventoryKit": "Afficher les articles d'inventaire"}, "inventoryKit": "Inventaire du kit", "inventoryKitHint": "Cette variante est composée de plusieurs articles d'inventaire ?", "validation": {"itemId": "Veuillez sélectionner un article d'inventaire.", "quantity": "La quantité est requise. Veuillez entrer un nombre positif."}, "header": "Stock & Inventaire", "editItemDetails": "Modifier les détails de l'article", "manageInventoryLabel": "<PERSON><PERSON>rer l'inventaire", "manageInventoryHint": "Lorsque activé, nous ajusterons la quantité d'inventaire pour vous lorsque les commandes et les retours sont créés.", "allowBackordersLabel": "Autoriser les commandes partielles", "allowBackordersHint": "Lorsque activé, les clients peuvent acheter la variante même s'il n'y a pas de quantité disponible.", "toast": {"levelsBatch": "Inventaire mis à jour.", "update": "Article d'inventaire mis à jour avec succès.", "updateLevel": "Inventaire mis à jour avec succès.", "itemsManageSuccess": "Articles d'inventaire mis à jour avec succès."}}}, "options": {"header": "Options", "edit": {"header": "Modifier l'option", "successToast": "Option {{title}} modifiée avec succès."}, "create": {"header": "<PERSON><PERSON>er une option", "successToast": "Option {{title}} c<PERSON><PERSON> avec succès."}, "deleteWarning": "Vous êtes sur le point de supprimer l'option : {{title}}. Cette action ne peut être annulée."}, "organization": {"header": "Organisation", "edit": {"header": "Modifier l'organisation", "toasts": {"success": "Organisation de {{title}} modifiée avec succès."}}}, "toasts": {"delete": {"success": {"header": "Produit supprimé", "description": "{{title}} a été supprimé avec succès."}, "error": {"header": "<PERSON><PERSON>ur lors de la suppression du produit"}}}}, "collections": {"domain": "Collections", "subtitle": "Organisez vos produits en collections.", "createCollection": "C<PERSON><PERSON> une collection", "createCollectionHint": "<PERSON><PERSON>er une nouvelle collection pour organiser vos produits.", "createSuccess": "Collection créée avec succès.", "editCollection": "Modifier la collection", "handleTooltip": "Le handle est utilisé pour faire référence à la collection dans votre boutique. Si non spécifié, le handle sera généré à partir du titre de la collection.", "deleteWarning": "Vous êtes sur le point de  supprimer la collection {{title}}. Cette action ne peut être annulée.", "removeSingleProductWarning": "Vous êtes sur le point de supprimer le produit {{title}} de la collection. Cette action ne peut être annulée.", "removeProductsWarning_one": "Vous êtes sur le point de supprimer {{count}} produit de la collection. Cette action ne peut être annulée.", "removeProductsWarning_other": "Vous êtes sur le point de supprimer {{count}} produits de la collection. Cette action ne peut être annulée.", "products": {"list": {"noRecordsMessage": "Il n'y a pas de produits dans la collection."}, "add": {"successToast_one": "Produit ajouté à la collection avec succès.", "successToast_other": "Produits ajoutés à la collection avec succès."}, "remove": {"successToast_one": "Produit retiré de la collection avec succès.", "successToast_other": "Produits retirés de la collection avec succès."}}}, "categories": {"domain": "Categories", "subtitle": "Organisez vos produits en catégories, et gérez l'ordre et la hiérarchie de ces catégories.", "create": {"header": "<PERSON><PERSON>er une catégorie", "hint": "Create a new category to organize your products.", "tabs": {"details": "Détails", "organize": "Organiser par ordre"}, "successToast": "<PERSON><PERSON><PERSON><PERSON> {{name}} c<PERSON><PERSON> avec succès."}, "edit": {"header": "Editer une catégorie", "description": "Modifier la catégorie pour mettre à jour ses détails.", "successToast": "Catégorie modifiée avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer la catégorie {{name}}. Cette action ne peut être annulée.", "successToast": "<PERSON><PERSON><PERSON><PERSON> {{name}} supprimée avec succès."}, "products": {"add": {"disabledTooltip": "Le produit est déjà dans cette catégorie.", "successToast_one": "Produit ajouté à la catégorie avec succès.", "successToast_other": "Produits ajoutés à la catégorie avec succès."}, "remove": {"confirmation_one": "Vous êtes sur le point de supprimer {{count}} produit de la catégorie. Cette action ne peut être annulée.", "confirmation_other": "Vous êtes sur le point de supprimer {{count}} produits de la catégorie. Cette action ne peut être annulée.", "successToast_one": "Produit retiré de la catégorie avec succès.", "successToast_other": "Produits retirés de la catégorie avec succès."}, "list": {"noRecordsMessage": "Il n'y a pas de produits dans la catégorie."}}, "organize": {"header": "Organiser", "action": "Modifier l'ordre"}, "fields": {"visibility": {"label": "Visibility", "internal": "Interne", "public": "Publique"}, "status": {"label": "Statut", "active": "Active", "inactive": "Inactive"}, "path": {"label": "Chemin", "tooltip": "A<PERSON><PERSON><PERSON> le chemin complet de la catégorie."}, "children": {"label": "<PERSON><PERSON><PERSON>"}, "new": {"label": "Nouveau"}}}, "inventory": {"domain": "Inventaire", "subtitle": "Gérer vos articles d'inventaire", "reserved": "Réservé", "available": "Disponible", "locationLevels": "Emplacements", "associatedVariants": "Variantes associées", "manageLocations": "<PERSON><PERSON><PERSON> les emplacements", "deleteWarning": "Vous êtes sur le point de supprimer un article d'inventaire. Cette action ne peut être annulée.", "editItemDetails": "Modifier les détails de l'article", "create": {"title": "C<PERSON>er un article d'inventaire", "details": "Details", "availability": "Disponibilité", "locations": "Emplacements", "attributes": "Attributs", "requiresShipping": "Nécessite l'expédition", "requiresShippingHint": "Le produit d'inventaire nécessite-t-il une expédition ?", "successToast": "Article d'inventaire créé avec succès."}, "reservation": {"header": "Reservation of {{itemName}}", "editItemDetails": "Modifier la réservation", "lineItemId": "ID de ligne", "orderID": "ID de la commande", "description": "Description", "location": "Emplacement", "inStockAtLocation": "En stock à cet emplacement", "availableAtLocation": "Disponible à cet emplacement", "reservedAtLocation": "Réservé à cet emplacement", "reservedAmount": "<PERSON><PERSON>", "create": "C<PERSON>er une réservation", "itemToReserve": "Article à réserver", "quantityPlaceholder": "<PERSON>mbien voulez-vous réserver ?", "descriptionPlaceholder": "Quel type de réservation est celle-ci ?", "successToast": "Réservation créée avec succès.", "updateSuccessToast": "Réservation modifiée avec succès.", "deleteSuccessToast": "Réservation supprimée avec succès.", "errors": {"noAvaliableQuantity": "Emplacement de stock sans quantité disponible.", "quantityOutOfRange": "Quantité minimale est 1 et maximum est {{max}}"}}, "toast": {"updateLocations": "Locations updated successfully.", "updateLevel": "Inventaire mis à jour avec succès.", "updateItem": "Article d'inventaire mis à jour avec succès."}}, "giftCards": {"domain": "Gift Cards", "editGiftCard": "Modifier la carte cadeau", "createGiftCard": "<PERSON><PERSON><PERSON> une carte cadeau", "createGiftCardHint": "<PERSON><PERSON><PERSON> manuellement une carte cadeau qui peut être utilisée comme moyen de paiement dans votre boutique.", "selectRegionFirst": "Sélectionnez une région d'abord", "deleteGiftCardWarning": "Vous êtes sur le point de supprimer la carte cadeau {{code}}. Cette action ne peut être annulée.", "balanceHigherThanValue": "Le solde ne peut être supérieur au montant initial.", "balanceLowerThanZero": "Le solde ne peut être négatif.", "expiryDateHint": "Les pays ont des lois différentes concernant les dates d'expiration des cartes cadeaux. Assurez-vous de vérifier les régulations locales avant de définir une date d'expiration.", "regionHint": "Changer la région de la carte cadeau changera également sa devise, potentiellement affectant sa valeur monétaire.", "enabledHint": "Spécifiez si la carte cadeau est activée ou désactivée.", "balance": "Solde", "currentBalance": "Solde actuel", "initialBalance": "Solde initial", "personalMessage": "Message personnel", "recipient": "<PERSON><PERSON><PERSON>"}, "customers": {"domain": "Clients", "list": {"noRecordsMessage": "Vos clients apparaîtront ici."}, "create": {"header": "<PERSON><PERSON><PERSON> un client", "hint": "<PERSON><PERSON>er un nouveau client et gérer leurs détails.", "successToast": "Client {{email}} c<PERSON><PERSON> avec succès."}, "groups": {"label": "Groupes de clients", "remove": "Êtes-vous sûr de vouloir supprimer le client de \"{{name}}\" groupe de clients ?", "removeMany": "Êtes-vous sûr de vouloir supprimer le client de les groupes de clients suivants : {{groups}} ?", "alreadyAddedTooltip": "Le client est déjà dans ce groupe de clients.", "list": {"noRecordsMessage": "Ce client n'appartient à aucun groupe."}, "add": {"success": "Client ajouté au groupe : {{groups}}.", "list": {"noRecordsMessage": "Veuillez créer un groupe de clients d'abord."}}, "removed": {"success": "Client retiré du groupe : {{groups}}.", "list": {"noRecordsMessage": "Veuillez créer un groupe de clients d'abord."}}}, "edit": {"header": "Editer un client", "emailDisabledTooltip": "Le courriel ne peut être modifié pour les clients enregistrés.", "successToast": "Client {{email}} modifié avec succès."}, "delete": {"title": "Supprimer un client", "description": "Vous êtes sur le point de supprimer le client {{email}}. Cette action ne peut être annulée.", "successToast": "Client {{email}} supprimé avec succès."}, "fields": {"guest": "Invi<PERSON>", "registered": "Enregistré", "groups": "Groupes"}, "registered": "Enregistré", "guest": "Invi<PERSON>", "hasAccount": "A un compte"}, "customerGroups": {"domain": "Groupes de clients", "subtitle": "Organisez les clients en groupes. Les groupes peuvent avoir des promotions et des prix différents.", "create": {"header": "Créer un groupe de clients", "hint": "Créer un nouveau groupe de clients pour segmenter vos clients.", "successToast": "Groupe de clients {{name}} c<PERSON><PERSON> avec succès."}, "edit": {"header": "Editer un groupe de clients", "successToast": "Groupe de clients {{name}} modifié avec succès."}, "delete": {"title": "Delete Customer Group", "description": "Vous êtes sur le point de supprimer le groupe de clients {{name}}. Cette action ne peut être annulée.", "successToast": "Groupe de clients {{name}} supprimé avec succès."}, "customers": {"alreadyAddedTooltip": "The customer has already been added to the group.", "add": {"successToast_one": "Client ajouté au groupe avec succès.", "successToast_other": "Clients ajoutés au groupe avec succès.", "list": {"noRecordsMessage": "<PERSON><PERSON><PERSON> un client d'abord."}}, "remove": {"title_one": "Supprimer un client", "title_other": "Supprimer des clients", "description_one": "Vous êtes sur le point de supprimer {{count}} client du groupe de clients. Cette action ne peut être annulée.", "description_other": "Vous êtes sur le point de supprimer {{count}} clients du groupe de clients. Cette action ne peut être annulée."}, "list": {"noRecordsMessage": "Ce groupe n'a pas de clients."}}}, "orders": {"domain": "Commandes", "claim": "Réclamation", "exchange": "Échange", "return": "Retour", "cancelWarning": "Vous êtes sur le point de supprimer la commande {{id}}. Cette action ne peut être annulée.", "onDateFromSalesChannel": "{{date}} de {{salesChannel}}", "list": {"noRecordsMessage": "Vos commandes apparaîtront ici."}, "summary": {"requestReturn": "Demander un retour", "allocateItems": "Allouer des articles", "editOrder": "Editer la commande", "editOrderContinue": "Continuer l'édition de la commande", "inventoryKit": "Consiste de {{count}}x articles d'inventaire", "itemTotal": "Total des articles", "shippingTotal": "Total des frais de livraison", "discountTotal": "Total des remises", "taxTotalIncl": "Total des taxes (inclues)", "itemSubtotal": "Sous-total des articles", "shippingSubtotal": "Sous-total des frais de livraison", "discountSubtotal": "Sous-total des remises", "taxTotal": "Total des taxes"}, "transfer": {"title": "Transfert de propriété", "requestSuccess": "La demande de transfert de la commande à été envoyé à: {{email}}.", "currentOwner": "Propriétaire actuel", "newOwner": "Nouveau propriétaire", "currentOwnerDescription": "Le client actuellement lié à cette commande.", "newOwnerDescription": "Le client à qui transferer cette commande."}, "payment": {"title": "Paiements", "isReadyToBeCaptured": "Le paiement <0/> est prêt à être capturé.", "totalPaidByCustomer": "Total payé par le client", "capture": "Capture du paiement", "capture_short": "Capture", "refund": "Remboursement", "markAsPaid": "Marquer comme payé", "statusLabel": "Statut du paiement", "statusTitle": "Statut du paiement", "status": {"notPaid": "Non payé", "authorized": "Autorisé", "partiallyAuthorized": "Partiellement autorisé", "awaiting": "En attente", "captured": "Capturé", "partiallyRefunded": "Partiellement rembo<PERSON>", "partiallyCaptured": "Partiellement capturé", "refunded": "Re<PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Requiert une action"}, "capturePayment": "Le paiement de {{amount}} sera capturé.", "capturePaymentSuccess": "Paiement de {{amount}} capturé avec succès", "markAsPaidPayment": "Le paiement de {{amount}} sera marqué comme payé.", "markAsPaidPaymentSuccess": "Paiement de {{amount}} marqué comme payé avec succès", "createRefund": "Créer un remboursement", "refundPaymentSuccess": "Remboursement de {{amount}} r<PERSON><PERSON><PERSON>", "createRefundWrongQuantity": "La quantité doit être un nombre entre 1 et {{number}}", "refundAmount": "Remboursement de {{amount}}", "paymentLink": "Copier le lien de paiement pour {{amount}}", "selectPaymentToRefund": "Sélectionner le paiement à rembourser"}, "edits": {"title": "Editer la commande", "confirm": "Confirmer l'édition", "confirmText": "Vous êtes sur le point de confirmer une édition de commande. Cette action ne peut être annulée.", "cancel": "Annuler l'édition", "currentItems": "Articles actuels", "currentItemsDescription": "Ajuster la quantité des articles ou les supprimer.", "addItemsDescription": "Vous pouvez ajouter de nouveaux articles à la commande.", "addItems": "Ajouter des articles", "amountPaid": "<PERSON><PERSON> payé", "newTotal": "Nouveau total", "differenceDue": "Différence due", "create": "Editer la commande", "currentTotal": "Total actuel", "noteHint": "Ajouter une note interne pour l'édition", "cancelSuccessToast": "Édition de commande annulée", "createSuccessToast": "Édition de commande demandée", "activeChangeError": "Il existe déjà une édition active sur la commande (retour, réclamation, échange etc.). Veuillez terminer ou annuler l'édition avant d'éditer la commande.", "panel": {"title": "Édition de commande demandée", "titlePending": "Édition de commande en attente"}, "toast": {"canceledSuccessfully": "Édition de commande annulée", "confirmedSuccessfully": "Édition de commande confirmée"}, "validation": {"quantityLowerThanFulfillment": "Ne peut pas définir la quantité à être inférieure ou égale à la quantité livrée"}}, "returns": {"create": "<PERSON><PERSON><PERSON> un retour", "confirm": "Confirm<PERSON> le retour", "confirmText": "Vous êtes sur le point de confirmer un retour. Cette action ne peut être annulée.", "inbound": "Entrant", "outbound": "Sortant", "sendNotification": "Envoyer une notification", "sendNotificationHint": "Notifier le client concernant le retour.", "returnTotal": "Total du retour", "inboundTotal": "Total entrant", "refundAmount": "Montant du remboursement", "outstandingAmount": "<PERSON><PERSON> imp<PERSON>", "reason": "<PERSON>son", "reasonHint": "Choisir la raison pour laquelle le client souhaite retourner des articles.", "note": "Note", "noInventoryLevel": "Pas de niveau d'inventaire", "noInventoryLevelDesc": "La localisation sélectionnée n'a pas de niveau d'inventaire pour les articles sélectionnés. Le retour peut être demandé mais ne peut être reçu que lorsqu'un niveau d'inventaire est créé pour la localisation sélectionnée.", "noteHint": "Vous pouvez taper librement si vous souhaitez spécifier quelque chose.", "location": "Emplacement", "locationHint": "Choisir l'emplacement auquel vous souhaitez retourner les articles.", "inboundShipping": "Fr<PERSON> de retour", "inboundShippingHint": "Choisir le mode de livraison que vous souhaitez utiliser.", "returnableQuantityLabel": "Quantité retournable", "refundableAmountLabel": "Montant remboursable", "returnRequestedInfo": "{{requestedItemsCount}}x articles retournés demandés", "returnReceivedInfo": "{{requestedItemsCount}}x articles retournés reçus", "itemReceived": "Articles reçus", "returnRequested": "Retour demandé", "damagedItemReceived": "Articles endommagés reçus", "damagedItemsReturned": "{{quantity}}x articles endommagés retournés", "activeChangeError": "Il existe déjà une édition active sur la commande (retour, réclamation, échange etc.). Veuillez terminer ou annuler l'édition avant d'éditer la commande.", "cancel": {"title": "<PERSON><PERSON><PERSON> le retour", "description": "Êtes-vous sûr de vouloir annuler la demande de retour ?"}, "placeholders": {"noReturnShippingOptions": {"title": "Aucune option de retour trouvée", "hint": "Aucune option de retour n'a été créée pour la localisation. Vous pouvez créer une option à <LinkComponent>Localisation & Livraison</LinkComponent>."}, "outboundShippingOptions": {"title": "Aucune option de livraison sortante trouvée", "hint": "Aucune option de livraison sortante n'a été créée pour la localisation. Vous pouvez créer une option à <LinkComponent>Localisation & Livraison</LinkComponent>."}}, "receive": {"action": "Recevoir des articles", "receiveItems": "{{returnType}} {{id}}", "restockAll": "Réapprovisionner tous les articles", "itemsLabel": "Articles reçus", "title": "Recevoir des articles pour #{{returnId}}", "sendNotificationHint": "Notifier le client concernant le retour.", "inventoryWarning": "Veuillez noter que nous ajusterons automatiquement les niveaux d'inventaire en fonction de vos entrées ci-dessus.", "writeOffInputLabel": "Combien d'articles sont endommagés ?", "toast": {"success": "Retour reçu avec succès.", "errorLargeValue": "La quantité est supérieure à la quantité d'articles demandée.", "errorNegativeValue": "La quantité ne peut être négative.", "errorLargeDamagedValue": "La quantité d'articles endommagés + la quantité d'articles reçus non endommagés dépasse la quantité totale d'articles sur le retour. Veuillez diminuer la quantité d'articles non endommagés."}}, "toast": {"canceledSuccessfully": "Retour annulé avec succès", "confirmedSuccessfully": "Return confirmed successfully"}, "panel": {"title": "Retour demandé", "description": "Il existe une demande de retour à compléter"}}, "claims": {"create": "<PERSON><PERSON><PERSON> une réclamation", "confirm": "Confirmer la réclamation", "confirmText": "Vous êtes sur le point de confirmer une réclamation. Cette action ne peut être annulée.", "manage": "<PERSON><PERSON><PERSON> la ré<PERSON>", "outbound": "Sortant", "outboundItemAdded": "{{itemsCount}}x ajoutés via la réclamation", "outboundTotal": "Total sortant", "outboundShipping": "Frais de livraison sortante", "outboundShippingHint": "Choisir le mode de livraison que vous souhaitez utiliser.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> estimée", "activeChangeError": "Il existe déjà une édition active sur la commande (retour, réclamation, échange etc.). Veuillez terminer ou annuler l'édition avant d'éditer la commande.", "actions": {"cancelClaim": {"successToast": "Réclamation annulée avec succès."}}, "cancel": {"title": "Annuler la réclamation", "description": "Êtes-vous sûr de vouloir annuler la réclamation ?"}, "tooltips": {"onlyReturnShippingOptions": "Cette liste ne contient que des options de retour."}, "toast": {"canceledSuccessfully": "Réclamation annulée avec succès", "confirmedSuccessfully": "Réclamation confirmée avec succès"}, "panel": {"title": "Réclamation demandée", "description": "Il existe une demande de réclamation à compléter"}}, "exchanges": {"create": "<PERSON><PERSON><PERSON> un échange", "manage": "<PERSON><PERSON><PERSON> l'échange", "confirm": "Confirmer l'échange", "confirmText": "Vous êtes sur le point de confirmer un échange. Cette action ne peut être annulée.", "outbound": "Sortant", "outboundItemAdded": "{{itemsCount}}x ajoutés via l'échange", "outboundTotal": "Total sortant", "outboundShipping": "Frais de livraison sortante", "outboundShippingHint": "Choose which method you want to use.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> estimée", "activeChangeError": "Il existe déjà une édition active sur la commande (retour, réclamation, échange etc.). Veuillez terminer ou annuler l'édition avant d'éditer la commande.", "actions": {"cancelExchange": {"successToast": "Échange annulé avec succès."}}, "cancel": {"title": "Annuler l'échange", "description": "Êtes-vous sûr de vouloir annuler l'échange ?"}, "tooltips": {"onlyReturnShippingOptions": "Cette liste ne contient que des options de retour."}, "toast": {"canceledSuccessfully": "Échange annulé avec succès", "confirmedSuccessfully": "Échange confirmé avec succès"}, "panel": {"title": "É<PERSON>e <PERSON>", "description": "Il existe une demande d'échange à compléter"}}, "reservations": {"allocatedLabel": "Allou<PERSON>", "notAllocatedLabel": "Non alloué"}, "allocateItems": {"action": "Allouer des articles", "title": "Allouer les articles de la commande", "locationDescription": "Choisir l'emplacement à partir duquel allouer les articles.", "itemsToAllocate": "Articles à allouer", "itemsToAllocateDesc": "Sélectionner le nombre d'articles que vous souhaitez allouer", "search": "Rechercher des articles", "consistsOf": "Consiste de {{num}}x articles d'inventaire", "requires": "Requiert {{num}} par variant", "toast": {"created": "Articles alloués avec succès"}, "error": {"quantityNotAllocated": "Il existe des articles non alloués."}}, "shipment": {"title": "Marquer l'expédition comme expédiée", "trackingNumber": "Numéro de suivi", "addTracking": "Ajouter un numéro de suivi", "sendNotification": "Envoyer une notification", "sendNotificationHint": "Notifier le client concernant cette expédition.", "toastCreated": "Expédition créée avec succès."}, "fulfillment": {"cancelWarning": "Vous êtes sur le point d'annuler une expédition. Cette action ne peut être annulée.", "markAsDeliveredWarning": "Vous êtes sur le point de marquer l'expédition comme livrée. Cette action ne peut être annulée.", "unfulfilledItems": "Articles non expédiés", "statusLabel": "Statut de l'expédition", "statusTitle": "Statut de l'expédition", "fulfillItems": "Expédier des articles", "awaitingFulfillmentBadge": "En attente d'expédition", "requiresShipping": "Requiert une expédition", "number": "Expédition #{{number}}", "itemsToFulfill": "Articles à expédier", "create": "C<PERSON>er une expédition", "available": "Disponible", "inStock": "En stock", "markAsShipped": "Marquer comme expédiée", "markAsDelivered": "Marquer comme livrée", "itemsToFulfillDesc": "Choisir les articles et les quantités à expédier", "locationDescription": "Choisir l'emplacement à partir duquel expédier les articles.", "sendNotificationHint": "Notifier le client concernant l'expédition créée.", "methodDescription": "Choisir un mode de livraison différent de celui sélectionné par le client", "error": {"wrongQuantity": "Seul un article est disponible pour l'expédition", "wrongQuantity_other": "La quantité doit être un nombre entre 1 et {{number}}", "noItems": "Aucun article à expédier."}, "status": {"notFulfilled": "Non expédié", "partiallyFulfilled": "Partiellement expédié", "fulfilled": "Expédié", "partiallyShipped": "Partiellement expédié", "shipped": "Expédié", "delivered": "Livré", "partiallyDelivered": "Partiellement livré", "partiallyReturned": "Partiellement retourné", "returned": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON><PERSON>", "requiresAction": "Requiert une action"}, "toast": {"created": "Expédition créée avec succès", "canceled": "Expédition annulée avec succès", "fulfillmentShipped": "Ne peut annuler une expédition déjà expédiée", "fulfillmentDelivered": "Expédition marquée comme livrée avec succès"}, "trackingLabel": "<PERSON><PERSON><PERSON>", "shippingFromLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemsLabel": "Articles"}, "refund": {"title": "Créer un remboursement", "sendNotificationHint": "Notifier le client concernant le remboursement créé.", "systemPayment": "Paiement système", "systemPaymentDesc": "Un ou plusieurs de vos paiements est un paiement système. Soyez conscient que les captures et les remboursements ne sont pas gérés par Medusa pour de tels paiements.", "error": {"amountToLarge": "Ne peut rembourser plus que le montant de la commande originale.", "amountNegative": "Le montant du remboursement doit être un nombre positif.", "reasonRequired": "Veuillez sélectionner une raison de remboursement."}}, "customer": {"contactLabel": "Contact", "editEmail": "Modifier l'email", "transferOwnership": "Transférer la propriété", "editBillingAddress": "Modifier l'adresse de facturation", "editShippingAddress": "Modifier l'adresse de livraison"}, "activity": {"header": "Activité", "showMoreActivities_one": "Afficher {{count}} activités supplémentaires", "showMoreActivities_other": "Afficher {{count}} activités supplémentaires", "comment": {"label": "Commentaire", "placeholder": "Laisser un commentaire", "addButtonText": "Ajouter un commentaire", "deleteButtonText": "Supp<PERSON><PERSON> le commentaire"}, "from": "<PERSON><PERSON><PERSON>", "to": "À", "events": {"common": {"toReturn": "<PERSON> retourner", "toSend": "À expédier"}, "placed": {"title": "Commande passée", "fromSalesChannel": "depuis {{salesChannel}}"}, "canceled": {"title": "Commande annulée"}, "payment": {"awaiting": "En attente de paiement", "captured": "Pa<PERSON><PERSON> capturé", "canceled": "Paiement annulé", "refunded": "<PERSON><PERSON><PERSON> rembo<PERSON>"}, "fulfillment": {"created": "Articles expédiés", "canceled": "Expédition annulée", "shipped": "Articles expédiés", "delivered": "Articles livrés", "items_one": "{{count}} article", "items_other": "{{count}} articles"}, "return": {"created": "Retour #{{returnId}} demandé", "canceled": "Retour #{{returnId}} annulé", "received": "Retour #{{returnId}} reçu", "items_one": "{{count}} article retourné", "items_other": "{{count}} articles retournés"}, "note": {"comment": "Commentaire", "byLine": "par {{author}}"}, "claim": {"created": "Réclamation #{{claimId}} demandée", "canceled": "Réclamation #{{claimId}} annulée", "itemsInbound": "{{count}} article à retourner", "itemsOutbound": "{{count}} article à expédier"}, "exchange": {"created": "Échange #{{exchangeId}} demandé", "canceled": "Échange #{{exchangeId}} annulé", "itemsInbound": "{{count}} article à retourner", "itemsOutbound": "{{count}} article à expédier"}, "edit": {"requested": "Commande modifiée #{{editId}} demandée", "confirmed": "Commande modifiée #{{editId}} confirmée"}, "transfer": {"requested": "Transfert de la commande #{{transferId}} demandé", "confirmed": "Transfert de la commande #{{transferId}} confirmé", "declined": "Transfert de la commande #{{transferId}} refusé"}}}, "fields": {"displayId": "Display ID", "refundableAmount": "Montant remboursable", "returnableQuantity": "Quantité retournable"}}, "draftOrders": {"domain": "Commandes à rédiger", "deleteWarning": "Vous êtes sur le point de supprimer la commande à rédiger {{id}}. Cette action ne peut être annulée.", "paymentLinkLabel": "Lien de paiement", "cartIdLabel": "ID de panier", "markAsPaid": {"label": "Marquer comme payée", "warningTitle": "Marquer comme payée", "warningDescription": "Vous êtes sur le point de marquer la commande à rédiger comme payée. Cette action ne peut être annulée, et la collecte de paiement ne sera pas possible plus tard."}, "status": {"open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Terminée"}, "create": {"createDraftOrder": "<PERSON><PERSON><PERSON> un brouillon de commande", "createDraftOrderHint": "<PERSON><PERSON>er un nouveau brouillon de commande pour gérer les détails d'une commande avant qu'elle ne soit passée.", "chooseRegionHint": "Choisir une région", "existingItemsLabel": "Articles existants", "existingItemsHint": "Ajouter des articles existants au brouillon de commande.", "customItemsLabel": "Articles personnalisés", "customItemsHint": "Ajouter des articles personnalisés au brouillon de commande.", "addExistingItemsAction": "Ajouter des articles existants", "addCustomItemAction": "Ajouter un article personnalisé", "noCustomItemsAddedLabel": "Aucun article personnalisé ajouté pour le moment", "noExistingItemsAddedLabel": "Aucun article existant ajouté pour le moment", "chooseRegionTooltip": "Choisir une région d'abord", "useExistingCustomerLabel": "Utiliser un client existant", "addShippingMethodsAction": "Ajouter des méthodes de livraison", "unitPriceOverrideLabel": "Prix unitaire remplacé", "shippingOptionLabel": "Option de livraison", "shippingOptionHint": "Choisir l'option de livraison pour le brouillon de commande.", "shippingPriceOverrideLabel": "Prix de livraison remplacé", "shippingPriceOverrideHint": "Remplacer le prix de livraison pour le brouillon de commande.", "sendNotificationLabel": "Envoyer une notification", "sendNotificationHint": "Envoyer une notification au client lors de la création du brouillon de commande."}, "validation": {"requiredEmailOrCustomer": "<PERSON><PERSON> ou client requis.", "requiredItems": "Au moins un article est requis.", "invalidEmail": "L'email doit être une adresse email valide."}}, "stockLocations": {"domain": "Emplacements & Livraison", "list": {"description": "Gérer les emplacements de stock et les options de livraison de votre boutique."}, "create": {"header": "Créer un emplacement de stock", "hint": "Un emplacement de stock est un site physique où les produits sont stockés et expédiés.", "successToast": "Emplacement {{name}} c<PERSON><PERSON> avec succès."}, "edit": {"header": "Modifier l'emplacement de stock", "viewInventory": "Voir le stock", "successToast": "Emplacement {{name}} modifié avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer l'emplacement de stock {{name}}. Cette action ne peut être annulée."}, "fulfillmentProviders": {"header": "Fournisseurs de livraison", "shippingOptionsTooltip": "Ce menu déroulant ne contient que les fournisseurs activés pour cet emplacement. Ajoutez-les à l'emplacement si le menu déroulant est désactivé.", "label": "Fournisseurs de livraison connectés", "connectedTo": "Connecté à {{count}} de {{total}} fournisseurs de livraison", "noProviders": "Cet emplacement de stock n'est connecté à aucun fournisseur de livraison.", "action": "Connecter les fournisseurs", "successToast": "Fournisseurs de livraison pour l'emplacement de stock mis à jour avec succès."}, "fulfillmentSets": {"pickup": {"header": "Retrait"}, "shipping": {"header": "<PERSON><PERSON><PERSON>"}, "disable": {"confirmation": "Êtes-vous sûr de vouloir désactiver {{name}} ? Cette action supprimera tous les zones de service et les options de livraison associées et ne peut être annulée.", "pickup": "Retrait désactivé avec succès.", "shipping": "Livraison désactivée avec succès."}, "enable": {"pickup": "Retrait activé avec succès.", "shipping": "Livraison activée avec succès."}}, "sidebar": {"header": "Configuration de la livraison", "shippingProfiles": {"label": "<PERSON><PERSON> de livraison", "description": "Grouper les produits par besoins de livraison"}}, "salesChannels": {"header": "Canaux de vente", "label": "Canaux de vente connectés", "connectedTo": "Connecté à {{count}} de {{total}} canaux de vente", "noChannels": "L'emplacement n'est connecté à aucun canal de vente.", "action": "Connecter les canaux de vente", "successToast": "Canaux de vente mis à jour avec succès."}, "shippingOptions": {"create": {"shipping": {"header": "<PERSON><PERSON>er une option de livraison pour {{zone}}", "hint": "Créer une nouvelle option de livraison pour définir comment les produits sont expédiés depuis cet emplacement.", "label": "Options de livraison", "successToast": "Option de livraison {{name}} c<PERSON><PERSON> avec succès."}, "returns": {"header": "<PERSON><PERSON>er une option de retour pour {{zone}}", "hint": "C<PERSON>er une nouvelle option de retour pour définir comment les produits sont retournés à cet emplacement.", "label": "Options de retour", "successToast": "Option de retour {{name}} c<PERSON><PERSON> avec succès."}, "tabs": {"details": "Détails", "prices": "Prix"}, "action": "<PERSON><PERSON>er une option"}, "delete": {"confirmation": "Vous êtes sur le point de supprimer l'option de livraison {{name}}. Cette action ne peut être annulée.", "successToast": "Option de livraison {{name}} supprimée avec succès."}, "edit": {"header": "Modifier l'option de livraison", "action": "Modifier l'option", "successToast": "Option de livraison {{name}} modifiée avec succès."}, "pricing": {"action": "Modifier les prix"}, "fields": {"count": {"shipping_one": "{{count}} shipping option", "shipping_other": "{{count}} options de livraison", "returns_one": "{{count}} option de retour", "returns_other": "{{count}} options de retour"}, "priceType": {"label": "Type de prix", "options": {"fixed": {"label": "Fixe", "hint": "Le prix de l'option de livraison est fixe et ne change pas en fonction du contenu de la commande."}, "calculated": {"label": "Calculé", "hint": "Le prix de l'option de livraison est calculé par le fournisseur de livraison lors de la passation de commande."}}}, "enableInStore": {"label": "Activer en magasin", "hint": "Indique si les clients peuvent utiliser cette option lors de la passation de commande."}, "provider": "Fournisseur de livrai<PERSON>", "profile": "<PERSON><PERSON> <PERSON>"}}, "serviceZones": {"create": {"headerPickup": "Créer une zone de service pour le retrait depuis {{location}}", "headerShipping": "Créer une zone de service pour la livraison depuis {{location}}", "action": "Créer une zone de service", "successToast": "Zone de service {{name}} créée avec succès."}, "edit": {"header": "Modifier la zone de service", "successToast": "La zone de service {{name}} a été modifiée avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer la zone de service {{name}}. Cette action ne peut être annulée.", "successToast": "La zone de service {{name}} a été supprimée avec succès."}, "manageAreas": {"header": "<PERSON><PERSON>rer les zones pour {{name}}", "action": "Gérer les zones", "label": "Zones", "hint": "Sélectionner les zones géographiques que la zone de service couvre.", "successToast": "Les zones pour {{name}} ont été mises à jour avec succès."}, "fields": {"noRecords": "Il n'y a pas de zones de service pour ajouter des options de livraison.", "tip": "Une zone de service est une collection de zones géographiques ou d'aires. Elle est utilisée pour restreindre les options de livraison disponibles à un ensemble de lieux défini."}}}, "shippingProfile": {"domain": "<PERSON><PERSON> de livraison", "subtitle": "Regrouper les produits avec des besoins de livraison similaires dans des profils.", "create": {"header": "<PERSON><PERSON>er un profil de livraison", "hint": "Créer un nouveau profil de livraison pour regrouper les produits avec des besoins de livraison similaires.", "successToast": "Le profil de livraison {{name}} a été créé avec succès."}, "delete": {"title": "Supprimer le profil de livraison", "description": "Vous êtes sur le point de supprimer le profil de livraison {{name}}. Cette action ne peut être annulée.", "successToast": "Le profil de livraison {{name}} a été supprimé avec succès."}, "tooltip": {"type": "Entrer le type de profil de livraison, par exemple : Po<PERSON> lourd, Surdimensionné, Livraison exclusive, etc."}}, "taxRegions": {"domain": "Taxes par régions", "list": {"hint": "Gérer les taxes que vous facturez à vos clients lorsqu'ils achètent depuis différents pays et régions."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer une taxe. Cette action ne peut être annulée.", "successToast": "La taxe a été supprimée avec succès."}, "create": {"header": "<PERSON><PERSON>er une taxe", "hint": "C<PERSON>er une nouvelle taxe pour définir les taux de taxe pour un pays spécifique.", "errors": {"rateIsRequired": "Le taux de taxe est requis lors de la création d'une taxe par défaut.", "nameIsRequired": "Le nom est requis lors de la création d'une taxe par défaut."}, "successToast": "La taxe a été créée avec succès."}, "province": {"header": "Région", "create": {"header": "Créer une taxe pour la région", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour une province spécifique."}}, "state": {"header": "États", "create": {"header": "C<PERSON>er une taxe pour les états", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour un état spécifique."}}, "stateOrTerritory": {"header": "États ou territoires", "create": {"header": "C<PERSON>er une taxe pour les états ou territoires", "hint": "Create a new tax region to define tax rates for a specific state/territory."}}, "county": {"header": "Comtés", "create": {"header": "C<PERSON>er une taxe pour les comtés", "hint": "C<PERSON>er une nouvelle taxe pour définir les taux de taxe pour un comté spécifique."}}, "region": {"header": "Régions", "create": {"header": "Créer une taxe pour les régions", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour une région spécifique."}}, "department": {"header": "Départements", "create": {"header": "Créer une taxe pour les départements", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour un département spécifique."}}, "territory": {"header": "Territories", "create": {"header": "C<PERSON>er une taxe pour les territoires", "hint": "C<PERSON>er une nouvelle taxe pour définir les taux de taxe pour un territoire spécifique."}}, "prefecture": {"header": "Préfectures", "create": {"header": "Créer une taxe pour les préfectures", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour une préfecture spécifique."}}, "district": {"header": "Quartiers", "create": {"header": "C<PERSON>er une taxe pour les quartiers", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour un district spécifique."}}, "governorate": {"header": "Gouvernorats", "create": {"header": "C<PERSON>er une taxe pour les gouvernorats", "hint": "C<PERSON>er une nouvelle taxe pour définir les taux de taxe pour un gouvernorat spécifique."}}, "canton": {"header": "Cantons", "create": {"header": "Créer une taxe pour les cantons", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour un canton spécifique."}}, "emirate": {"header": "Emirates", "create": {"header": "Créer une taxe pour les émirats", "hint": "C<PERSON>er une nouvelle taxe pour définir les taux de taxe pour un émirat spécifique."}}, "sublevel": {"header": "Niveaux subordonnés", "create": {"header": "Créer une taxe pour les niveaux subordonnés", "hint": "Créer une nouvelle taxe pour définir les taux de taxe pour un niveau subordonné spécifique."}}, "taxOverrides": {"header": "Overrides", "create": {"header": "<PERSON><PERSON><PERSON> une surcharge", "hint": "C<PERSON>er une taxe qui surcharge les taux de taxe par défaut pour les conditions sélectionnées."}, "edit": {"header": "Modifier la surcharge", "hint": "Modifier la taxe qui surcharge les taux de taxe par défaut pour les conditions sélectionnées."}}, "taxRates": {"create": {"header": "<PERSON><PERSON><PERSON> un taux d'imposition", "hint": "Créer une nouvelle taxe pour définir le taux de taxe pour une région.", "successToast": "Taxe créée avec succès."}, "edit": {"header": "Modifier la taxe", "hint": "Modifier la taxe pour définir le taux de taxe pour une région.", "successToast": "Taxe modifiée avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer la taxe {{name}}. Cette action ne peut être annulée.", "successToast": "La taxe a été supprimée avec succès."}}, "fields": {"isCombinable": {"label": "Combinable", "hint": "Indique si cette taxe peut être combinée avec le taux de taxe par défaut de la région.", "true": "Combinable", "false": "Non combinable"}, "defaultTaxRate": {"label": "Taux de taxe par défaut", "tooltip": "Le taux de taxe par défaut pour cette région. Un exemple est le taux de TVA standard pour un pays ou une région.", "action": "<PERSON><PERSON>er un taux de taxe par défaut"}, "taxRate": "Taux de taxe", "taxCode": "Code de taxe", "targets": {"label": "Cibles", "hint": "Sélectionner les cibles à laquelle cette taxe s'appliquera.", "options": {"product": "Produits", "productCollection": "Collections de produits", "productTag": "Tags de produits", "productType": "Types de produits", "customerGroup": "Groupes de clients"}, "operators": {"in": "dans", "on": "sur", "and": "et"}, "placeholders": {"product": "Rechercher des produits", "productCollection": "Rechercher des collections de produits", "productTag": "Rechercher des tags de produits", "productType": "Rechercher des types de produits", "customerGroup": "Rechercher des groupes de clients"}, "tags": {"product": "Produit", "productCollection": "Collection de produits", "productTag": "Tag de produit", "productType": "Type de produit", "customerGroup": "Groupe de clients"}, "modal": {"header": "Ajouter des cibles"}, "values_one": "{{count}} valeur", "values_other": "{{count}} valeurs", "numberOfTargets_one": "{{count}} cible", "numberOfTargets_other": "{{count}} cibles", "additionalValues_one": "et {{count}} plus de valeur", "additionalValues_other": "et {{count}} plus de valeurs", "action": "Ajouter une cible"}, "sublevels": {"labels": {"province": "Province", "state": "État", "region": "Région", "stateOrTerritory": "État/Territoire", "department": "Département", "county": "Comté", "territory": "<PERSON><PERSON><PERSON>", "prefecture": "Préfecture", "district": "Quartier", "governorate": "Gouvernorat", "emirate": "<PERSON><PERSON><PERSON>", "canton": "Canton", "sublevel": "Code de niveau subordonné"}, "placeholders": {"province": "Select province", "state": "Sélectionner l'état", "region": "Sélectionner la région", "stateOrTerritory": "Sélectionner l'état/territoire", "department": "Sélectionner le département", "county": "Sélectionner le comté", "territory": "Sélectionner le territoire", "prefecture": "Sélectionner la préfecture", "district": "Sé<PERSON><PERSON><PERSON> le quartier", "governorate": "Sélectionner le gouvernorat", "emirate": "Sélectionner l'émirat", "canton": "Sélectionner le canton"}, "tooltips": {"sublevel": "Entrer le code ISO 3166-2 pour la région de taxe subordonnée.", "notPartOfCountry": "{{province}} ne semble pas être une partie de {{country}}. Veuillez vérifier si c'est correct."}, "alert": {"header": "Les régions subordonnées sont désactivées pour cette taxe", "description": "Les régions subordonnées sont désactivées pour cette région par défaut. Vous pouvez les activer pour créer des régions subordonnées comme des provinces, des états ou des territoires.", "action": "Activer les régions subordonnées"}}, "noDefaultRate": {"label": "Pas de taux de taxe par défaut", "tooltip": "Cette région de taxe n'a pas de taux de taxe par défaut. Si un taux standard est appliqué, comme la TVA d'un pays, veuillez l'ajouter à cette région."}}}, "promotions": {"domain": "Promotions", "sections": {"details": "Détails de la promotion"}, "tabs": {"template": "Type", "details": "Détails", "campaign": "Campagne"}, "fields": {"type": "Type", "value_type": "Type de valeur", "value": "<PERSON><PERSON>", "campaign": "Campagne", "method": "Méthode", "allocation": "Allocation", "addCondition": "Ajouter une condition", "clearAll": "Effacer tout", "amount": {"tooltip": "Sélectionner le code de devise pour activer la définition de la valeur"}, "conditions": {"rules": {"title": "Qui peut utiliser ce code?", "description": "Quel client est autorisé à utiliser le code de promotion? Le code de promotion peut être utilisé par tous les clients si laissé tel quel."}, "target-rules": {"title": "Quels articles seront appliqués à la promotion?", "description": "La promotion sera appliquée aux articles qui correspondent aux conditions suivantes."}, "buy-rules": {"title": "Quels éléments doivent être dans le panier pour déverrouiller la promotion?", "description": "Si ces conditions correspondent, nous activons la promotion sur les articles cibles."}}}, "tooltips": {"campaignType": "Le code de devise doit être sélectionné dans la promotion pour définir un budget de dépense."}, "errors": {"requiredField": "Champ requis", "promotionTabError": "Corriger les erreurs dans la promotion avant de continuer"}, "toasts": {"promotionCreateSuccess": "Promotion ({{code}}) a été créée avec succès."}, "create": {}, "edit": {"title": "Modifier les détails de la promotion", "rules": {"title": "Modifier les conditions d'utilisation"}, "target-rules": {"title": "Modifier les conditions des articles"}, "buy-rules": {"title": "Modifier les conditions d'achat"}}, "campaign": {"header": "Campaign", "edit": {"header": "Modifier la campagne", "successToast": "La campagne de la promotion a été mise à jour avec succès."}, "actions": {"goToCampaign": "Aller à la campagne"}}, "campaign_currency": {"tooltip": "C'est la devise de la promotion. Changez-la depuis l'onglet Détails."}, "form": {"required": "Requis", "and": "ET", "selectAttribute": "Sélectionner un attribut", "campaign": {"existing": {"title": "Campagne existante", "description": "Ajouter la promotion à une campagne existante.", "placeholder": {"title": "Aucune campagne existante", "desc": "Vous pouvez créer une pour suivre plusieurs promotions et définir des limites de budget."}}, "new": {"title": "Nouvelle campagne", "description": "<PERSON><PERSON>er une nouvelle campagne pour cette promotion."}, "none": {"title": "Sans campagne", "description": "Continuer sans associer la promotion à une campagne"}}, "status": {"title": "Status"}, "method": {"label": "Method", "code": {"title": "Code de promotion", "description": "Les clients doivent entrer ce code lors de la caisse"}, "automatic": {"title": "Automatique", "description": "Les clients verront cette promotion lors de la caisse"}}, "max_quantity": {"title": "Quantité maximum", "description": "Quantité maximum d'articles à laquelle cette promotion s'applique."}, "type": {"standard": {"title": "Standard", "description": "Une promotion standard"}, "buyget": {"title": "Acheter X, obt<PERSON>z Y", "description": "Achat X, obtenez Y"}}, "allocation": {"each": {"title": "<PERSON><PERSON>", "description": "Applique la valeur sur chaque article"}, "across": {"title": "Sur tous les articles", "description": "Applique la valeur sur tous les articles"}}, "code": {"title": "Code", "description": "Le code que les clients entreront lors de la caisse."}, "value": {"title": "Valeur de la promotion"}, "value_type": {"fixed": {"title": "Valeur de la promotion", "description": "La somme à être remise. Exemple : 100"}, "percentage": {"title": "Valeur de la promotion", "description": "Le pourcentage à remiser. Exemple : 8%"}}}, "deleteWarning": "Vous êtes sur le point de supprimer la promotion {{code}}. Cette action ne peut être annulée.", "createPromotionTitle": "Créer une promotion", "type": "Type de promotion", "conditions": {"add": "Add condition", "list": {"noRecordsMessage": "Ajouter une condition pour restreindre les articles à laquelle la promotion s'applique."}}}, "campaigns": {"domain": "Campagnes", "details": "<PERSON>é<PERSON> de la campagne", "status": {"active": "Active", "expired": "Expirée", "scheduled": "<PERSON><PERSON><PERSON>"}, "delete": {"title": "Êtes-vous sûr?", "description": "Vous êtes sur le point de supprimer la campagne '{{name}}'. Cette action ne peut être annulée.", "successToast": "La campagne '{{name}}' a été supprimée avec succès."}, "edit": {"header": "Modifier la campagne", "description": "Modifier les détails de la campagne.", "successToast": "La campagne '{{name}}' a été mise à jour avec succès."}, "configuration": {"header": "Configuration", "edit": {"header": "Modifier la configuration de la campagne", "description": "Modifier la configuration de la campagne.", "successToast": "La configuration de la campagne a été mise à jour avec succès."}}, "create": {"title": "<PERSON><PERSON><PERSON> une campagne", "description": "C<PERSON>er une campagne promotionnelle.", "hint": "C<PERSON>er une campagne promotionnelle.", "header": "<PERSON><PERSON><PERSON> une campagne", "successToast": "La campagne '{{name}}' a été créée avec succès."}, "fields": {"name": "Nom", "identifier": "Identifiant", "start_date": "Date de début", "end_date": "Date de fin", "total_spend": "Budget dépensé", "total_used": "Budget utilisé", "budget_limit": "Limite de budget", "campaign_id": {"hint": "Seules les campagnes avec le même code de devise que la promotion sont affichées dans cette liste."}}, "budget": {"create": {"hint": "Créer un budget pour la campagne.", "header": "Budget de la campagne"}, "details": "Budget de la campagne", "fields": {"type": "Type", "currency": "<PERSON><PERSON>", "limit": "Limite", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"spend": {"title": "Dé<PERSON>se", "description": "Définir une limite sur le montant total des remises de toutes les utilisations de la promotion."}, "usage": {"title": "Usage", "description": "Définir une limite sur le nombre de fois que la promotion peut être utilisée."}}, "edit": {"header": "Modifier le budget de la campagne"}}, "promotions": {"remove": {"title": "Supprimer la promotion de la campagne", "description": "Vous êtes sur le point de supprimer {{count}} promotion(s) de la campagne. Cette action ne peut être annulée."}, "alreadyAdded": "This promotion has already been added to the campaign.", "alreadyAddedDiffCampaign": "Cette promotion a déjà été ajoutée à une autre campagne ({{name}}).", "currencyMismatch": "La devise de la promotion et de la campagne ne correspondent pas", "toast": {"success": "Promotion(s) ajoutée(s) à la campagne avec succès"}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON>er une promotion d'abord."}}, "list": {"noRecordsMessage": "Il n'y a pas de promotions dans la campagne."}}, "deleteCampaignWarning": "Vous êtes sur le point de supprimer la campagne {{name}}. Cette action ne peut être annulée.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "Listes de prix", "subtitle": "C<PERSON>er des prix de vente ou des prix de remplacement pour des conditions spécifiques.", "delete": {"confirmation": "Vous êtes sur le point de supprimer la liste de prix {{title}}. Cette action ne peut être annulée.", "successToast": "La liste de prix {{title}} a été supprimée avec succès."}, "create": {"header": "<PERSON><PERSON><PERSON> une liste de prix", "subheader": "<PERSON><PERSON>er une nouvelle liste de prix pour gérer les prix de vos produits.", "tabs": {"details": "Détails", "products": "Produits", "prices": "Prix"}, "successToast": "La liste de prix {{title}} a été créée avec succès.", "products": {"list": {"noRecordsMessage": "<PERSON><PERSON>er un produit d'abord."}}}, "edit": {"header": "Modifier la liste de prix", "successToast": "La liste de prix {{title}} a été mise à jour avec succès."}, "configuration": {"header": "Configuration", "edit": {"header": "Modifier la configuration de la liste de prix", "description": "Modifier la configuration de la liste de prix.", "successToast": "La configuration de la liste de prix a été mise à jour avec succès."}}, "products": {"header": "Produits", "actions": {"addProducts": "Ajouter des produits", "editPrices": "Modifier les prix"}, "delete": {"confirmation_one": "Vous êtes sur le point de supprimer les prix pour {{count}} produit dans la liste de prix. Cette action ne peut être annulée.", "confirmation_other": "Vous êtes sur le point de supprimer les prix pour {{count}} produits dans la liste de prix. Cette action ne peut être annulée.", "successToast_one": "Les prix pour {{count}} produit ont été supprimés avec succès.", "successToast_other": "Les prix pour {{count}} produits ont été supprimés avec succès."}, "add": {"successToast": "Les prix ont été ajoutés à la liste de prix avec succès."}, "edit": {"successToast": "Les prix ont été mis à jour avec succès."}}, "fields": {"priceOverrides": {"label": "Prix de remplacement", "header": "Prix de remplacement"}, "status": {"label": "Status", "options": {"active": "Active", "draft": "Brouillon", "expired": "Expiré", "scheduled": "Programmé"}}, "type": {"label": "Type", "hint": "Choisir le type de liste de prix que vous souhaitez créer.", "options": {"sale": {"label": "Prix de vente", "description": "Les prix de vente sont des changements de prix temporaires pour les produits."}, "override": {"label": "Remplacement", "description": "Les remplacements sont généralement utilisés pour créer des prix spécifiques aux clients."}}}, "startsAt": {"label": "La liste de prix a une date de début?", "hint": "Planifier la liste de prix pour l'activation dans le futur."}, "endsAt": {"label": "La liste de prix a une date d'expiration?", "hint": "Planifier la liste de prix pour la désactivation dans le futur."}, "customerAvailability": {"header": "Choisir les groupes de clients", "label": "Disponibilité des clients", "hint": "Choisir les groupes de clients auxquels la liste de prix doit s'appliquer.", "placeholder": "Rechercher les groupes de clients", "attribute": "Groupes de clients"}}}, "profile": {"domain": "Profil", "manageYourProfileDetails": "<PERSON><PERSON><PERSON> les détails de votre profil.", "fields": {"languageLabel": "<PERSON><PERSON>", "usageInsightsLabel": "Données d'utilisation"}, "edit": {"header": "Modifier le profil", "languageHint": "La langue que vous souhaitez utiliser dans le tableau de bord administratif. Cela ne change pas la langue de votre boutique.", "languagePlaceholder": "Sélectionner la langue", "usageInsightsHint": "Partager les données d'utilisation et nous aider à améliorer Medusa. Vous pouvez en savoir plus sur ce que nous collectons et comment nous l'utilisons dans notre <0>documentation</0>."}, "toast": {"edit": "Modifications du profil enregistrées"}}, "users": {"domain": "Utilisateurs", "editUser": "Modifier l'utilisateur", "inviteUser": "Inviter un utilisateur", "inviteUserHint": "Inviter un nouvel utilisateur à votre boutique.", "sendInvite": "Envoyer l'invitation", "pendingInvites": "Invitations en attente", "deleteInviteWarning": "Vous êtes sur le point de supprimer l'invitation pour {{email}}. Cette action ne peut être annulée.", "resendInvite": "Renvoyer l'invitation", "copyInviteLink": "Copier le lien d'invitation", "expiredOnDate": "<PERSON><PERSON><PERSON> le {{date}}", "validFromUntil": "Valide de <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "Accepté le {{date}}", "inviteStatus": {"accepted": "Accepté", "pending": "En attente", "expired": "Expiré"}, "roles": {"admin": "Admin", "developer": "Développeur", "member": "Membre"}, "deleteUserWarning": "Vous êtes sur le point de supprimer l'utilisateur {{name}}. Cette action ne peut être annulée.", "invite": "Inviter"}, "store": {"domain": "Boutique", "manageYourStoresDetails": "<PERSON><PERSON><PERSON> les détails de votre boutique.", "editStore": "Modifier la boutique", "defaultCurrency": "<PERSON><PERSON> par défaut", "defaultRegion": "Région par défaut", "swapLinkTemplate": "Lien de remplacement", "paymentLinkTemplate": "Lien de paiement", "inviteLinkTemplate": "Lien d'invitation", "currencies": "Devi<PERSON>", "addCurrencies": "Ajouter des devises", "enableTaxInclusivePricing": "Activer le prix taxe inclusive", "disableTaxInclusivePricing": "Désactiver le prix taxe inclusive", "removeCurrencyWarning_one": "Vous êtes sur le point de supprimer {{count}} devise de votre boutique. Assurez-vous de supprimer tous les prix utilisant la devise avant de continuer.", "removeCurrencyWarning_other": "Vous êtes sur le point de supprimer {{count}} devises de votre boutique. Assurez-vous de supprimer tous les prix utilisant les devises avant de continuer.", "currencyAlreadyAdded": "La devise a déjà été ajoutée à votre boutique.", "edit": {"header": "Modifier la boutique"}, "toast": {"update": "Boutique mise à jour avec succès", "currenciesUpdated": "<PERSON><PERSON> mises à jour avec succès", "currenciesRemoved": "Devises supprimées de la boutique avec succès", "updatedTaxInclusivitySuccessfully": "Prix taxe inclusive mis à jour avec succès"}}, "regions": {"domain": "Régions", "subtitle": "Une région est une zone dans laquelle vous vendez des produits. Elle peut couvrir plusieurs pays, et a des taux de taxe, fournisseurs et devises différents.", "createRegion": "Créer une région", "createRegionHint": "G<PERSON>rer les taux de taxe et les fournisseurs pour un ensemble de pays.", "addCountries": "Ajouter des pays", "editRegion": "Modifier la région", "countriesHint": "Ajouter les pays inclus dans cette région.", "deleteRegionWarning": "Vous êtes sur le point de supprimer la région {{name}}. Cette action ne peut être annulée.", "removeCountriesWarning_one": "Vous êtes sur le point de supprimer {{count}} pays de la région. Cette action ne peut être annulée.", "removeCountriesWarning_other": "Vous êtes sur le point de supprimer {{count}} pays de la région. Cette action ne peut être annulée.", "removeCountryWarning": "Vous êtes sur le point de supprimer le pays {{name}} de la région. Cette action ne peut être annulée.", "automaticTaxesHint": "Lorsq<PERSON>'elle est activée, les taxes ne sont calculées qu'à la caisse en fonction de l'adresse de livraison.", "taxInclusiveHint": "Lorsqu'elle est activée, les prix dans la région sont taxe inclusive.", "providersHint": "Ajouter les fournisseurs de paiement disponibles dans cette région.", "shippingOptions": "Options de livraison", "deleteShippingOptionWarning": "Vous êtes sur le point de supprimer l'option de livraison {{name}}. Cette action ne peut être annulée.", "return": "Retour", "outbound": "Sortant", "priceType": "Type de prix", "flatRate": "Tarif fixe", "calculated": "Calculé", "list": {"noRecordsMessage": "Créer une région pour les zones dans lesquelles vous vendez."}, "toast": {"delete": "Région supprimée avec succès", "edit": "Région modifiée avec succès", "create": "Région créée avec succès", "countries": "Pays de la région mis à jour avec succès"}, "shippingOption": {"createShippingOption": "<PERSON><PERSON>er une option de livraison", "createShippingOptionHint": "Créer une nouvelle option de livraison pour la région.", "editShippingOption": "Modifier l'option de livraison", "fulfillmentMethod": "<PERSON><PERSON><PERSON><PERSON> de livrai<PERSON>", "type": {"outbound": "Outbound", "outboundHint": "Utiliser cette option si vous créez une option de livraison pour envoyer des produits au client.", "return": "Retour", "returnHint": "Utiliser cette option si vous créez une option de livraison pour que le client retourne des produits à vous."}, "priceType": {"label": "Type de prix", "flatRate": "Tarif fixe", "calculated": "Calculé"}, "availability": {"adminOnly": "Admin uniquement", "adminOnlyHint": "Lorsq<PERSON>'elle est activée, l'option de livraison ne sera disponible que dans le tableau de bord administratif et pas dans la boutique."}, "taxInclusiveHint": "Lorsqu'elle est activée, le prix de l'option de livraison sera taxe inclusive.", "requirements": {"label": "Conditions", "hint": "Spécifier les conditions pour l'option de livraison."}}}, "taxes": {"domain": "Régions fiscales", "domainDescription": "Gérer votre région fiscale", "countries": {"taxCountriesHint": "Les paramètres fiscaux s'appliquent aux pays listés."}, "settings": {"editTaxSettings": "Modifier les paramètres fiscaux", "taxProviderLabel": "Fournisseur", "systemTaxProviderLabel": "Fournisseur de taxes système", "calculateTaxesAutomaticallyLabel": "Calculer les taxes automatiquement", "calculateTaxesAutomaticallyHint": "Lorsqu'elle est activée, les taux de taxes seront calculés automatiquement et appliqués aux paniers. Lorsqu'elle est désactivée, les taxes doivent être calculées manuellement à la caisse. Les taxes manuelles sont recommandées pour l'utilisation avec des fournisseurs de taxes tiers.", "applyTaxesOnGiftCardsLabel": "Appliquer les taxes sur les cartes-cad", "applyTaxesOnGiftCardsHint": "Lorsqu'elle est activée, les taxes seront appliquées aux cartes-cad à la caisse. Dans certains pays, les régulations fiscales requièrent l'application de taxes aux cartes-cad lors de l'achat.", "defaultTaxRateLabel": "Taux de taxe par défaut", "defaultTaxCodeLabel": "Code de taxe par défaut"}, "defaultRate": {"sectionTitle": "Taux de taxe par défaut"}, "taxRate": {"sectionTitle": "Taux de taxe", "createTaxRate": "<PERSON><PERSON>er un taux de taxe", "createTaxRateHint": "Créer un nouveau taux de taxe pour la région.", "deleteRateDescription": "Vous êtes sur le point de supprimer le taux de taxe {{name}}. Cette action ne peut être annulée.", "editTaxRate": "Modifier le taux de taxe", "editRateAction": "Modifier le taux", "editOverridesAction": "Modifier les remplacements", "editOverridesTitle": "Modifier les remplacements", "editOverridesHint": "Spécifier les remplacements pour le taux de taxe.", "deleteTaxRateWarning": "Vous êtes sur le point de supprimer le taux de taxe {{name}}. Cette action ne peut être annulée.", "productOverridesLabel": "Remplacements de produits", "productOverridesHint": "Spécifier les remplacements de produits pour le taux de taxe.", "addProductOverridesAction": "Ajouter des remplacements de produits", "productTypeOverridesLabel": "Remplacements de types de produits", "productTypeOverridesHint": "Spécifier les remplacements de types de produits pour le taux de taxe.", "addProductTypeOverridesAction": "Ajouter des remplacements de types de produits", "shippingOptionOverridesLabel": "Remplacements d'options de livraison", "shippingOptionOverridesHint": "Spécifier les remplacements d'options de livraison pour le taux de taxe.", "addShippingOptionOverridesAction": "Ajouter des remplacements d'options de livraison", "productOverridesHeader": "Produits", "productTypeOverridesHeader": "Types de produits", "shippingOptionOverridesHeader": "Options de livraison"}}, "locations": {"domain": "Localisations", "editLocation": "Modifier la localisation", "addSalesChannels": "Ajouter des canaux de vente", "noLocationsFound": "Aucune localisation trouvée", "selectLocations": "Sélectionner les localisations qui stockent l'article.", "deleteLocationWarning": "Vous êtes sur le point de supprimer la localisation {{name}}. Cette action ne peut être annulée.", "removeSalesChannelsWarning_one": "Vous êtes sur le point de supprimer {{count}} canal de vente de la localisation.", "removeSalesChannelsWarning_other": "Vous êtes sur le point de supprimer {{count}} canaux de vente de la localisation.", "toast": {"create": "Localisation créée avec succès", "update": "Localisation mise à jour avec succès", "removeChannel": "Canal de vente supprimé avec succès"}}, "reservations": {"domain": "Réservations", "subtitle": "Gérer la quantité réservée des articles en stock.", "deleteWarning": "Vous êtes sur le point de supprimer une réservation. Cette action ne peut être annulée."}, "salesChannels": {"domain": "Canaux de vente", "subtitle": "<PERSON><PERSON><PERSON> les canaux de vente en ligne et hors ligne.", "createSalesChannel": "Créer un canal de vente", "createSalesChannelHint": "C<PERSON>er un nouveau canal de vente pour vendre vos produits.", "enabledHint": "Spécifier si le canal de vente est activé.", "removeProductsWarning_one": "Vous êtes sur le point de supprimer {{count}} produit de {{sales_channel}}.", "removeProductsWarning_other": "Vous êtes sur le point de supprimer {{count}} produits de {{sales_channel}}.", "addProducts": "Ajouter des produits", "editSalesChannel": "Modifier le canal de vente", "productAlreadyAdded": "Le produit a déjà été ajouté au canal de vente.", "deleteSalesChannelWarning": "Vous êtes sur le point de supprimer le canal de vente {{name}}. Cette action ne peut être annulée.", "toast": {"create": "Canal de vente créé avec succès", "update": "Canal de vente mis à jour avec succès", "delete": "Canal de vente supprimé avec succès"}, "products": {"list": {"noRecordsMessage": "Il n'y a aucun produit dans le canal de vente."}, "add": {"list": {"noRecordsMessage": "<PERSON><PERSON>er un produit d'abord."}}}}, "apiKeyManagement": {"domain": {"publishable": "Publishable API Keys", "secret": "Secret API Keys"}, "subtitle": {"publishable": "Gérer les clés API utilisées dans le storefront pour limiter la portée des requêtes aux canaux de vente spécifiques.", "secret": "Gérer les clés API utilisées pour authentifier les utilisateurs administratifs dans les applications administratives."}, "status": {"active": "Active", "revoked": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"publishable": "Publique", "secret": "Se<PERSON>r<PERSON><PERSON>"}, "create": {"createPublishableHeader": "Créer une clé API publique", "createPublishableHint": "Créer une nouvelle clé API publique pour limiter la portée des requêtes aux canaux de vente spécifiques.", "createSecretHeader": "<PERSON><PERSON>er une clé API secrète", "createSecretHint": "Créer une nouvelle clé API secrète pour accéder à l'API Medusa en tant qu'utilisateur administratif authentifié.", "secretKeyCreatedHeader": "<PERSON><PERSON> secrète cré<PERSON>", "secretKeyCreatedHint": "Votre nouvelle clé secrète a été générée. Copiez et stockez-la maintenant. C'est la seule fois où elle sera affichée.", "copySecretTokenSuccess": "Clé secrète copiée dans le presse-papiers.", "copySecretTokenFailure": "Erreur lors de la copie de la clé secrète dans le presse-papiers.", "successToast": "Clé API créée avec succès."}, "edit": {"header": "Modifier la clé API", "description": "Modifier le titre de la clé API.", "successToast": "Clé API {{title}} mise à jour avec succès."}, "salesChannels": {"title": "Ajouter des canaux de vente", "description": "Ajouter les canaux de vente auxquels la clé API doit être limitée.", "successToast_one": "{{count}} canal de vente a été ajouté à la clé API.", "successToast_other": "{{count}} canaux de vente ont été ajoutés à la clé API.", "alreadyAddedTooltip": "Le canal de vente a déjà été ajouté à la clé API.", "list": {"noRecordsMessage": "Il n'y a aucun canal de vente dans la portée de la clé API publique."}}, "delete": {"warning": "Vous êtes sur le point de supprimer la clé API {{title}}. Cette action ne peut être annulée.", "successToast": "Clé API {{title}} supprimée avec succès."}, "revoke": {"warning": "Vous êtes sur le point de révoquer la clé API {{title}}. Cette action ne peut être annulée.", "successToast": "Clé API {{title}} révoquée avec succès."}, "addSalesChannels": {"list": {"noRecordsMessage": "Créer un canal de vente d'abord."}}, "removeSalesChannel": {"warning": "Vous êtes sur le point de supprimer le canal de vente {{name}} de la clé API. Cette action ne peut être annulée.", "warningBatch_one": "Vous êtes sur le point de supprimer {{count}} canal de vente de la clé API. Cette action ne peut être annulée.", "warningBatch_other": "Vous êtes sur le point de supprimer {{count}} canaux de vente de la clé API. Cette action ne peut être annulée.", "successToast": "Canal de vente supprimé avec succès de la clé API.", "successToastBatch_one": "{{count}} canal de vente a été supprimé de la clé API.", "successToastBatch_other": "{{count}} canaux de vente ont été supprimés de la clé API."}, "actions": {"revoke": "Révoquer la clé API", "copy": "Copier la clé API", "copySuccessToast": "Clé API copiée dans le presse-papiers."}, "table": {"lastUsedAtHeader": "Dernière utilisation", "createdAtHeader": "Annuler à"}, "fields": {"lastUsedAtLabel": "Dernière utilisation", "revokedByLabel": "Annuler par", "revokedAtLabel": "Annuler à", "createdByLabel": "Créée par"}}, "returnReasons": {"domain": "<PERSON><PERSON> de retour", "subtitle": "<PERSON><PERSON><PERSON> les raisons des retours.", "calloutHint": "<PERSON><PERSON><PERSON> les raisons pour classer les retours.", "editReason": "Modifier la raison de retour", "create": {"header": "Ajouter une raison de retour", "subtitle": "Spécifier les raisons les plus courantes des retours.", "hint": "<PERSON><PERSON>er une nouvelle raison de retour pour classer les retours.", "successToast": "<PERSON>son de retour {{label}} c<PERSON><PERSON> avec succès."}, "edit": {"header": "Modifier la raison de retour", "subtitle": "Modifier la valeur de la raison de retour.", "successToast": "<PERSON>son de retour {{label}} mise à jour avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer la raison de retour {{label}}. Cette action ne peut être annulée.", "successToast": "<PERSON>son de retour {{label}} supprimée avec succès."}, "fields": {"value": {"label": "<PERSON><PERSON>", "placeholder": "mauvaise_taille", "tooltip": "La valeur doit être un identifiant unique pour la raison de retour."}, "label": {"label": "Label", "placeholder": "<PERSON><PERSON><PERSON><PERSON> taille"}, "description": {"label": "Description", "placeholder": "Le client a reçu la mauvaise taille"}}}, "login": {"forgotPassword": "Mot de passe oublié? - <0>Réinitialiser</0>", "title": "Bienvenue sur Medusa", "hint": "Connectez-vous pour accéder à l'espace administratif"}, "invite": {"title": "Bienvenue sur Medusa", "hint": "<PERSON><PERSON><PERSON> votre compte ci-dessous", "backToLogin": "Retour à la connexion", "createAccount": "<PERSON><PERSON><PERSON> un compte", "alreadyHaveAccount": "Vous avez déjà un compte? - <0>Se connecter</0>", "emailTooltip": "Votre email ne peut être modifié. Si vous souhaitez utiliser un autre email, un nouveau lien d'invitation doit être envoyé.", "invalidInvite": "Le lien d'invitation est invalide ou a expiré.", "successTitle": "Votre compte a été enregistré", "successHint": "Commencez avec Medusa Admin dès maintenant.", "successAction": "Commencer <PERSON><PERSON><PERSON>", "invalidTokenTitle": "Votre lien d'invitation est invalide", "invalidTokenHint": "Essayez de demander un nouveau lien d'invitation.", "passwordMismatch": "Les mots de passe ne correspondent pas", "toast": {"accepted": "Invitation acceptée avec succès"}}, "resetPassword": {"title": "Réinitialiser le mot de passe", "hint": "Entrez votre email ci-dessous, et nous vous enverrons des instructions sur la façon de réinitialiser votre mot de passe.", "email": "Email", "sendResetInstructions": "Envoyer les instructions de réinitialisation", "backToLogin": "<0>Retour à la connexion</0>", "newPasswordHint": "Choisissez un nouveau mot de passe ci-dessous.", "invalidTokenTitle": "Votre lien de réinitialisation est invalide", "invalidTokenHint": "Essayez de demander un nouveau lien de réinitialisation.", "expiredTokenTitle": "Votre lien de réinitialisation a expiré", "goToResetPassword": "Aller à la réinitialisation du mot de passe", "resetPassword": "Réinitialiser le mot de passe", "newPassword": "Nouveau mot de passe", "repeatNewPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le nouveau mot de passe", "tokenExpiresIn": "Le lien expire dans <0>{{time}}</0> minutes", "successfulRequestTitle": "Un email vous a été envoyé", "successfulRequest": "Nous vous avons envoyé un email que vous pouvez utiliser pour réinitialiser votre mot de passe. Vérifiez votre dossier de spam si vous n'avez pas reçu l'email après quelques minutes.", "successfulResetTitle": "Réinitialisation du mot de passe réussie", "successfulReset": "Veuillez vous connecter sur la page de connexion.", "passwordMismatch": "Les mots de passe ne correspondent pas", "invalidLinkTitle": "Votre lien de réinitialisation est invalide", "invalidLinkHint": "Essayez de réinitialiser votre mot de passe à nouveau."}, "workflowExecutions": {"domain": "Workflows", "subtitle": "Voir et suivre les exécutions des workflows dans votre application Medusa.", "transactionIdLabel": "ID de la transaction", "workflowIdLabel": "ID du workflow", "progressLabel": "Progression", "stepsCompletedLabel_one": "{{completed}} de {{count}} étape", "stepsCompletedLabel_other": "{{completed}} de {{count}} étapes", "list": {"noRecordsMessage": "Aucun workflow n'a été exécuté, pour le moment."}, "history": {"sectionTitle": "Historique", "runningState": "En cours...", "awaitingState": "En attente", "failedState": "Échec", "skippedState": "Ignoré", "skippedFailureState": "Ignoré (Échec)", "definitionLabel": "Définition", "outputLabel": "<PERSON><PERSON><PERSON>", "compensateInputLabel": "Entrée de compensation", "revertedLabel": "Inversé", "errorLabel": "<PERSON><PERSON><PERSON>"}, "state": {"done": "<PERSON><PERSON><PERSON><PERSON>", "failed": "Échec", "reverted": "Inversé", "invoking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "compensating": "<PERSON><PERSON><PERSON><PERSON>", "notStarted": "Non démarré"}, "transaction": {"state": {"waitingToCompensate": "En attente de compensation"}}, "step": {"state": {"skipped": "Ignoré", "skippedFailure": "Ignoré (Échec)", "dormant": "<PERSON><PERSON><PERSON>", "timeout": "Timeout"}}}, "productTypes": {"domain": "Types de produits", "subtitle": "Organiser vos produits en types.", "create": {"header": "Créer un type de produit", "hint": "Créer un nouveau type de produit pour classer vos produits.", "successToast": "Type de produit {{value}} c<PERSON>é avec succès."}, "edit": {"header": "Modifier le type de produit", "successToast": "Type de produit {{value}} mis à jour avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer le type de produit {{value}}. Cette action ne peut être annulée.", "successToast": "Type de produit {{value}} supprimé avec succès."}, "fields": {"value": "<PERSON><PERSON>"}}, "productTags": {"domain": "Tags de produits", "create": {"header": "Créer un tag de produit", "subtitle": "Créer un nouveau tag de produit pour classer vos produits.", "successToast": "Tag de produit {{value}} c<PERSON><PERSON> avec succès."}, "edit": {"header": "Modifier le tag de produit", "subtitle": "Modifier la valeur du tag de produit.", "successToast": "Tag de produit {{value}} mis à jour avec succès."}, "delete": {"confirmation": "Vous êtes sur le point de supprimer le tag de produit {{value}}. Cette action ne peut être annulée.", "successToast": "Tag de produit {{value}} supprimé avec succès."}, "fields": {"value": "<PERSON><PERSON>"}}, "notifications": {"domain": "Notifications", "emptyState": {"title": "Aucune notification", "description": "Vous n'avez aucune notification pour le moment, mais elles apparaîtront ici une fois que vous les aurez."}, "accessibility": {"description": "Les notifications concernant les activités Medusa seront listées ici."}}, "errors": {"serverError": "Erreur serveur - <PERSON><PERSON><PERSON><PERSON> plus tard.", "invalidCredentials": "Mauvais email ou mot de passe"}, "statuses": {"scheduled": "Programmé", "expired": "Expiré", "active": "Actif", "enabled": "Activé", "disabled": "Désactivé"}, "labels": {"productVariant": "Variante de produit", "prices": "Prix", "available": "Disponible", "inStock": "En stock", "added": "<PERSON><PERSON><PERSON>", "removed": "Supprimé"}, "fields": {"amount": "<PERSON><PERSON>", "refundAmount": "Montant de remboursement", "name": "Nom", "default": "<PERSON><PERSON> <PERSON><PERSON>", "lastName": "Nom de famille", "firstName": "Prénom", "title": "Titre", "customTitle": "<PERSON><PERSON><PERSON>", "manageInventory": "<PERSON><PERSON>rer l'inventaire", "inventoryKit": "Kit d'inventaire", "inventoryItems": "Articles en inventaire", "inventoryItem": "Article en inventaire", "requiredQuantity": "Quantité requise", "description": "Description", "email": "Email", "password": "Mot de passe", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le mot de passe", "confirmPassword": "Confirmer le mot de passe", "newPassword": "Nouveau mot de passe", "repeatNewPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> le nouveau mot de passe", "categories": "Catégories", "shippingMethod": "<PERSON><PERSON><PERSON><PERSON> de livrai<PERSON>", "configurations": "Configurations", "conditions": "Conditions", "category": "<PERSON><PERSON><PERSON><PERSON>", "collection": "Collection", "discountable": "Remise", "handle": "Identifiant", "subtitle": "Sous-titre", "item": "Article", "qty": "Qté", "limit": "Limite", "tags": "Tags", "type": "Type", "reason": "<PERSON>son", "none": "Aucun", "all": "Tous", "search": "<PERSON><PERSON><PERSON>", "percentage": "Pourcentage", "sales_channels": "Canaux de vente", "customer_groups": "Groupes de clients", "product_tags": "Tags de produits", "product_types": "Types de produits", "product_collections": "Collections de produits", "status": "Statut", "code": "Code", "value": "<PERSON><PERSON>", "disabled": "Désactivé", "dynamic": "Dynamique", "normal": "Normal", "years": "<PERSON><PERSON>", "months": "<PERSON><PERSON>", "days": "Jours", "hours": "<PERSON><PERSON>", "minutes": "Minutes", "totalRedemptions": "Total d'utilisations", "countries": "Pays", "paymentProviders": "Fournisseurs de paiement", "refundReason": "Raison de remboursement", "fulfillmentProviders": "Fournisseurs de livraison", "fulfillmentProvider": "Fournisseur de livrai<PERSON>", "providers": "Fournisseurs", "availability": "Disponibilité", "inventory": "Inventaire", "optional": "Optionnel", "note": "Note", "automaticTaxes": "Taxes automatiques", "taxInclusivePricing": "Prix incluant les taxes", "currency": "Monnaie", "address": "<PERSON><PERSON><PERSON>", "address2": "Appartement, suite, etc.", "city": "Ville", "postalCode": "Code postal", "country": "Pays", "state": "État", "province": "Province", "company": "Entreprise", "phone": "Téléphone", "metadata": "Métadonnées", "selectCountry": "Sélectionner un pays", "products": "Produits", "variants": "<PERSON><PERSON><PERSON>", "orders": "Commandes", "account": "<PERSON><PERSON><PERSON>", "total": "Total de la commande", "paidTotal": "Total capturé", "totalExclTax": "Total hors taxes", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "outboundShipping": "Outbound Shipping", "returnShipping": "Return Shipping", "tax": "Taxe", "created": "<PERSON><PERSON><PERSON>", "key": "Clé", "customer": "Client", "date": "Date", "order": "Commande", "fulfillment": "<PERSON><PERSON><PERSON>", "provider": "Fournisseur", "payment": "Paiement", "items": "Articles", "salesChannel": "Canal de vente", "region": "Région", "discount": "Remise", "role": "R<PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON>", "salesChannels": "Canaux de vente", "product": "Produit", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour", "revokedAt": "<PERSON><PERSON><PERSON>", "true": "Vrai", "false": "Faux", "giftCard": "<PERSON><PERSON> cadeau", "tag": "Tag", "dateIssued": "Date d'émission", "issuedDate": "Date d'émission", "expiryDate": "Date d'expiration", "price": "Prix", "priceTemplate": "Prix {{regionOrCurrency}}", "height": "<PERSON><PERSON>", "width": "<PERSON><PERSON>", "length": "<PERSON><PERSON><PERSON>", "weight": "Poids", "midCode": "MID code", "hsCode": "HS code", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Quantité en inventaire", "barcode": "Code barre", "countryOfOrigin": "Pays d'origine", "material": "<PERSON><PERSON>", "thumbnail": "Miniature", "sku": "SKU", "managedInventory": "Inventaire géré", "allowBackorder": "Autoriser les commandes en rupture de stock", "inStock": "En stock", "location": "Emplacement", "quantity": "Quantité", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "Parent", "minSubtotal": "<PERSON><PERSON> Sous-total", "maxSubtotal": "<PERSON><PERSON>-total", "shippingProfile": "<PERSON><PERSON> <PERSON>", "summary": "Résumé", "details": "Détails", "label": "Label", "rate": "<PERSON><PERSON>", "requiresShipping": "Re<PERSON>ert une livraison", "unitPrice": "Prix unitaire", "startDate": "Date de début", "endDate": "Date de fin", "draft": "Brouillon", "values": "Valeurs"}, "dateTime": {"years_one": "<PERSON><PERSON>", "years_other": "<PERSON><PERSON>", "months_one": "<PERSON><PERSON>", "months_other": "<PERSON><PERSON>", "weeks_one": "<PERSON><PERSON><PERSON>", "weeks_other": "Se<PERSON>ines", "days_one": "Jour", "days_other": "Jours", "hours_one": "<PERSON><PERSON>", "hours_other": "<PERSON><PERSON>", "minutes_one": "Minute", "minutes_other": "Minutes", "seconds_one": "Seconde", "seconds_other": "Secondes"}}