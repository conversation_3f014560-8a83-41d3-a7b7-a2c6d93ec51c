import { OnChangeFn, RowSelectionState } from "@tanstack/react-table";
import { useState } from "react";
import { _DataTable as DataTable } from "../../../../components/table/data-table";
import { useVariants } from "../../../../hooks/api";
import { useDataTable } from "../../../../hooks/use-data-table";
import { useQueryParams } from "../../../../hooks/use-query-params";
import { Checkbox } from "@medusajs/ui";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { ProductCell, ProductHeader } from "../../../../components/table/table-cells/product/product-cell";

const PAGE_SIZE = 50;
const PREFIX = "rit";

type ManageItemsTableProps = {
  onSelectionChange: (ids: string[]) => void;
  currencyCode: string;
};

const columnHelper = createColumnHelper<any>();

export const ManageItemsTable = ({
  onSelectionChange,
  currencyCode,
}: ManageItemsTableProps) => {
  const { t } = useTranslation();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const updater: OnChangeFn<RowSelectionState> = (fn) => {
    const newState: RowSelectionState =
      typeof fn === "function" ? fn(rowSelection) : fn;

    setRowSelection(newState);
    onSelectionChange(Object.keys(newState));
  };

  // Query params for variants
  const raw = useQueryParams(
    ["q", "offset", "order", "created_at", "updated_at"],
    PREFIX
  );

  const { offset, created_at, updated_at, ...rest } = raw;
  const searchParams = {
    ...rest,
    limit: PAGE_SIZE,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : undefined,
    updated_at: updated_at ? JSON.parse(updated_at) : undefined,
  };

  const { variants = [], count } = useVariants({
    ...searchParams,
    fields: "*inventory_items.inventory.location_levels,+inventory_quantity",
  });

  const columns = useMemo(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (
            <Checkbox
              checked={
                table.getIsSomePageRowsSelected()
                  ? "indeterminate"
                  : table.getIsAllPageRowsSelected()
              }
              onCheckedChange={(value) =>
                table.toggleAllPageRowsSelected(!!value)
              }
            />
          );
        },
        cell: ({ row }) => {
          const isSelectable = row.getCanSelect();

          return (
            <Checkbox
              disabled={!isSelectable}
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
          );
        },
      }),
      columnHelper.display({
        id: "product",
        header: () => <ProductHeader />,
        cell: ({ row }) => {
          return <ProductCell product={row.original.product} />;
        },
      }),
      columnHelper.accessor("sku", {
        header: t("fields.sku"),
        cell: ({ getValue }) => {
          return getValue() || "-";
        },
      }),
      columnHelper.accessor("title", {
        header: t("fields.title"),
      }),
    ],
    [t, currencyCode]
  );

  const filters: any[] = [];

  const { table } = useDataTable({
    data: variants,
    columns: columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE,
    enableRowSelection: (row) => true,
    rowSelection: {
      state: rowSelection,
      updater,
    },
  });

  return (
    <div className="flex size-full flex-col overflow-hidden">
      <DataTable
        table={table}
        columns={columns}
        pageSize={PAGE_SIZE}
        count={count}
        filters={filters}
        pagination
        layout="fill"
        search
        orderBy={["product_id", "title", "sku"]}
        prefix={PREFIX}
        queryObject={raw}
      />
    </div>
  );
};