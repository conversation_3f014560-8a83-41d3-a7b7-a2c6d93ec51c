import { Container, Heading } from "@medusajs/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import { useMemo } from "react"

import { _DataTable } from "../../../../components/table/data-table/data-table"
import { useApprovals } from "../../../../hooks/api/approvals"
import { useDataTable } from "../../../../hooks/use-data-table"
import { useApprovalsTableColumns } from "./table/columns"
import { useApprovalsTableFilters } from "./table/filters"
import { useApprovalsTableQuery } from "./table/query"

const PAGE_SIZE = 20

export const ApprovalsTable = () => {
  const { t } = useTranslation()
  const { searchParams, raw } = useApprovalsTableQuery({
    pageSize: PAGE_SIZE,
  })

  // 从searchParams中提取搜索关键词和筛选参数
  const searchQuery = searchParams.q
  const statusFilter = searchParams.status
  const createdAtFilter = searchParams.created_at
  const updatedAtFilter = searchParams.updated_at

  const { approvals, count, isError, error, isLoading } = useApprovals(
    {
      // 移除搜索、排序和筛选参数，因为我们要在客户端实现
      ...searchParams,
      q: undefined,
      order: undefined,
      status: undefined,
      created_at: undefined,
      updated_at: undefined,
    },
    {
      placeholderData: keepPreviousData,
    }
  )

  // 调试：打印数据
  console.log("useApprovals approvals:", approvals)
  console.log("useApprovals count:", count)

  // 获取排序参数
  const sortOrder = searchParams.order || "-created_at"

  // 客户端筛选和排序：根据搜索关键词和筛选条件筛选审批并排序
  const filteredApprovals = useMemo(() => {
    let result = approvals ?? []

    // 根据搜索关键词筛选
    if (searchQuery) {
      result = result.filter((cart) => {
        const searchTerm = searchQuery.toLowerCase()
        return (
          // 购物车ID
          cart.id?.toLowerCase().includes(searchTerm) ||
          // 审批类型
          cart.approvals?.[0]?.type?.toLowerCase().includes(searchTerm) ||
          // 公司名称
          cart.company?.name?.toLowerCase().includes(searchTerm) ||
          // 客户邮箱
          cart.email?.toLowerCase().includes(searchTerm) ||
          // 审批状态
          cart.approval_status?.status?.toLowerCase().includes(searchTerm)
        )
      })
    }

    // 根据状态筛选
    if (statusFilter && statusFilter.length > 0) {
      result = result.filter((cart) => {
        return statusFilter.includes(cart.approval_status?.status)
      })
    }

    // 根据创建时间筛选
    if (createdAtFilter) {
      result = result.filter((cart) => {
        const createdAt = new Date(cart.created_at)
        if (createdAtFilter.gte) {
          const gteDate = new Date(createdAtFilter.gte)
          if (createdAt < gteDate) return false
        }
        if (createdAtFilter.lte) {
          const lteDate = new Date(createdAtFilter.lte)
          if (createdAt > lteDate) return false
        }
        return true
      })
    }

    // 根据更新时间筛选
    if (updatedAtFilter) {
      result = result.filter((cart) => {
        const updatedAt = new Date(cart.updated_at)
        if (updatedAtFilter.gte) {
          const gteDate = new Date(updatedAtFilter.gte)
          if (updatedAt < gteDate) return false
        }
        if (updatedAtFilter.lte) {
          const lteDate = new Date(updatedAtFilter.lte)
          if (updatedAt > lteDate) return false
        }
        return true
      })
    }

    // 排序
    if (sortOrder) {
      const [field, direction] = sortOrder.startsWith("-")
        ? [sortOrder.slice(1), "desc"]
        : [sortOrder, "asc"]

      result.sort((a, b) => {
        let aValue: any
        let bValue: any

        switch (field) {
          case "id":
            aValue = a.id
            bValue = b.id
            break
          case "type":
            aValue = a.approvals?.[0]?.type || ""
            bValue = b.approvals?.[0]?.type || ""
            break
          case "status":
            aValue = a.approval_status?.status || ""
            bValue = b.approval_status?.status || ""
            break
          case "created_at":
            aValue = new Date(a.created_at)
            bValue = new Date(b.created_at)
            break
          case "updated_at":
            aValue = new Date(a.updated_at)
            bValue = new Date(b.updated_at)
            break
          case "amount":
            // 计算总金额
            aValue = a.items?.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0) || 0
            bValue = b.items?.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0) || 0
            break
          default:
            return 0
        }

        if (aValue < bValue) return direction === "asc" ? -1 : 1
        if (aValue > bValue) return direction === "asc" ? 1 : -1
        return 0
      })
    }

    return result
  }, [approvals, searchQuery, statusFilter, createdAtFilter, updatedAtFilter, sortOrder])

  // 计算筛选后的数量
  const filteredCount = useMemo(() => {
    return filteredApprovals.length
  }, [filteredApprovals.length])

  const filters = useApprovalsTableFilters()
  const columns = useApprovalsTableColumns()

  const { table } = useDataTable({
    data: filteredApprovals ?? [],
    columns,
    enablePagination: true,
    count: filteredCount,
    pageSize: PAGE_SIZE,
  })

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{t("approvals.title", "Approvals")}</Heading>
      </div>
      <_DataTable
        columns={columns}
        table={table}
        pagination
        navigateTo={(row) => `/approvals/${row.original.id}`}
        filters={filters}
        count={filteredCount}
        search
        isLoading={isLoading}
        pageSize={PAGE_SIZE}
        orderBy={[
          { key: "id", label: t("approvals.table.id", "ID") },
          { key: "type", label: t("approvals.table.type", "Type") },
          { key: "status", label: t("approvals.table.status", "Status") },
          { key: "amount", label: t("approvals.table.amount", "Amount") },
          { key: "created_at", label: t("fields.createdAt", "Created At") },
          { key: "updated_at", label: t("fields.updatedAt", "Updated At") },
        ]}
        queryObject={raw}
        noRecords={{
          message: t("approvals.noApprovals", "No approvals found"),
        }}
      />
    </Container>
  )
}