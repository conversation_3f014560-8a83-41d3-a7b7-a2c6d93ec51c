import React, { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Container, Table, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { useApprovals } from "../../../../hooks/api/approvals"
import { Approval } from "../../../../types"
import { ApprovalStatusBadge } from "./approval-status-badge"
import { ApprovalsTableFilters } from "./approvals-table-filters"

export const ApprovalsTable = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [statusFilter, setStatusFilter] = useState<string>("")

  const { approvals, isLoading, isError, error } = useApprovals({
    status: statusFilter || undefined,
  })

  if (isLoading) {
    return (
      <Container className="flex items-center justify-center p-8">
        <Text>{t("general.loading", "Loading...")}</Text>
      </Container>
    )
  }

  if (isError) {
    return (
      <Container className="flex items-center justify-center p-8">
        <Text className="text-ui-fg-error">
          {t("general.error", "Error loading approvals")}
        </Text>
      </Container>
    )
  }

  const hasApprovals = approvals && approvals.length > 0

  if (!hasApprovals) {
    return (
      <Container className="p-0">
        <ApprovalsTableFilters 
          status={statusFilter}
          onStatusChange={setStatusFilter}
        />
        <Container className="flex flex-col items-center justify-center p-8 text-center">
          <Text className="text-ui-fg-subtle mb-2">
            {t("approvals.noApprovals", "No approvals found")}
          </Text>
          <Text className="text-ui-fg-muted text-sm">
            {t("approvals.noApprovalsDescription", "There are currently no approvals to review.")}
          </Text>
        </Container>
      </Container>
    )
  }

  return (
    <Container className="p-0">
      <ApprovalsTableFilters 
        status={statusFilter}
        onStatusChange={setStatusFilter}
      />
      <Table>
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell>
              {t("approvals.table.id", "ID")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.type", "Type")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.company", "Company")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.customer", "Customer")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.amount", "Amount")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.status", "Status")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("approvals.table.createdAt", "Created")}
            </Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {approvals.map((approval: Approval) => (
            <Table.Row
              key={approval.id}
              className="cursor-pointer hover:bg-ui-bg-subtle"
              onClick={() => navigate(`/approvals/${approval.id}`)}
            >
              <Table.Cell className="font-medium">
                {approval.id.slice(-8).toUpperCase()}
              </Table.Cell>
              <Table.Cell>
                <Text className="capitalize">{approval.type}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text>{approval.company?.name || "-"}</Text>
              </Table.Cell>
              <Table.Cell>
                <div>
                  <Text className="font-medium">
                    {approval.customer?.first_name} {approval.customer?.last_name}
                  </Text>
                  <Text className="text-ui-fg-subtle text-sm">
                    {approval.customer?.email}
                  </Text>
                </div>
              </Table.Cell>
              <Table.Cell>
                {approval.amount && approval.currency_code ? (
                  <Text className="font-medium">
                    {new Intl.NumberFormat("en-US", {
                      style: "currency",
                      currency: approval.currency_code.toUpperCase(),
                    }).format(approval.amount / 100)}
                  </Text>
                ) : (
                  <Text>-</Text>
                )}
              </Table.Cell>
              <Table.Cell>
                <ApprovalStatusBadge status={approval.status} />
              </Table.Cell>
              <Table.Cell>
                <Text className="text-ui-fg-subtle">
                  {new Date(approval.created_at).toLocaleDateString()}
                </Text>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Container>
  )
}