import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, toast } from "@medusajs/ui";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import { RouteFocusModal } from "../../../../components/modals/route-focus-modal/route-focus-modal";
import { useRouteModal } from "../../../../components/modals/route-modal-provider/use-route-modal";
import { useConfirmQuote } from "../../../../hooks/api/quotes";
import { useOrderPreview } from "../../../../hooks/api";
import { getLocaleAmount } from "../../../../lib/money-amount-helpers";
import { ManageItemsSection } from "./manage-items-section";

export const ManageQuoteFormSchema = z.object({});

export type ManageQuoteFormSchemaType = z.infer<typeof ManageQuoteFormSchema>;

type ManageQuoteFormProps = {
  order: any;
};

export const ManageQuoteForm = ({ order }: ManageQuoteFormProps) => {
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { order: preview } = useOrderPreview(order.id);

  /**
   * MUTATIONS
   */
  const { mutateAsync: confirmQuote, isPending: isRequesting } =
    useConfirmQuote(order.id);

  /**
   * FORM
   */
  const form = useForm<ManageQuoteFormSchemaType>({
    defaultValues: () => Promise.resolve({}),
    resolver: zodResolver(ManageQuoteFormSchema),
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      await confirmQuote({});

      toast.success(t("quotes.toasts.updateSuccess", "Quote updated successfully"));
      handleSuccess();
    } catch (e) {
      toast.error(t("general.error"), {
        description: e.message,
      });
    }
  });

  if (!preview) {
    return <></>;
  }

  return (
    <RouteFocusModal.Form form={form}>
      <form onSubmit={handleSubmit} className="flex h-full flex-col">
        <RouteFocusModal.Header />

        <RouteFocusModal.Body className="flex size-full justify-center overflow-y-auto">
          <div className="mt-16 w-[720px] max-w-[100%] px-4 md:p-0">
            <Heading level="h1">{t("quotes.details.manageQuote", "Manage Quote")}</Heading>

            <ManageItemsSection preview={preview} order={order} />

            {/*TOTALS SECTION*/}
            <div className="mt-8 border-y border-dotted py-4">
              <div className="mb-2 flex items-center justify-between">
                <span className="txt-small text-ui-fg-subtle">
                  {t("orders.edits.currentTotal", "原始总价")}
                </span>

                <span className="txt-small text-ui-fg-subtle">
                  {getLocaleAmount(order.total, order.currency_code)}
                </span>
              </div>

              <div className="mb-2 flex items-center justify-between">
                <span className="txt-small text-ui-fg-subtle">
                  {t("orders.edits.newTotal", "新总价")}
                </span>

                <span className="txt-small text-ui-fg-subtle">
                  {getLocaleAmount(preview.total, order.currency_code)}
                </span>
              </div>
            </div>

            <div className="p-8" />
          </div>
        </RouteFocusModal.Body>

        <RouteFocusModal.Footer>
          <div className="flex w-full items-center justify-end gap-x-4">
            <div className="flex items-center justify-end gap-x-2">
              <Button
                key="submit-button"
                type="submit"
                variant="primary"
                size="small"
              >
                {t("actions.continue")}
              </Button>
            </div>
          </div>
        </RouteFocusModal.Footer>
      </form>
    </RouteFocusModal.Form>
  );
};