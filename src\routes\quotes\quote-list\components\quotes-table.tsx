import { Container, Heading } from "@medusajs/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import { useMemo } from "react"

import { _DataTable } from "../../../../components/table/data-table/data-table"
import { useQuotes } from "../../../../hooks/api/quotes"
import { useDataTable } from "../../../../hooks/use-data-table"
import { useQuotesTableColumns } from "./table/columns"
import { useQuotesTableFilters } from "./table/filters"
import { useQuotesTableQuery } from "./table/query"

const PAGE_SIZE = 20

export const QuotesTable = () => {
  const { t } = useTranslation()
  const { searchParams, raw } = useQuotesTableQuery({
    pageSize: PAGE_SIZE,
  })

  // 从searchParams中提取搜索关键词和筛选参数
  const searchQuery = searchParams.q
  const statusFilter = searchParams.status
  const createdAtFilter = searchParams.created_at
  const updatedAtFilter = searchParams.updated_at

  const { quotes, count, isError, error, isLoading } = useQuotes(
    {
      // 移除搜索、排序和筛选参数，因为我们要在客户端实现
      ...searchParams,
      q: undefined,
      order: undefined,
      status: undefined,
      created_at: undefined,
      updated_at: undefined,
    },
    {
      placeholderData: keepPreviousData,
    }
  )

  // 获取排序参数
  const sortOrder = searchParams.order || "-created_at"

  // 客户端筛选和排序：根据搜索关键词和筛选条件筛选报价并排序
  const filteredQuotes = useMemo(() => {
    let result = quotes ?? []

    // 根据搜索关键词筛选
    if (searchQuery) {
      result = result.filter((quote) => {
        const searchTerm = searchQuery.toLowerCase()
        return (
          // 报价ID
          quote.id?.toLowerCase().includes(searchTerm) ||
          // 报价显示ID (如 #123)
          quote.draft_order?.display_id?.toString().includes(searchTerm) ||
          // 客户邮箱
          quote.customer?.email?.toLowerCase().includes(searchTerm) ||
          // 公司名称
          quote.customer?.company_name?.toLowerCase().includes(searchTerm) ||
          // 报价状态
          quote.status?.toLowerCase().includes(searchTerm)
        )
      })
    }

    // 根据状态筛选
    if (statusFilter && statusFilter.length > 0) {
      result = result.filter((quote) => {
        return statusFilter.includes(quote.status)
      })
    }

    // 根据创建时间筛选
    if (createdAtFilter) {
      result = result.filter((quote) => {
        if (!quote.created_at) return false
        const quoteDate = new Date(quote.created_at)

        // 处理日期范围筛选 - 使用$gte和$lte格式
        if (createdAtFilter.$gte) {
          const startDate = new Date(createdAtFilter.$gte)
          if (quoteDate < startDate) return false
        }

        if (createdAtFilter.$lte) {
          const endDate = new Date(createdAtFilter.$lte)
          // 设置结束日期为当天的23:59:59
          endDate.setHours(23, 59, 59, 999)
          if (quoteDate > endDate) return false
        }

        return true
      })
    }

    // 根据更新时间筛选
    if (updatedAtFilter) {
      result = result.filter((quote) => {
        if (!quote.updated_at) return false
        const quoteDate = new Date(quote.updated_at)

        // 处理日期范围筛选 - 使用$gte和$lte格式
        if (updatedAtFilter.$gte) {
          const startDate = new Date(updatedAtFilter.$gte)
          if (quoteDate < startDate) return false
        }

        if (updatedAtFilter.$lte) {
          const endDate = new Date(updatedAtFilter.$lte)
          // 设置结束日期为当天的23:59:59
          endDate.setHours(23, 59, 59, 999)
          if (quoteDate > endDate) return false
        }

        return true
      })
    }

    // 客户端排序
    if (sortOrder) {
      const isDescending = sortOrder.startsWith('-')
      const sortField = isDescending ? sortOrder.substring(1) : sortOrder

      result = [...result].sort((a, b) => {
        let aValue: any
        let bValue: any

        switch (sortField) {
          case 'id':
            aValue = a.id || ''
            bValue = b.id || ''
            break
          case 'status':
            aValue = a.status || ''
            bValue = b.status || ''
            break
          case 'created_at':
            aValue = new Date(a.created_at || 0).getTime()
            bValue = new Date(b.created_at || 0).getTime()
            break
          case 'updated_at':
            aValue = new Date(a.updated_at || 0).getTime()
            bValue = new Date(b.updated_at || 0).getTime()
            break
          default:
            return 0
        }

        if (aValue < bValue) return isDescending ? 1 : -1
        if (aValue > bValue) return isDescending ? -1 : 1
        return 0
      })
    }

    return result
  }, [quotes, searchQuery, statusFilter, createdAtFilter, updatedAtFilter, sortOrder])

  // 计算筛选后的数量
  const filteredCount = useMemo(() => {
    return filteredQuotes.length
  }, [filteredQuotes.length])

  const filters = useQuotesTableFilters()
  const columns = useQuotesTableColumns()

  const { table } = useDataTable({
    data: filteredQuotes ?? [],
    columns,
    enablePagination: true,
    count: filteredCount,
    pageSize: PAGE_SIZE,
  })

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{t("quotes.title", "Quotes")}</Heading>
      </div>
      <_DataTable
        columns={columns}
        table={table}
        pagination
        navigateTo={(row) => `/quotes/${row.original.id}`}
        filters={filters}
        count={filteredCount}
        search
        isLoading={isLoading}
        pageSize={PAGE_SIZE}
        orderBy={[
          { key: "id", label: t("quotes.table.id", "Quote ID") },
          { key: "status", label: t("quotes.table.status", "Status") },
          { key: "created_at", label: t("fields.createdAt", "Created At") },
          { key: "updated_at", label: t("fields.updatedAt", "Updated At") },
        ]}
        queryObject={raw}
        noRecords={{
          message: t("quotes.noQuotes", "No quotes found"),
        }}
      />
    </Container>
  )
}

