import { Button, Drawer, Input, Label, Select, Text, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useCreateCompany } from "../../../../hooks/api/companies"
import { useRegions } from "../../../../hooks/api/regions"
import { AdminCreateCompany } from "../../../../types"

export const CompanyCreateDrawer = () => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [formData, setFormData] = useState<AdminCreateCompany>({} as AdminCreateCompany)

  const { regions, isPending: regionsLoading } = useRegions({
    fields: "*countries",
    limit: 999
  })

  const createCompanyMutation = useCreateCompany({
    onSuccess: () => {
      toast.success(t("companies.toasts.createSuccess", "Successfully created company"))
      setOpen(false)
      setFormData({} as AdminCreateCompany)
    },
    onError: (error) => {
      toast.error(t("companies.toasts.createError", "Failed to create company"))
    },
  })

  const currencyCodes = regions?.map((region) => region.currency_code)
  const countries = regions?.flatMap((region) => region.countries)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleCurrencyChange = (value: string) => {
    setFormData({ ...formData, currency: value })
  }

  const handleCountryChange = (value: string) => {
    setFormData({ ...formData, country: value })
  }

  const handleSubmit = async () => {
    createCompanyMutation.mutate(formData)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Trigger asChild>
        <Button variant="secondary" size="small">
          {t("companies.create.title", "Create Company")}
        </Button>
      </Drawer.Trigger>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>{t("companies.create.title", "Create Company")}</Drawer.Title>
        </Drawer.Header>

        <form>
          <Drawer.Body className="p-4">
            <div className="flex flex-col gap-2">
              <Label size="xsmall">{t("companies.fields.name", "Company Name")}</Label>
              <Input
                type="text"
                name="name"
                value={formData.name || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.name", "Enter company name")}
              />
              <Label size="xsmall">{t("companies.fields.phone", "Phone")}</Label>
              <Input
                type="text"
                name="phone"
                value={formData.phone || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.phone", "Enter phone number")}
              />
              <Label size="xsmall">{t("companies.fields.email", "Email")}</Label>
              <Input
                type="email"
                name="email"
                value={formData.email || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.email", "Enter email address")}
              />
              <Label size="xsmall">{t("companies.fields.address", "Address")}</Label>
              <Input
                type="text"
                name="address"
                value={formData.address || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.address", "Enter street address")}
              />
              <Label size="xsmall">{t("companies.fields.city", "City")}</Label>
              <Input
                type="text"
                name="city"
                value={formData.city || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.city", "Enter city")}
              />
              <Label size="xsmall">{t("companies.fields.state", "State")}</Label>
              <Input
                type="text"
                name="state"
                value={formData.state || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.state", "Enter state")}
              />
              <Label size="xsmall">{t("companies.fields.zip", "ZIP Code")}</Label>
              <Input
                type="text"
                name="zip"
                value={formData.zip || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.zip", "Enter ZIP code")}
              />

              <div className="flex gap-4 w-full">
                <div className="flex flex-col gap-2 w-1/2">
                  <Label size="xsmall">{t("companies.fields.country", "Country")}</Label>
                  <Select
                    name="country"
                    value={formData.country || ""}
                    onValueChange={handleCountryChange}
                    disabled={regionsLoading}
                  >
                    <Select.Trigger disabled={regionsLoading}>
                      <Select.Value placeholder={t("companies.fields.selectCountry", "Select Country")} />
                    </Select.Trigger>
                    <Select.Content className="z-50">
                      {countries?.map((country) => (
                        <Select.Item
                          key={country?.iso_2 || ""}
                          value={country?.iso_2 || ""}
                        >
                          {country?.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>
                <div className="flex flex-col gap-2 w-1/2">
                  <Label size="xsmall">{t("companies.fields.currency", "Currency")}</Label>
                  <Select
                    name="currency"
                    value={formData.currency || ""}
                    onValueChange={handleCurrencyChange}
                    disabled={regionsLoading}
                  >
                    <Select.Trigger disabled={regionsLoading}>
                      <Select.Value placeholder={t("companies.fields.selectCurrency", "Select Currency")} />
                    </Select.Trigger>
                    <Select.Content className="z-50">
                      {currencyCodes?.map((currencyCode) => (
                        <Select.Item key={currencyCode} value={currencyCode}>
                          {currencyCode?.toUpperCase()}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>
              </div>
              <Label size="xsmall">{t("companies.fields.logoUrl", "Logo URL")}</Label>
              <Input
                type="text"
                name="logo_url"
                value={formData.logo_url || ""}
                onChange={handleChange}
                placeholder={t("companies.placeholders.logoUrl", "Enter logo URL")}
              />
            </div>
          </Drawer.Body>
          <Drawer.Footer>
            <Drawer.Close asChild>
              <Button variant="secondary">{t("actions.cancel", "Cancel")}</Button>
            </Drawer.Close>
            <Button
              isLoading={createCompanyMutation.isPending}
              onClick={handleSubmit}
            >
              {t("actions.save", "Save")}
            </Button>
            {createCompanyMutation.error && (
              <Text className="txt-compact-small text-ui-fg-warning">
                {t("common.error", "Error")}: {createCompanyMutation.error?.message}
              </Text>
            )}
          </Drawer.Footer>
        </form>
      </Drawer.Content>
    </Drawer>
  )
}