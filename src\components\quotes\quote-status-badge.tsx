import { Badge } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Quote } from "../../types"

interface QuoteStatusBadgeProps {
  status: Quote["status"]
}

export const QuoteStatusBadge = ({ status }: QuoteStatusBadgeProps) => {
  const { t } = useTranslation()

  const getStatusConfig = (status: Quote["status"]) => {
    switch (status) {
      case "pending_merchant":
        return { 
          color: "orange" as const, 
          label: t("quotes.status.pending_merchant", "Pending Merchant") 
        }
      case "pending_customer":
        return { 
          color: "blue" as const, 
          label: t("quotes.status.pending_customer", "Pending Customer") 
        }
      case "merchant_rejected":
        return { 
          color: "red" as const, 
          label: t("quotes.status.merchant_rejected", "Merchant Rejected") 
        }
      case "customer_rejected":
        return { 
          color: "red" as const, 
          label: t("quotes.status.customer_rejected", "Customer Rejected") 
        }
      case "accepted":
        return { 
          color: "green" as const, 
          label: t("quotes.status.accepted", "Accepted") 
        }
      default:
        return { 
          color: "grey" as const, 
          label: t("quotes.status.unknown", "Unknown") 
        }
    }
  }

  const { color, label } = getStatusConfig(status)

  return (
    <Badge size="small" color={color}>
      {label}
    </Badge>
  )
}