import { Container, Heading, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { ApprovalStatusBadge } from "../../../../components/approvals"
import { Approval } from "../../../../types"

interface ApprovalDetailsSectionProps {
  approval: Approval
}

export const ApprovalDetailsSection = ({ approval }: ApprovalDetailsSectionProps) => {
  const { t } = useTranslation()

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">
            {t("approvals.details.header", "Approval Details")}
          </Heading>
          <Text className="text-ui-fg-subtle">
            {t("approvals.details.summary", "Approval Summary")}
          </Text>
        </div>
        <ApprovalStatusBadge status={approval.status} />
      </div>

      <div className="grid grid-cols-2 gap-4 px-6 py-4">
        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.table.id", "ID")}
          </Text>
          <Text className="font-medium">
            {approval.id.slice(-8).toUpperCase()}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.table.type", "Type")}
          </Text>
          <Text className="font-medium capitalize">
            {approval.type}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.details.company", "Company")}
          </Text>
          <Text className="font-medium">
            {approval.company?.name || "-"}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.details.customer", "Customer")}
          </Text>
          <div>
            <Text className="font-medium">
              {approval.customer?.first_name} {approval.customer?.last_name}
            </Text>
            <Text className="text-ui-fg-subtle text-sm">
              {approval.customer?.email}
            </Text>
          </div>
        </div>

        {approval.order && (
          <div>
            <Text className="text-ui-fg-subtle text-sm mb-1">
              {t("approvals.details.order", "Order")}
            </Text>
            <div>
              <Text className="font-medium">
                #{approval.order.display_id}
              </Text>
              <Text className="text-ui-fg-subtle text-sm">
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: approval.order.currency_code.toUpperCase(),
                }).format(approval.order.total / 100)}
              </Text>
            </div>
          </div>
        )}

        {approval.amount && approval.currency_code && (
          <div>
            <Text className="text-ui-fg-subtle text-sm mb-1">
              {t("approvals.details.amount", "Amount")}
            </Text>
            <Text className="font-medium">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: approval.currency_code.toUpperCase(),
              }).format(approval.amount / 100)}
            </Text>
          </div>
        )}

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.table.createdAt", "Created")}
          </Text>
          <Text>
            {new Date(approval.created_at).toLocaleDateString()}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.details.updatedAt", "Updated")}
          </Text>
          <Text>
            {new Date(approval.updated_at).toLocaleDateString()}
          </Text>
        </div>
      </div>

      {approval.reason && (
        <div className="px-6 py-4">
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("approvals.details.reason", "Reason")}
          </Text>
          <Text>{approval.reason}</Text>
        </div>
      )}
    </Container>
  )
}