import bg from "./bg.json"
import de from "./de.json"
import el from "./el.json"
import en from "./en.json"
import es from "./es.json"
import fr from "./fr.json"
import he from "./he.json"
import hu from "./hu.json"
import it from "./it.json"
import ja from "./ja.json"
import pl from "./pl.json"
import ptBR from "./ptBR.json"
import th from "./th.json"
import tr from "./tr.json"
import uk from "./uk.json"
import ro from "./ro.json"
import mk from "./mk.json"
import mn from "./mn.json"
import ar from "./ar.json"
import zhCN from "./zhCN.json"
import fa from "./fa.json"
import cs from "./cs.json"
import ru from "./ru.json"
import lt from "./lt.json"
import vi from "./vi.json"
import ko from "./ko.json"
import nl from "./nl.json"
import bs from "./bs.json"
import id from "./id.json"
export default {
  bs: {
    translation: bs,
  },
  bg: {
    translation: bg,
  },
  de: {
    translation: de,
  },
  el: {
    translation: el,
  },
  en: {
    translation: en,
  },
  es: {
    translation: es,
  },
  fr: {
    translation: fr,
  },
  he: {
    translation: he,
  },
  hu: {
    translation: hu,
  },
  it: {
    translation: it,
  },
  ja: {
    translation: ja,
  },
  pl: {
    translation: pl,
  },
  ptBR: {
    translation: ptBR,
  },
  th: {
    translation: th,
  },
  tr: {
    translation: tr,
  },
  uk: {
    translation: uk,
  },
  ro: {
    translation: ro,
  },
  mk: {
    translation: mk,
  },
  mn: {
    translation: mn,
  },
  ar: {
    translation: ar,
  },
  zhCN: {
    translation: zhCN,
  },
  fa: {
    translation: fa,
  },
  cs: {
    translation: cs,
  },
  ru: {
    translation: ru,
  },
  lt: {
    translation: lt,
  },
  vi: {
    translation: vi,
  },
  ko: {
    translation: ko,
  },
  nl: {
    translation: nl,
  },
  id: {
    translation: id,
  },
}
