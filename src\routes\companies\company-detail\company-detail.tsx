import { useParams, useLoaderData } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { Text } from "@medusajs/ui"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { useCompany } from "../../../hooks/api/companies"
import { useCustomerGroups } from "../../../hooks/api/customer-groups"
import {
  CompanyGeneralSection,
  CompanyEmployeesSection
} from "./components"
import { companyLoader } from "./loader"

export const CompanyDetail = () => {
  const { id } = useParams()
  const { t } = useTranslation()

  const initialData = useLoaderData() as Awaited<ReturnType<typeof companyLoader>>
  const { company, isLoading, isError, error } = useCompany(id!, {
    fields: "*employees,*employees.customer,*customer_group,*approval_settings"
  }, {
    initialData
  })

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Text>加载中...</Text>
      </div>
    );
  }

  // 只有在不是加载状态且确实没有找到公司时才显示错误
  if (!isLoading && (!company || isError)) {
    return (
      <div className="flex items-center justify-center h-64">
        <Text>公司未找到</Text>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <CompanyGeneralSection company={company} />
      <CompanyEmployeesSection company={company} />
    </div>
  )
}