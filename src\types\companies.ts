export interface Company {
  id: string
  name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zip: string
  country?: string
  currency_code?: string
  logo_url?: string
  website?: string
  description?: string
  created_at: string
  updated_at: string
  employees?: Employee[]
  customer_group?: {
    id: string
    name: string
  }
  approval_settings?: ApprovalSettings
}

export interface Employee {
  id: string
  company_id: string
  customer_id: string
  is_admin: boolean
  spending_limit?: number
  created_at: string
  updated_at: string
  customer: {
    id: string
    email: string
    first_name: string
    last_name: string
    phone?: string
  }
}

export interface ApprovalSettings {
  id: string
  company_id: string
  requires_admin_approval: boolean
  requires_sales_manager_approval: boolean
  created_at: string
  updated_at: string
}

export interface AdminCompanyResponse {
  company: Company
}

export interface AdminCompaniesResponse {
  companies: Company[]
  count: number
  offset: number
  limit: number
}

export interface AdminCreateCompany {
  name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zip: string
  country?: string
  currency_code?: string
  logo_url?: string
  website?: string
  description?: string
}

export interface AdminUpdateCompany {
  name?: string
  email?: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip?: string
  country?: string
  currency_code?: string
  logo_url?: string
  website?: string
  description?: string
}

export interface AdminCreateEmployee {
  company_id?: string
  customer_id: string
  is_admin: boolean
  spending_limit: number
}

export interface AdminUpdateEmployee {
  id?: string
  is_admin?: boolean
  spending_limit?: number
  raw_spending_limit?: {
    value?: number
  }
}

export interface AdminEmployeeResponse {
  employee: Employee
}