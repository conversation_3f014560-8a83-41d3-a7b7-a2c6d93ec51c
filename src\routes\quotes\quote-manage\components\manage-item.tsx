import { AdminOrderLineItem } from "@medusajs/framework/types";
import { Badge, Button, Input, Text, CurrencyInput, IconButton } from "@medusajs/ui";
import { PencilSquare, DocumentSeries, XCircle, ArrowUturnLeft, XMark } from "@medusajs/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ActionMenu } from "../../../../components/common/action-menu";
import { Form } from "../../../../components/common/form";
import { Thumbnail } from "../../../../components/common/thumbnail";

// Currency symbol mapping
const getCurrencySymbol = (currencyCode: string): string => {
  const currencySymbols: Record<string, string> = {
    USD: "$",
    EUR: "€",
    GBP: "£",
    JPY: "¥",
    CNY: "¥",
    CAD: "$",
    AUD: "$",
    CHF: "CHF",
    SEK: "kr",
    NOK: "kr",
    DKK: "kr",
    PLN: "zł",
    CZK: "Kč",
    HUF: "Ft",
    RUB: "₽",
    INR: "₹",
    BRL: "R$",
    MXN: "$",
    KRW: "₩",
    SGD: "$",
    HKD: "$",
    NZD: "$",
    ZAR: "R",
    TRY: "₺",
    AED: "د.إ",
    SAR: "ر.س",
    THB: "฿",
    MYR: "RM",
    IDR: "Rp",
    PHP: "₱",
    VND: "₫",
  };

  return currencySymbols[currencyCode.toUpperCase()] || currencyCode;
};

type ManageItemProps = {
  item: AdminOrderLineItem;
  currencyCode: string;
  onQuantityChange: (itemId: string, quantity: number) => void;
  onUnitPriceChange: (itemId: string, unitPrice: number) => void;
  onAddItem: (item: AdminOrderLineItem) => void;
  onRemoveItem: (itemId: string) => void;
  onUndoItem: (itemId: string) => void;
  onDuplicate?: (item: AdminOrderLineItem) => void;
  isAdded?: boolean;
  isModified?: boolean;
  isRemoved?: boolean;
};

export const ManageItem = ({
  item,
  currencyCode,
  onQuantityChange,
  onUnitPriceChange,
  onAddItem,
  onRemoveItem,
  onUndoItem,
  onDuplicate,
  isAdded = false,
  isModified = false,
  isRemoved = false,
}: ManageItemProps) => {
  const { t } = useTranslation();
  const [quantity, setQuantity] = useState(item.quantity);
  const [unitPrice, setUnitPrice] = useState(item.unit_price || 0);
  const [showPriceForm, setShowPriceForm] = useState(false);

  const handleQuantityChange = (value: string) => {
    const newQuantity = parseInt(value) || 0;
    setQuantity(newQuantity);
    onQuantityChange(item.id, newQuantity);
  };

  const handleUnitPriceChange = (value: string) => {
    // 移除逗号和其他格式化字符，只保留数字和小数点
    const cleanValue = value.replace(/[,\s]/g, '');
    const newPrice = parseFloat(cleanValue) || 0;
    setUnitPrice(newPrice);
    onUnitPriceChange(item.id, newPrice);
  };

  const getStatusBadge = () => {
    if (isAdded) {
      return <Badge size="2xsmall" rounded="full" color="blue" className="whitespace-nowrap">{t("general.new", "New")}</Badge>;
    }
    if (isRemoved) {
      return <Badge size="2xsmall" rounded="full" color="red" className="whitespace-nowrap">{t("general.removed", "Removed")}</Badge>;
    }
    if (isModified) {
      return <Badge size="2xsmall" rounded="full" color="orange" className="whitespace-nowrap">{t("general.modified", "Modified")}</Badge>;
    }
    return null;
  };

  return (
    <div className="bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl">
      <div className="flex flex-col items-center gap-x-2 gap-y-2 p-3 text-sm md:flex-row">
        <div className="flex flex-1 items-center justify-between">
          <div className="flex flex-row items-center gap-x-3 min-w-0 flex-1">
            <div className="h-8 w-6 flex items-center justify-center overflow-hidden rounded flex-shrink-0">
              {item.thumbnail ? (
                <img
                  src={item.thumbnail}
                  alt=""
                  className="h-full w-full object-cover object-center"
                />
              ) : (
                <div className="h-8 w-6 bg-ui-bg-component flex items-center justify-center rounded">
                  <span className="text-xs text-ui-fg-subtle">{t("general.noImage", "No Image")}</span>
                </div>
              )}
            </div>

            <div className="flex flex-col min-w-0 w-48">
              <div className="truncate" title={`${item.title?.replace(/（/g, '(').replace(/）/g, ')').replace(/\s+/g, ' ').trim()}${item.variant_sku ? ` (${item.variant_sku.trim()})` : ''}`}>
                <Text className="txt-small" as="span" weight="plus">
                  {item.title?.replace(/（/g, '(').replace(/）/g, ')').replace(/\s+/g, ' ').trim()}{" "}
                </Text>
                {item.variant_sku && <span>({item.variant_sku.trim()})</span>}
              </div>
              <Text as="div" className="text-ui-fg-subtle txt-small truncate" title={item.product_title}>
                {item.product_title}
              </Text>
            </div>
          </div>

          <div className="flex-shrink-0 ml-2">
            {getStatusBadge()}
          </div>
        </div>

        <div className="flex flex-1 justify-between">
          <div className="flex flex-grow items-center gap-2">
            <Input
              className="bg-ui-bg-base txt-small w-[67px] rounded-lg [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
              type="number"
              disabled={isRemoved}
              value={quantity}
              onChange={(e) => handleQuantityChange(e.target.value)}
            />
            <Text className="txt-small text-ui-fg-subtle">
              {t("fields.qty", "Qty")}
            </Text>
          </div>

          <div className="text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0 items-center justify-center">
            <Text className="txt-small">
              {getCurrencySymbol(currencyCode)}{(quantity * unitPrice).toFixed(2)}
            </Text>
          </div>

          <ActionMenu
            groups={[
              {
                actions: [
                  {
                    label: t("quotes.manage.updatePrice", "更新价格"),
                    onClick: () => setShowPriceForm(!showPriceForm),
                    icon: <PencilSquare />,
                  },
                  ...(onDuplicate ? [{
                    label: t("actions.duplicate", "Duplicate"),
                    onClick: () => onDuplicate(item),
                    icon: <DocumentSeries />,
                  }] : []),
                ],
              },
              {
                actions: [
                  ...(!isRemoved ? [{
                    label: t("actions.remove", "Remove"),
                    onClick: () => onRemoveItem(item.id),
                    icon: <XCircle />,
                  }] : []),
                ],
              },
            ]}
          />
        </div>
      </div>

      {showPriceForm && (
        <div className="grid grid-cols-1 gap-2 p-3 md:grid-cols-2">
          <div>
            <Form.Label>{t("fields.price", "Price")}</Form.Label>
            <Form.Hint className="!mt-1">
              {t("quotes.manage.overridePriceHint", "Override the original price for this item")}
            </Form.Hint>
          </div>

          <div className="flex items-center gap-1">
            <div className="flex-grow">
              <Form.Item>
                <Form.Control>
                  <CurrencyInput
                    symbol={currencyCode}
                    code={getCurrencySymbol(currencyCode)}
                    defaultValue={item.unit_price}
                    type="numeric"
                    min={0}
                    onBlur={(e) => {
                      const newValue = e.target.value;
                      handleUnitPriceChange(newValue);
                    }}
                    disabled={isRemoved}
                    className="bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover"
                  />
                </Form.Control>
                <Form.ErrorMessage />
              </Form.Item>
            </div>
            <IconButton
              type="button"
              className="flex-shrink"
              variant="transparent"
              onClick={() => setShowPriceForm(false)}
            >
              <XMark className="text-ui-fg-muted" />
            </IconButton>
          </div>
        </div>
      )}
    </div>
  );
};