import { clx, Text } from "@medusajs/ui";
import { getLocaleAmount } from "../../../../lib/money-amount-helpers";

type AmountCellProps = {
  currencyCode: string;
  amount?: number | null;
  originalAmount?: number | null;
  className?: string;
};

export const AmountCell = ({
  currencyCode,
  amount,
  originalAmount,
  className,
}: AmountCellProps) => {
  if (typeof amount === "undefined" || amount === null) {
    return <Text size="small">-</Text>;
  }

  const formatted = getLocaleAmount(amount, currencyCode);
  const hasOriginalAmount = originalAmount && originalAmount !== amount;

  return (
    <div className={clx("flex flex-col items-end", className)}>
      {hasOriginalAmount && (
        <Text size="small" className="text-ui-fg-muted line-through">
          {getLocaleAmount(originalAmount, currencyCode)}
        </Text>
      )}
      <Text size="small" className="text-ui-fg-base">
        {formatted}
      </Text>
    </div>
  );
};