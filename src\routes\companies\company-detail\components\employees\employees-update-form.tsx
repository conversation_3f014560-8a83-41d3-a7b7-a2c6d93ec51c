import {
  Button,
  Container,
  CurrencyInput,
  Drawer,
  Label,
  Switch,
  Table,
  Text,
  Tooltip,
} from "@medusajs/ui"
import { InformationCircleSolid } from "@medusajs/icons"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Company, Employee, AdminUpdateEmployee } from "../../../../../types"
import { getCurrencySymbol } from "../../../../../lib/data/currencies"

// CoolSwitch component - matches old version styling
interface CoolSwitchProps {
  fieldName: string
  label: string
  description: string
  checked: boolean
  onChange: (checked: boolean) => void
  tooltip?: string
}

const CoolSwitch = ({ fieldName, label, description, checked, onChange, tooltip }: CoolSwitchProps) => {
  return (
    <Container className="bg-ui-bg-subtle flex flex-col gap-2">
      <div className="flex items-center gap-2">
        <Switch name={fieldName} checked={checked} onCheckedChange={onChange} />
        <Label size="xsmall" className="txt-compact-small font-medium">
          {label}
        </Label>
        {tooltip && (
          <Tooltip content={tooltip} className="z-50">
            <InformationCircleSolid color="gray" />
          </Tooltip>
        )}
      </div>
      <Text size="xsmall">{description}</Text>
    </Container>
  )
}

interface EmployeesUpdateFormProps {
  company: Company
  employee: Employee
  handleSubmit: (data: AdminUpdateEmployee) => Promise<void>
  loading: boolean
}

export const EmployeesUpdateForm = ({
  company,
  employee,
  handleSubmit,
  loading,
}: EmployeesUpdateFormProps) => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState<{
    is_admin: boolean
    spending_limit: string
  }>({
    is_admin: employee.is_admin || false,
    spending_limit: employee.spending_limit?.toString() || "0",
  })

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const spendingLimit = formData.spending_limit
      ? Number(formData.spending_limit)
      : undefined

    const data: AdminUpdateEmployee = {
      ...formData,
      id: employee.id,
      is_admin: formData.is_admin,
      spending_limit: spendingLimit,
      raw_spending_limit: {
        value: spendingLimit,
      },
    }

    await handleSubmit(data)
  }

  return (
    <form onSubmit={onSubmit}>
      <Drawer.Body className="p-4">
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-2 mb-4">
            <div className="flex items-center justify-between">
              <h2 className="h2-core">详细信息</h2>
              <a
                href={`/customers/${employee.customer.id}/edit`}
                className="txt-compact-small text-ui-fg-interactive hover:text-ui-fg-interactive-hover self-end"
              >
                编辑客户详情
              </a>
            </div>
            <Container className="p-0 overflow-hidden">
              <Table>
                <Table.Body>
                  <Table.Row>
                    <Table.Cell className="font-medium font-sans txt-compact-small">
                      姓名
                    </Table.Cell>
                    <Table.Cell>
                      {employee.customer.first_name} {employee.customer.last_name}
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell className="font-medium font-sans txt-compact-small">
                      邮箱
                    </Table.Cell>
                    <Table.Cell>{employee.customer.email}</Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell className="font-medium font-sans txt-compact-small">
                      电话
                    </Table.Cell>
                    <Table.Cell>{employee.customer.phone}</Table.Cell>
                  </Table.Row>
                  <Table.Row>
                    <Table.Cell className="font-medium font-sans txt-compact-small">
                      公司
                    </Table.Cell>
                    <Table.Cell>{company.name}</Table.Cell>
                  </Table.Row>
                </Table.Body>
              </Table>
            </Container>
          </div>
          <div className="flex flex-col gap-4">
            <h2 className="h2-core">权限</h2>
            <div className="flex flex-col gap-2">
              <Label size="xsmall" className="txt-compact-small font-medium">
                消费限额 ({company.currency_code?.toUpperCase() || "USD"})
              </Label>
              <CurrencyInput
                symbol={getCurrencySymbol(company.currency_code)}
                code={company.currency_code || "USD"}
                name="spending_limit"
                value={formData.spending_limit}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    spending_limit: e.target.value.replace(/[^0-9.]/g, ""),
                  })
                }
                placeholder="输入消费限额"
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label size="xsmall" className="txt-compact-small font-medium">
                管理员权限
              </Label>
              <CoolSwitch
                fieldName="is_admin"
                label="管理员"
                description="授予该员工管理员权限"
                checked={formData.is_admin}
                onChange={(checked) =>
                  setFormData({ ...formData, is_admin: checked })
                }
                tooltip="管理员可以管理公司设置"
              />
            </div>
          </div>
        </div>
      </Drawer.Body>

      <Drawer.Footer>
        <Drawer.Close asChild>
          <Button variant="secondary">取消</Button>
        </Drawer.Close>
        <Button type="submit" disabled={loading}>
          {loading ? "保存中..." : "保存"}
        </Button>
      </Drawer.Footer>
    </form>
  )
}
