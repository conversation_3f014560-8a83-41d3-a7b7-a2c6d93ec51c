import { renderHook } from "@testing-library/react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactNode } from "react"
import { 
  useApprovals, 
  useUpdateApproval, 
  useUpdateApprovalSettings,
  useApprovalSettings
} from "../approvals"

// 创建测试用的QueryClient
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

// 测试包装器
const createWrapper = () => {
  const queryClient = createTestQueryClient()
  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe("Approvals API Hooks", () => {
  beforeEach(() => {
    // Mock fetch
    global.fetch = jest.fn()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  describe("useApprovals", () => {
    it("should create query with correct key", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useApprovals(), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.data).toBe("undefined")
      expect(typeof result.current.isLoading).toBe("boolean")
      expect(typeof result.current.error).toBe("object")
    })

    it("should create query with parameters", () => {
      const wrapper = createWrapper()
      const query = { status: "pending", company_id: "company-123" }
      const { result } = renderHook(() => useApprovals(query), { wrapper })
      
      expect(result.current).toBeDefined()
    })
  })

  describe("useUpdateApproval", () => {
    it("should create mutation for updating approval", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useUpdateApproval("approval-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
      expect(typeof result.current.mutateAsync).toBe("function")
    })
  })

  describe("useUpdateApprovalSettings", () => {
    it("should create mutation for updating approval settings", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useUpdateApprovalSettings("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.mutate).toBe("function")
    })
  })

  describe("useApprovalSettings", () => {
    it("should create query for approval settings", () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useApprovalSettings("company-123"), { wrapper })
      
      expect(result.current).toBeDefined()
      expect(typeof result.current.data).toBe("undefined")
      expect(typeof result.current.isLoading).toBe("boolean")
    })
  })
})