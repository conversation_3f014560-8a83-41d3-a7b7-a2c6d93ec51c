import { But<PERSON>, Container, <PERSON><PERSON>, Textarea, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Check, X } from "@medusajs/icons"
import { useUpdateApproval } from "../../../../hooks/api/approvals"
import { Approval } from "../../../../types"

interface ApprovalActionsSectionProps {
  approval: Approval
}

export const ApprovalActionsSection = ({ approval }: ApprovalActionsSectionProps) => {
  const { t } = useTranslation()
  const [reason, setReason] = useState("")
  const [showReasonInput, setShowReasonInput] = useState(false)
  const [actionType, setActionType] = useState<"approved" | "rejected" | null>(null)

  const updateApprovalMutation = useUpdateApproval(approval.id, {
    onSuccess: () => {
      const message = actionType === "approved" 
        ? t("approvals.toasts.approveSuccess", "Successfully approved")
        : t("approvals.toasts.rejectSuccess", "Successfully rejected")
      toast.success(message)
      setShowReasonInput(false)
      setReason("")
      setActionType(null)
    },
    onError: (error) => {
      const message = actionType === "approved"
        ? t("approvals.toasts.approveError", "Failed to approve")
        : t("approvals.toasts.rejectError", "Failed to reject")
      toast.error(message)
    },
  })

  const handleApprove = () => {
    setActionType("approved")
    setShowReasonInput(true)
  }

  const handleReject = () => {
    setActionType("rejected")
    setShowReasonInput(true)
  }

  const handleSubmit = () => {
    if (!actionType) return

    updateApprovalMutation.mutate({
      status: actionType,
      reason: reason.trim() || undefined,
    })
  }

  const handleCancel = () => {
    setShowReasonInput(false)
    setReason("")
    setActionType(null)
  }

  // 只在待审批状态时显示操作按钮
  const canTakeAction = approval.status === "pending"

  if (!canTakeAction) {
    return null
  }

  return (
    <Container className="p-6">
      <Heading level="h2" className="mb-4">
        {t("approvals.details.actions", "Actions")}
      </Heading>
      
      {!showReasonInput ? (
        <div className="flex gap-2">
          <Button
            variant="primary"
            onClick={handleApprove}
          >
            <Check className="h-4 w-4" />
            {t("approvals.actions.approve", "Approve")}
          </Button>
          
          <Button
            variant="danger"
            onClick={handleReject}
          >
            <X className="h-4 w-4" />
            {t("approvals.actions.reject", "Reject")}
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <div>
            <Textarea
              placeholder={t("approvals.actions.reasonPlaceholder", "Enter reason (optional)...")}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              className="w-full"
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={actionType === "approved" ? "primary" : "danger"}
              onClick={handleSubmit}
              isLoading={updateApprovalMutation.isPending}
            >
              {actionType === "approved" 
                ? t("approvals.actions.confirmApprove", "Confirm Approve")
                : t("approvals.actions.confirmReject", "Confirm Reject")
              }
            </Button>
            
            <Button
              variant="secondary"
              onClick={handleCancel}
              disabled={updateApprovalMutation.isPending}
            >
              {t("general.cancel", "Cancel")}
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}