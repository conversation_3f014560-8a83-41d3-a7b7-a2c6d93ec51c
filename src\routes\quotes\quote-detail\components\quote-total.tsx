import { Text } from "@medusajs/ui";
import { useTranslation } from "react-i18next";
import { getLocaleAmount } from "../../../../lib/money-amount-helpers";

export const QuoteTotal = ({ order, preview }: { order: any; preview: any }) => {
  const { t } = useTranslation();

  return (
    <div className=" flex flex-col gap-y-2 px-6 py-4">
      <div className="text-ui-fg-base flex items-center justify-between">
        <Text
          weight="plus"
          className="text-ui-fg-subtle"
          size="small"
          leading="compact"
        >
          {t("quotes.details.originalTotal", "Original Total")}
        </Text>
        <Text
          weight="plus"
          className="text-ui-fg-subtle"
          size="small"
          leading="compact"
        >
          {getLocaleAmount(order.total, order.currency_code)}
        </Text>
      </div>

      <div className="text-ui-fg-base flex items-center justify-between">
        <Text
          className="text-ui-fg-subtle text-semibold"
          size="small"
          leading="compact"
          weight="plus"
        >
          {t("quotes.details.quoteTotal", "Quote Total")}
        </Text>
        <Text
          className="text-ui-fg-subtle text-bold"
          size="small"
          leading="compact"
          weight="plus"
        >
          {getLocaleAmount(preview.summary.current_order_total, order.currency_code)}
        </Text>
      </div>
    </div>
  );
};