import { But<PERSON>, Drawer, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Company, Employee, AdminUpdateEmployee } from "../../../../../types"
import { useUpdateEmployee } from "../../../../../hooks/api/companies"
import { EmployeesUpdateForm } from "./employees-update-form"

interface EmployeesUpdateDrawerProps {
  company: Company
  employee: Employee
  open: boolean
  setOpen: (open: boolean) => void
}

export const EmployeesUpdateDrawer = ({
  company,
  employee,
  open,
  setOpen,
}: EmployeesUpdateDrawerProps) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)

  const { mutateAsync: updateEmployee } = useUpdateEmployee(company.id, employee.id)

  const handleSubmit = async (data: AdminUpdateEmployee) => {
    setIsLoading(true)
    try {
      await updateEmployee(data, {
        onSuccess: () => {
          toast.success("员工信息更新成功")
          setOpen(false)
        },
        onError: (e) => {
          toast.error(e.message)
        },
      })
    } catch (error) {
      console.error("Error updating employee:", error)
      toast.error("更新员工信息失败")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>编辑员工</Drawer.Title>
        </Drawer.Header>
        <EmployeesUpdateForm
          company={company}
          employee={employee}
          handleSubmit={handleSubmit}
          loading={isLoading}
        />
      </Drawer.Content>
    </Drawer>
  )
}
