import { Badge } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Approval } from "../../types"

interface ApprovalStatusBadgeProps {
  status: Approval["status"]
}

export const ApprovalStatusBadge = ({ status }: ApprovalStatusBadgeProps) => {
  const { t } = useTranslation()

  const getStatusConfig = (status: Approval["status"]) => {
    switch (status) {
      case "pending":
        return { 
          color: "orange" as const, 
          label: t("approvals.status.pending", "Pending") 
        }
      case "approved":
        return { 
          color: "green" as const, 
          label: t("approvals.status.approved", "Approved") 
        }
      case "rejected":
        return { 
          color: "red" as const, 
          label: t("approvals.status.rejected", "Rejected") 
        }
      case "expired":
        return { 
          color: "grey" as const, 
          label: t("approvals.status.expired", "Expired") 
        }
      default:
        return { 
          color: "grey" as const, 
          label: t("approvals.status.unknown", "Unknown") 
        }
    }
  }

  const { color, label } = getStatusConfig(status)

  return (
    <Badge size="small" color={color}>
      {label}
    </Badge>
  )
}