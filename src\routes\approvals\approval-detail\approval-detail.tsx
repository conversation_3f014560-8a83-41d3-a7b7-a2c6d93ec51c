import { useParams } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { TwoColumnPage } from "../../../components/layout/pages"
import { useExtension } from "../../../providers/extension-provider"
import { useApprovals } from "../../../hooks/api/approvals"
import { 
  ApprovalDetailsSection, 
  ApprovalActionsSection 
} from "./components"

export const ApprovalDetail = () => {
  const { id } = useParams()
  const { t } = useTranslation()
  const { getWidgets } = useExtension()

  // 由于没有单个审批的API，我们从列表中查找
  const { approvals, isLoading, isError, error } = useApprovals()
  
  const approval = approvals?.find(a => a.id === id)

  if (isLoading) {
    return <SingleColumnPageSkeleton sections={2} showJSON showMetadata />
  }

  if (isError) {
    throw error
  }

  if (!approval) {
    throw new Response("Approval not found", { status: 404 })
  }

  return (
    <TwoColumnPage
      widgets={{
        before: getWidgets("approval.details.before"),
        after: getWidgets("approval.details.after"),
        sideAfter: getWidgets("approval.details.side.after"),
        sideBefore: getWidgets("approval.details.side.before"),
      }}
      data={approval}
      hasOutlet
      showJSON
      showMetadata
    >
      <TwoColumnPage.Main>
        <ApprovalDetailsSection approval={approval} />
      </TwoColumnPage.Main>
      <TwoColumnPage.Sidebar>
        <ApprovalActionsSection approval={approval} />
      </TwoColumnPage.Sidebar>
    </TwoColumnPage>
  )
}