{"$schema": "./$schema.json", "general": {"ascending": "Didėjančia tvarka", "descending": "Mažėjančia tvarka", "add": "<PERSON><PERSON><PERSON><PERSON>", "start": "Pradžia", "end": "Pabaiga", "open": "<PERSON><PERSON><PERSON><PERSON>", "close": "Uždaryti", "apply": "<PERSON><PERSON><PERSON>", "range": "Intervalas", "search": "Pa<PERSON>š<PERSON>", "of": "i<PERSON>", "results": "rezultatai", "pages": "<PERSON><PERSON><PERSON><PERSON>", "next": "Kitas", "prev": "Praeitas", "is": "yra", "timeline": "<PERSON><PERSON> j<PERSON>", "success": "Payvko", "warning": "Įspėjimas", "tip": "<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>", "select": "Pasirinkite", "selected": "Pasirinktas", "enabled": "Įjungtas", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Pasibaigęs", "active": "Aktyvus", "revoked": "<PERSON><PERSON>uk<PERSON>", "new": "<PERSON><PERSON><PERSON>", "modified": "<PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON>ė<PERSON>", "removed": "<PERSON><PERSON><PERSON><PERSON>", "admin": "<PERSON><PERSON>", "store": "Parduotuvė", "details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "items_one": "{{count}} da<PERSON><PERSON>", "items_other": "{{count}} da<PERSON><PERSON>", "countSelected": "{{count}} pasirinkta", "countOfTotalSelected": "{{count}} iš {{total}} pasirinkta", "plusCount": "+ {{count}}", "plusCountMore": "+ {{count}} dar", "areYouSure": "Jūs įsitikinę?", "areYouSureDescription": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{entity}} {{title}}. <PERSON><PERSON> ve<PERSON>.", "noRecordsFound": "Įrašų nerasta", "typeToConfirm": "Įveskite {val} pat<PERSON><PERSON><PERSON><PERSON>:", "noResultsTitle": "Rezultatų nėra", "noResultsMessage": "Pabandykite pakeisti paieškos kriterijus", "noSearchResults": "Paieškos rezultatų nėra", "noSearchResultsFor": "Paieškos rezultatų nėra pagal <0>'{{query}}'</0>", "noRecordsTitle": "Įrašų nėra", "noRecordsMessage": "Nėra įrašų parodymui", "unsavedChangesTitle": "Tikrai norite išeiti iš šios formos?", "unsavedChangesDescription": "Turite neišsaugotų pakeitimų, kurie bus prarasti jei išeisite iš šios formos.", "includesTaxTooltip": "Kainos šiame stulpelyje nurodytos su mokesčiais.", "excludesTaxTooltip": "Kainos šiame stulpelyje nurodytos be mokesčių.", "noMoreData": "Daugiau duomenų nėra"}, "json": {"header": "JSON", "numberOfKeys_one": "{{count}} įrašas", "numberOfKeys_other": "{{count}} įrašų", "drawer": {"header_one": "JSON <0>· {{count}} įrašas</0>", "header_other": "JSON <0>· {{count}} įrašų</0>", "description": "Peržiūrėkite šio objekto JSON duomenis"}}, "metadata": {"header": "Met<PERSON><PERSON><PERSON><PERSON>", "numberOfKeys_one": "{{count}} įrašas", "numberOfKeys_other": "{{count}} įrašų", "edit": {"header": "<PERSON><PERSON><PERSON>", "description": "Keisti šio objekto metaduomenis.", "successToast": "Metaduomen<PERSON><PERSON>.", "actions": {"insertRowAbove": "Įterpti eilutę virš", "insertRowBelow": "Įterpti eilutę po", "deleteRow": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>"}, "labels": {"key": "įrašas", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "complexRow": {"label": "<PERSON> kuri<PERSON> e<PERSON><PERSON>", "description": "Šiame objekte yra neprimityvių metaduomenų, pvz., masyvų ar objektų, kurių čia redaguoti čia negalima. Norėdami pakeisti išjungtas eilutes, naudokite API tiesiogiai.", "tooltip": "<PERSON><PERSON><PERSON><PERSON>, nes joje yra neprimityvių duomenų."}}}, "validation": {"mustBeInt": "Re<PERSON><PERSON><PERSON><PERSON> turi būti s<PERSON> s<PERSON>.", "mustBePositive": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi būti <PERSON>."}, "actions": {"save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "saveAsDraft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaip juodraštį", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "copied": "Nukopijuota", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "revoke": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "forceConfirm": "Priverst<PERSON><PERSON>", "continueEdit": "Tęsti redagavimą", "enable": "Įjungti", "disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undo": "Atstaty<PERSON>", "complete": "Baigtas", "viewDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti informaciją", "back": "Atgal", "close": "Uždaryti", "showMore": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continueWithEmail": "Tęstu su el. paštu", "idCopiedToClipboard": "ID nukopijuotas į atmintį", "addReason": "<PERSON><PERSON><PERSON><PERSON> priežastį", "addNote": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Atstaty<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "addItems": "<PERSON><PERSON><PERSON><PERSON>", "download": "Parsisiųsti", "clear": "Išvalyti", "clearAll": "Išvalyti visus", "apply": "Pritaikyti", "add": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "browse": "Naršyti", "logout": "<PERSON>si<PERSON><PERSON><PERSON>", "hide": "Paslėpti", "export": "Eksportuoti", "import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cannotUndo": "Šio ve<PERSON> pasekm<PERSON>s <PERSON>tom<PERSON>"}, "operators": {"in": "<PERSON><PERSON>"}, "app": {"search": {"label": "Ieškoti", "title": "Pa<PERSON>š<PERSON>", "description": "Paieška visoje parduotuvėje: tarp užsakymų, prekių, klientų, kt.", "allAreas": "Vios sritys", "navigation": "Navigacija", "openResult": "Atidaryti rezultatą", "showMore": "<PERSON><PERSON><PERSON>", "placeholder": "Peršok arba rask bet ką...", "noResultsTitle": "Rezultatų nerasta", "noResultsMessage": "<PERSON><PERSON> ne<PERSON>, atitinkančio Jūsų piešką.", "emptySearchTitle": "<PERSON><PERSON><PERSON><PERSON>, įveskite", "emptySearchMessage": "Įveskite paieškos žodį ar frazę.", "loadMore": "<PERSON><PERSON><PERSON><PERSON> dar {{count}}", "groups": {"all": "Visos sritys", "customer": "Klientai", "customerGroup": "Klientų grupės", "product": "Prek<PERSON><PERSON>", "productVariant": "Prekių variantai", "inventory": "Atsargos", "reservation": "Rezervacijos", "category": "<PERSON><PERSON><PERSON><PERSON>", "collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "Užsakymai", "promotion": "<PERSON><PERSON><PERSON><PERSON>", "campaign": "<PERSON><PERSON><PERSON><PERSON>", "priceList": "<PERSON>nor<PERSON>", "user": "Administratoriai", "region": "Regionai", "taxRegion": "Mokesčių regionai", "returnReason": "Grąžinimo priežastys", "salesChannel": "Pardavimų kanalai", "productType": "Prekių tipai", "productTag": "Prekių žymos", "location": "Sandėliavimo vietos", "shippingProfile": "<PERSON><PERSON><PERSON> tipas", "publishableApiKey": "Vieši API raktai", "secretApiKey": "Slapti API raktai", "command": "<PERSON><PERSON><PERSON>", "navigation": "Navigacija"}}, "keyboardShortcuts": {"pageShortcut": "<PERSON><PERSON><PERSON><PERSON> į", "settingShortcut": "Nustatymai", "commandShortcut": "<PERSON><PERSON><PERSON>", "then": "tada", "navigation": {"goToOrders": "Užsakymai", "goToProducts": "Prek<PERSON><PERSON>", "goToCollections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToCategories": "<PERSON><PERSON><PERSON><PERSON>", "goToCustomers": "Klientai", "goToCustomerGroups": "Klientų grupės", "goToInventory": "Atsargos", "goToReservations": "Rezervacijos", "goToPriceLists": "<PERSON>nor<PERSON>", "goToPromotions": "<PERSON><PERSON><PERSON><PERSON>", "goToCampaigns": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"goToSettings": "Nustatymai", "goToStore": "Parduotuvė", "goToUsers": "Administratoriai", "goToRegions": "Regionai", "goToTaxRegions": "Mokesčių regionai", "goToSalesChannels": "Pardavimų kanalai", "goToProductTypes": "Prekių tipai", "goToLocations": "Vietos", "goToPublishableApiKeys": "Vieši API raktai", "goToSecretApiKeys": "Slapti API raktai", "goToWorkflows": "<PERSON><PERSON> e<PERSON>s", "goToProfile": "Paskyra", "goToReturnReasons": "Grąžinimo priežastys"}}, "menus": {"user": {"documentation": "Dokumentacija", "changelog": "Pakeitimų žurnalas", "shortcuts": "Spartieji k<PERSON>šai", "profileSettings": "Paskyros nustatym<PERSON>", "theme": {"label": "<PERSON><PERSON>", "dark": "<PERSON><PERSON>", "light": "Š<PERSON><PERSON>", "system": "Sistemos"}}, "store": {"label": "Parduotuvė", "storeSettings": "Parduotu<PERSON><PERSON><PERSON> n<PERSON>"}, "actions": {"logout": "<PERSON>si<PERSON><PERSON><PERSON>"}}, "nav": {"accessibility": {"title": "Navigacija", "description": "<PERSON><PERSON><PERSON><PERSON>."}, "common": {"extensions": "Plėtiniai"}, "main": {"store": "Parduotuvė", "storeSettings": "Parduotu<PERSON><PERSON><PERSON> n<PERSON>"}, "settings": {"header": "Nustatymai", "general": "<PERSON><PERSON><PERSON>", "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myAccount": "<PERSON><PERSON>"}}}, "dataGrid": {"columns": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetToDefault": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "disabled": "Matomų stulpelių keitimas išjungtas."}, "shortcuts": {"label": "Spartieji k<PERSON>šai", "commands": {"undo": "Atstaty<PERSON>", "redo": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "paste": "Įklijuoti", "edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "clear": "Išvalyti", "moveUp": "<PERSON><PERSON>", "moveDown": "<PERSON><PERSON>", "moveLeft": "<PERSON><PERSON>", "moveRight": "<PERSON><PERSON>", "moveTop": "Eiti į viršų", "moveBottom": "Eitį į apačią", "selectDown": "Pasirinkti žemiau", "selectUp": "Pasirinkti aukščiau", "selectColumnDown": "Pasirinkti stulpelį žemyn", "selectColumnUp": "Pasirinkti stulpelį aukštyn", "focusToolbar": "Pereitį į įrankių juostą", "focusCancel": "Pereiti prie atšaukimo"}}, "errors": {"fixError": "Pataisykite klaidą", "count_one": "{{count}} k<PERSON>a", "count_other": "{{count}} k<PERSON><PERSON>"}}, "filters": {"sortLabel": "R<PERSON>š<PERSON>ot<PERSON>", "filterLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchLabel": "Ieškoti", "date": {"today": "Šiandien", "lastSevenDays": "Per paskutines 7 dienas", "lastThirtyDays": "Per paskutines 30 dienų", "lastNinetyDays": "Per paskutines 90 dienų", "lastTwelveMonths": "Per paskutinius 12 mėnesių", "custom": "Nustatytas", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "starting": "Pradedant", "ending": "Baigiant"}, "compare": {"lessThan": "Mažiau už", "greaterThan": "Daugiau už", "exact": "Tiksliai", "range": "<PERSON><PERSON><PERSON><PERSON>", "lessThanLabel": "ma<PERSON><PERSON><PERSON> už {{value}}", "greaterThanLabel": "daug<PERSON><PERSON> už {{value}}", "andLabel": "ir"}, "sorting": {"alphabeticallyAsc": "A iki Z", "alphabeticallyDesc": "Z iki A", "dateAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "dateDesc": "Sen<PERSON><PERSON>i v<PERSON>š<PERSON>"}, "radio": {"yes": "<PERSON><PERSON>", "no": "Ne", "true": "<PERSON><PERSON><PERSON>", "false": "Netiesa"}, "addFilter": "<PERSON><PERSON><PERSON><PERSON> filt<PERSON>"}, "errorBoundary": {"badRequestTitle": "400 - <PERSON><PERSON><PERSON>žkla<PERSON>", "badRequestMessage": "Serveris negali suprasti užklausos dėl neteisingos sintaksės.", "notFoundTitle": "404 - <PERSON><PERSON><PERSON> ad<PERSON>", "notFoundMessage": "Patikrinkite adresą ir bandykite dar kartą arba pasinaudokite paieška.", "internalServerErrorTitle": "500 - Vidinė <PERSON><PERSON> k<PERSON>a", "internalServerErrorMessage": "Serveryje įvyko netikėta klaida. Bandykite vėliau.", "defaultTitle": "Įvyko klaida", "defaultMessage": "<PERSON>eikiant šį puslapį įvyko netik<PERSON><PERSON> k<PERSON>.", "noMatchMessage": "Jūsų ieškomas puslapis neegzistuoja.", "backToDashboard": "Atgal į darbastalį"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "shippingAddress": {"header": "Pristat<PERSON><PERSON>", "editHeader": "Keisti pristatymo <PERSON>", "editLabel": "Pristat<PERSON><PERSON>", "label": "Pristat<PERSON><PERSON>"}, "billingAddress": {"header": "Adresas sąskaitoms", "editHeader": "Keisti adresą sąskaitoms", "editLabel": "Adresas sąskaitoms", "label": "Adresas sąskaitoms", "sameAsShipping": "Sutampa su pristatymo adresu"}, "contactHeading": "Kontaktai", "locationHeading": "Vieta"}, "email": {"editHeader": "Keisti el. paš<PERSON>ą", "editLabel": "El. <PERSON>", "label": "El. <PERSON>"}, "transferOwnership": {"header": "<PERSON><PERSON><PERSON>i nuosavybės te<PERSON>", "label": "<PERSON><PERSON><PERSON>i nuosavybės te<PERSON>", "details": {"order": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "currentOwner": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON><PERSON>."}, "newOwner": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON><PERSON>, kuriam bus perkeltas užsakymas."}, "validation": {"mustBeDifferent": "<PERSON><PERSON>jas savininkas turi būti kitas nei da<PERSON>.", "required": "<PERSON><PERSON><PERSON> sa<PERSON> prival<PERSON>."}}, "sales_channels": {"availableIn": "Galima įsigyti <0>{{x}}</0> iš <1>{{y}}</1> pardavimo kanalų"}, "products": {"domain": "Prek<PERSON><PERSON>", "list": {"noRecordsMessage": "Sukurkite pirmą savo prekę, kad pradėtumėte parduoti."}, "edit": {"header": "<PERSON><PERSON><PERSON>", "description": "Keikite prekė<PERSON>", "successToast": "<PERSON><PERSON><PERSON> {{title}} sėmingai pakeista."}, "create": {"title": "Su<PERSON><PERSON>i prekę", "description": "Sukurkite naują prekę.", "header": "Bendra informacija", "tabs": {"details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "organize": "Skirstymas", "variants": "<PERSON><PERSON><PERSON>", "inventory": "Atsargų <PERSON>iniai"}, "errors": {"variants": "Pasirinkite bent vieną variantą.", "options": "Sukurkite bent vieną pasirinkimą.", "uniqueSku": "SKU turi b<PERSON><PERSON> un<PERSON>."}, "inventory": {"heading": "Atsargų <PERSON>iniai", "label": "Pridėti atsargas prie varianto atsargų rinkinio.", "itemPlaceholder": "Pasirinkite atsargą", "quantityPlaceholder": "Koks kiekis reikaling<PERSON> rink<PERSON>?"}, "variants": {"header": "<PERSON><PERSON><PERSON>", "subHeadingTitle": "<PERSON><PERSON>, tai prekė su variantais", "subHeadingDescription": "<PERSON><PERSON>, mes sukursime numatytąjį variantą už jus.", "optionTitle": {"placeholder": "<PERSON><PERSON><PERSON>"}, "optionValues": {"placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"}, "productVariants": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Šis reitingas turė<PERSON> įtakos variantų reikiavimui parduotuvėje.", "alert": "Pridėkite pasirinkimų, kad sukurtumėte variantų.", "tip": "Nepažymėti variantai nebus sukurti. <PERSON><PERSON><PERSON> galite bet kada sukurti ar keisti variantus, bet šis sąrašas aitinka variantus su jūsų prekės pasrinkimais."}, "productOptions": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Apibrėžkite pre<PERSON>, pvz. spalva, dydis ir kt."}}, "successToast": "<PERSON><PERSON><PERSON> {{title}} sėkmingai sukurta."}, "export": {"header": "Esportuoti prekių sąrašą", "description": "Išesportuokite prekių sąrašą į CSV bylą.", "success": {"title": "Apdorojame jūsų eksportą", "description": "Duomenų eksportavimas gali užtrukti kelias minutes. Pranešime kai baigsis."}, "filters": {"title": "Filtrai", "description": "Pritaikyti filtrus lentelės perž<PERSON>ū<PERSON>"}, "columns": {"title": "Stulpeliai", "description": "Pritaikykite išeksporuotus duomenis savo poreikiams"}}, "import": {"header": "Importuoti prekių sąrašą", "uploadLabel": "Import<PERSON><PERSON> prekes", "uploadHint": "Atvilkite CSV bylą arba spustelėkite, kad įkeltumėte", "description": "Importuokite produktus pateikdami CSV bylą iš anksto nustatytu formatu", "template": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kaip sutvar<PERSON>ti sąrašą?", "description": "Atsisiųskite pateiktą šabloną, kad įsitikintumėte, jog la<PERSON><PERSON><PERSON><PERSON> tin<PERSON>."}, "upload": {"title": "Įkelti CSV bylą", "description": "Importuodami galite pridėti arba atnaujinti produktus. Norėdami atnaujinti esamus produktus, turite naudoti esamą nuorodą ir ID, o esamiems variantams atnaujinti turite naudoti esamą ID. Prieš importuojant produktus, jūsų bus paprašyta patvirtinti.", "preprocessing": "A<PERSON><PERSON><PERSON><PERSON>...", "productsToCreate": "Prekės, kurios bus sukurtos", "productsToUpdate": "Prekės, kurios bus atnaujintos"}, "success": {"title": "Apdorojame <PERSON>ų importą", "description": "Duomenų importavimas gali užtrukti kelias minutes. Pranešime kai baigsis."}}, "deleteWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> prekę {{title}}. <PERSON><PERSON> ve<PERSON>.", "variants": {"header": "<PERSON><PERSON><PERSON>", "empty": {"heading": "Variantų nėra", "description": "Nėra rodomų variantų."}, "filtered": {"heading": "Rezultatų nėra", "description": "Nė vienas variantas neatitinka dabartinių filtro kriterijų."}}, "attributes": "Požymiai", "editAttributes": "<PERSON><PERSON> p<PERSON>", "editOptions": "<PERSON><PERSON><PERSON>", "editPrices": "<PERSON><PERSON><PERSON>", "media": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editHint": "Pridėki<PERSON> pre<PERSON>, kad pateikti jas pardu<PERSON>.", "makeThumbnail": "Pagaminti miniatūrą", "uploadImagesLabel": "Įkelti nuotraukas", "uploadImagesHint": "Atvilkite nuotraukas čia arba spustelkite įkėlimui.", "invalidFileType": "'{{name}}' ne<PERSON><PERSON><PERSON><PERSON> bylos tipas. Palaikomi bylų tipai: {{types}}.", "failedToUpload": "Nepavyko įkelti pridėtų nuotraukų. Bandykite dar kartą.", "deleteWarning_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> {{count}} nuotrauką. <PERSON><PERSON> ve<PERSON>.", "deleteWarning_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> {{count}} nuotraukų. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "deleteWarningWithThumbnail_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> {{count}} nuotrauką ir jos miniatūrą. <PERSON>is veiksmas neatstatomas.", "deleteWarningWithThumbnail_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} nuotraukų ir jų miniatūrų. Šis veiksmas neatstatomas.", "thumbnailTooltip": "Miniatūra", "galleryLabel": "<PERSON><PERSON><PERSON>", "downloadImageLabel": "Parsisiųsti nuotrauką", "deleteImageLabel": "Pašalinti nuotrauką", "emptyState": {"header": "Nuotraukų dar nėra", "description": "Pridėkite prekės nuo<PERSON> kad <PERSON> jas parduotuv<PERSON>", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "successToast": "Nuotraukos s<PERSON> atnaujintos."}, "discountableHint": "<PERSON><PERSON>, nuolaidos prekei netaikomos.", "noSalesChannels": "Nėra nei vienam pardavimo kanale", "variantCount_one": "{{count}} variantas", "variantCount_other": "{{count}} variantai", "deleteVariantWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> pre<PERSON> variantą {{title}}. <PERSON><PERSON> ve<PERSON>.", "productStatus": {"draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "published": "Publikuojama", "proposed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected": "Atmes<PERSON>"}, "fields": {"title": {"label": "Pavadinimas", "hint": "Pavadinkite prekę trumpai ir aiškiai.<0/>50-60 simbolių yra rekomenduojamas ilgis paieš<PERSON> sistemoms.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> stri<PERSON>"}, "subtitle": {"label": "Paantraštė", "placeholder": "Šilt<PERSON> ir jauku"}, "handle": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Nuoroda naudojama norint nurodyti prekės adresą jūsų parduotuvėje. <PERSON><PERSON>, nuoroda bus sugeneruota iš prek<PERSON>s pavadin<PERSON>.", "placeholder": "žieminė-striukė"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Aprašykite prekę trumpai ir aiškiai.<0/>120-160 simbolių yra rekomenduojamas ilgis paieš<PERSON> sistemoms.", "placeholder": "<PERSON><PERSON><PERSON> ir jauki s<PERSON>"}, "discountable": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON>, nuolaidos šiai prekei netaikomos"}, "shipping_profile": {"label": "Pristatymo eiga", "hint": "Priskirkite prekės pristatymo e<PERSON>ą"}, "type": {"label": "Tipas"}, "collection": {"label": "Kolekcija"}, "categories": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "tags": {"label": "<PERSON><PERSON><PERSON>"}, "sales_channels": {"label": "Pardavimų kanalai", "hint": "<PERSON>i ne<PERSON> kit<PERSON>, <PERSON>i pre<PERSON> bus prieinama tik pagrindiniam pardavimo kanale."}, "countryOrigin": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "material": {"label": "Medžiaga"}, "width": {"label": "<PERSON><PERSON><PERSON>"}, "length": {"label": "Length"}, "height": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "weight": {"label": "<PERSON><PERSON><PERSON>"}, "options": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Pasirinkimai naudojami norint a<PERSON><PERSON> pre<PERSON> spal<PERSON>, dydį ir pan.", "add": "<PERSON><PERSON><PERSON><PERSON>", "optionTitle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "optionTitlePlaceholder": "Spalva", "variations": "Pasirinkimai (atskirti kableliu)", "variantionsPlaceholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>"}, "variants": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Nepažymėti variantai nebus sukurti. Šis reitingavimas įtakos variantų rikiavimą parduotuvėje."}, "mid_code": {"label": "Gamintojo kodas"}, "hs_code": {"label": "HS kodas"}}, "variant": {"edit": {"header": "Keisti variantą", "success": "Prekės variantas sėkmingai pakeistas"}, "create": {"header": "<PERSON><PERSON><PERSON><PERSON> variantas"}, "deleteWarning": "Tikrai norite pašalinti šį variantą?", "pricesPagination": "1 - {{current}} iš {{total}} kainų", "tableItemAvailable": "{{availableCount}} laisva", "tableItem_one": "{{availableCount}} laisva {{locationCount}} vietoje", "tableItem_other": "{{availableCount}} laisvų {{locationCount}} vietose", "inventory": {"notManaged": "Nevaldomos", "manageItems": "<PERSON><PERSON><PERSON><PERSON>", "notManagedDesc": "Šio varianto atsargos netvarkomos. Įjunkite „Tvarkyti atsargas“, kad gal<PERSON>tum<PERSON>te sekti varianto atsargas.", "manageKit": "Tvarkyti atsargų rinkinį", "navigateToItem": "Eiti į atsargas", "actions": {"inventoryItems": "Eiti į atsargas", "inventoryKit": "<PERSON><PERSON><PERSON>"}, "inventoryKit": "Atsargų rinkinys", "inventoryKitHint": "Ar <PERSON>is variantas susideda iš kelių atargų?", "validation": {"itemId": "Pasirinkite atsargas", "quantity": "<PERSON><PERSON><PERSON>. Įveskite teigiamą skaičių."}, "header": "Atsargos ir likuč<PERSON>i", "editItemDetails": "<PERSON><PERSON><PERSON>", "manageInventoryLabel": "<PERSON><PERSON><PERSON><PERSON>", "manageInventoryHint": "<PERSON>, mes pakeisime atargų likučius kai užsakymai ar grąžinimai sukuriami.", "allowBackordersLabel": "Leisti užsakyti kai n<PERSON>ra", "allowBackordersHint": "<PERSON>, klientai gali p<PERSON>ti variantą net jei neturime reikiamo kiekio.", "toast": {"levelsBatch": "Atsargų likučiai atnaujinti.", "update": "Atsargos sėkmingai atnaujintos.", "updateLevel": "Likučiai sėkmingai atnaujinti.", "itemsManageSuccess": "Atsargos sėkmingai pakeistos."}}}, "options": {"header": "Pasirink<PERSON><PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON>", "successToast": "Pasirinkimas {{title}} sėkmingai pakeistas."}, "create": {"header": "Su<PERSON><PERSON><PERSON> p<PERSON>", "successToast": "Pasirinkimas {{title}} sėkming<PERSON> sukurtas."}, "deleteWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> pre<PERSON> p<PERSON>: {{title}}. <PERSON><PERSON> ve<PERSON>."}, "organization": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": {"header": "Keisti išdėstymą", "toasts": {"success": "{{title}} i<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> p<PERSON>."}}}, "stock": {"heading": "Tvarkykite atsargų likučius ir vietas", "description": "Pakeiskite atsargų likučius visiems prekės variantams.", "loading": "<PERSON><PERSON><PERSON><PERSON>, gali šiek tiek užtrukti...", "tooltips": {"alreadyManaged": "<PERSON><PERSON> atsargos jau yar redaguojamos prie {{title}}.", "alreadyManagedWithSku": "<PERSON><PERSON> atsargos jau yar redaguojamos prie {{title}} ({{sku}})."}}, "shippingProfile": {"header": "<PERSON><PERSON><PERSON> n<PERSON>", "edit": {"header": "<PERSON><PERSON><PERSON> n<PERSON>", "toasts": {"success": "<PERSON><PERSON><PERSON> n<PERSON> {{title}} s<PERSON><PERSON><PERSON><PERSON> at<PERSON>."}}, "create": {"errors": {"required": "Siuntimo paskyra privaloma"}}}, "toasts": {"delete": {"success": {"header": "<PERSON><PERSON><PERSON>", "description": "{{title}} s<PERSON><PERSON><PERSON><PERSON> p<PERSON>."}, "error": {"header": "Nepavyko <PERSON>"}}}}, "collections": {"domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Suskirstykite prekes į kolekcijas.", "createCollection": "Su<PERSON><PERSON>i k<PERSON>ją", "createCollectionHint": "Sukurkite naują kolekciją prekių skirstymui.", "createSuccess": "Kolekcija sėkmingai sukurta.", "editCollection": "<PERSON><PERSON><PERSON> k<PERSON>", "handleTooltip": "Nuordoda naudojama norint nurodyti kolekcijos adresą parduotuvėje. <PERSON>i <PERSON>, nuoroda bus sugenruota iš kolekcijos pavadinimo.", "deleteWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> k<PERSON> {{title}}. <PERSON><PERSON> ve<PERSON>.", "removeSingleProductWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> prekę {{title}} iš <PERSON>. Šis veiksmas neatstatomas.", "removeProductsWarning_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} prekę iš kole<PERSON>. Šis veiksmas neatstatomas.", "removeProductsWarning_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} preki<PERSON> iš kole<PERSON>. Šis veiksmas neatstatomas.", "products": {"list": {"noRecordsMessage": "Kolekcijoje prekių nėra"}, "add": {"successToast_one": "Prekė sėkmingai pridėta į kolekciją.", "successToast_other": "Prek<PERSON><PERSON> s<PERSON> p<PERSON>ė<PERSON> į kolekciją."}, "remove": {"successToast_one": "<PERSON><PERSON><PERSON> s<PERSON>k<PERSON> p<PERSON>ša<PERSON> i<PERSON>.", "successToast_other": "<PERSON>k<PERSON><PERSON> p<PERSON> i<PERSON>."}}}, "categories": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Suskirstykite prekes į ka<PERSON><PERSON><PERSON><PERSON>, kurias galite rikiuoti ir išdėstyti hierarchiškai.", "create": {"header": "<PERSON><PERSON><PERSON><PERSON> kategorij<PERSON>", "hint": "Sukurkite naują kategoriją prekių skirstymui.", "tabs": {"details": "Nustatymai", "organize": "Tvarkyti eiliškumą"}, "successToast": "<PERSON><PERSON><PERSON> {{name}} sėkmingai sukurta."}, "edit": {"header": "<PERSON><PERSON><PERSON> ka<PERSON>", "description": "Pakeistite kategorijos nustatymus ar rikiavimą.", "successToast": "Kategorija s<PERSON>kmingai pakeista."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> kate<PERSON> {{name}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "<PERSON><PERSON><PERSON> {{name}} s<PERSON><PERSON><PERSON><PERSON> p<PERSON>."}, "products": {"add": {"disabledTooltip": "<PERSON><PERSON><PERSON> jau yra š<PERSON> kategor<PERSON>.", "successToast_one": "<PERSON><PERSON><PERSON><PERSON> {{count}} prekė į kategoriją.", "successToast_other": "<PERSON><PERSON><PERSON><PERSON> {{count}} preki<PERSON> į kategoriją."}, "remove": {"confirmation_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} prekę iš kategor<PERSON>. Šis veiksmas neatstatomas.", "confirmation_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} preki<PERSON> iš kategori<PERSON>. Šis veiksmas neatstatomas.", "successToast_one": "<PERSON><PERSON><PERSON><PERSON> {{count}} prek<PERSON> iš ka<PERSON>.", "successToast_other": "<PERSON><PERSON><PERSON><PERSON> {{count}} pre<PERSON><PERSON> iš kate<PERSON>."}, "list": {"noRecordsMessage": "Kategorijoje prekių nėra."}}, "organize": {"header": "<PERSON><PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON>i rikiavimą"}, "fields": {"visibility": {"label": "<PERSON><PERSON><PERSON><PERSON>", "internal": "<PERSON><PERSON><PERSON><PERSON>", "public": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "active": "Įjungta", "inactive": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "path": {"label": "<PERSON><PERSON><PERSON>", "tooltip": "Rodyti pilną kategorijos keli<PERSON>."}, "children": {"label": "Vaikai"}, "new": {"label": "<PERSON><PERSON><PERSON>"}}}, "inventory": {"domain": "Atsargos", "subtitle": "Tvarkykite savo turimas atsargas", "reserved": "Rezervuota", "available": "<PERSON><PERSON><PERSON>", "locationLevels": "Vietos", "associatedVariants": "<PERSON><PERSON><PERSON><PERSON>", "manageLocations": "Tvarkyti vietas", "deleteWarning": "Ke<PERSON><PERSON> p<PERSON><PERSON><PERSON>i atsargas. Šis ve<PERSON>.", "editItemDetails": "<PERSON><PERSON><PERSON>", "create": {"title": "<PERSON><PERSON><PERSON><PERSON> atsar<PERSON>", "details": "Nustatymai", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "locations": "Vietos", "attributes": "Požymiai", "requiresShipping": "Reikalingas siunt<PERSON>", "requiresShippingHint": "Ar šioms atsargoms reikalingas siuntimas?", "successToast": "Atsargos sėkmingai sukurtos."}, "reservation": {"header": "{{itemName}} rezervacija", "editItemDetails": "<PERSON><PERSON>i rezer<PERSON>ją", "lineItemId": "Užsakymo eilutės ID", "orderID": "Užsakymo ID", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Sandėliavimo vieta", "inStockAtLocation": "Sandėlyje šioje vietoje", "availableAtLocation": "Laiva šioje vietoje", "reservedAtLocation": "Rezervuota šioje vietoje", "reservedAmount": "Rezervuoti kiekį", "create": "Sukurti rezervaciją", "itemToReserve": "Rezervuojama pre<PERSON>ė", "quantityPlaceholder": "Kiek norite rezervuoti?", "descriptionPlaceholder": "Koks šios rezervacijos tipas?", "successToast": "Rezervacija sėkmingai sukurta.", "updateSuccessToast": "Rezervacija sėkmingai pakeista.", "deleteSuccessToast": "Rezervacija sėkmingai p<PERSON>šalinta.", "errors": {"noAvaliableQuantity": "Vietos sandėlyje nėra pakankamo laisvo kiekio.", "quantityOutOfRange": "Mažiausias kiekis yra 1, o didžiausias kiekis yra {{max}}"}}, "adjustInventory": {"errors": {"stockedQuantity": "<PERSON><PERSON><PERSON> negali būti ma<PERSON> nei rezervuotas kiekis {{quantity}}."}}, "toast": {"updateLocations": "Vietos sėkmingai atnaujintos.", "updateLevel": "Atsargų kiekis sėkmingai pakeistas.", "updateItem": "Atsargos sėkmingai atnaujintos."}, "stock": {"title": "Keisti atsargų likučius", "description": "Pakeiskite pasirinktų atargų likučius.", "action": "<PERSON><PERSON><PERSON> l<PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disablePrompt_one": "<PERSON><PERSON><PERSON> i<PERSON>i {{count}} vietos likutį. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "disablePrompt_other": "<PERSON><PERSON><PERSON> i<PERSON>i {{count}} vietos likučius. <PERSON><PERSON> veiksma<PERSON> neatstatomas.", "disabledToggleTooltip": "<PERSON><PERSON><PERSON>: išvalyk<PERSON> užsakytus ir/arba rezervuotus likučius prieš išjungiant.", "successToast": "Atsargų likučiai sėkmingai atnaujinti."}}, "giftCards": {"domain": "Dovan<PERSON> kortelės", "editGiftCard": "Keisti dovanų kortelę", "createGiftCard": "Sukurti dovanų kortelę", "createGiftCardHint": "Sukurkite dovanų kortelę, kuri gali būti panaudota atsiskaitymui parduotuvėje.", "selectRegionFirst": "Pirma pasirinkite regioną", "deleteGiftCardWarning": "<PERSON><PERSON><PERSON> pašalinti dovanų kortelę {{code}}. <PERSON><PERSON> ve<PERSON> neatstatom<PERSON>.", "balanceHigherThanValue": "<PERSON><PERSON><PERSON> negali būti didesnis nei pradinė suma.", "balanceLowerThanZero": "<PERSON><PERSON><PERSON> negali būti ne<PERSON>.", "expiryDateHint": "Šalyse galioja skirtingi įstatymai dėl dovanų kortelių galiojimo pabaigos. Prieš nustatydami galiojimo datą, peržiūrėkite vietines taisykles.", "regionHint": "Pakeitus dovanų kortelės regioną, gali pasikeis ir jos valiuta, o tai gali turėti įtakos kortelės vertei.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ar do<PERSON><PERSON> k<PERSON> įjungta, ar <PERSON><PERSON><PERSON><PERSON><PERSON>.", "balance": "<PERSON><PERSON><PERSON>", "currentBalance": "<PERSON><PERSON><PERSON>", "initialBalance": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "personalMessage": "<PERSON><PERSON><PERSON><PERSON>", "recipient": "<PERSON><PERSON><PERSON><PERSON>"}, "customers": {"domain": "Klientai", "list": {"noRecordsMessage": "Jūsų klientai bus rodomi čia."}, "create": {"header": "Su<PERSON><PERSON>i k<PERSON>ą", "hint": "Sukurkite naują k<PERSON>ą ir tvarkykite jo duomenis.", "successToast": "Klientas {{email}} sėkmingai sukurtas."}, "groups": {"label": "Klientų grupės", "remove": "Tikrai norite pa<PERSON>lint<PERSON> klient<PERSON> iš \"{{name}}\" klient<PERSON> grupės?", "removeMany": "Tikrai norite pašalinti klientą iš šių klientų grupių: {{groups}}?", "alreadyAddedTooltip": "Klientas jau yra šioje klientų grupėje.", "list": {"noRecordsMessage": "Šis klientas nepriklauso nei vienai klientų grupei."}, "add": {"success": "<PERSON><PERSON><PERSON> p<PERSON> prie: {{groups}}.", "list": {"noRecordsMessage": "Prašome pirma sukurti klientų grupę."}}, "removed": {"success": "<PERSON><PERSON><PERSON> p<PERSON> iš: {{groups}}.", "list": {"noRecordsMessage": "Prašome pirma sukurti klientų grupę."}}}, "edit": {"header": "<PERSON><PERSON><PERSON>", "emailDisabledTooltip": "El. paštas negali būti pakeistas užsiregistravusiems klientams.", "successToast": "<PERSON><PERSON>as {{email}} sėkmingai pakeistas."}, "delete": {"title": "Pa<PERSON>lint<PERSON>", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> k<PERSON> {{email}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "successToast": "<PERSON><PERSON><PERSON> {{email}} s<PERSON>k<PERSON><PERSON> p<PERSON>."}, "fields": {"guest": "<PERSON><PERSON><PERSON><PERSON>", "registered": "Užsiregistravęs", "groups": "Grup<PERSON><PERSON>"}, "registered": "Užsiregistravęs", "guest": "<PERSON><PERSON><PERSON><PERSON>", "hasAccount": "<PERSON><PERSON>", "addresses": {"title": "<PERSON><PERSON><PERSON>", "fields": {"addressName": "Adreso pavadin<PERSON>s", "address1": "Adreso eilutė 1", "address2": "Adreso eilutė 2", "city": "Miestas", "province": "Provincija", "postalCode": "<PERSON><PERSON><PERSON> k<PERSON>", "country": "<PERSON><PERSON>", "phone": "Telefonas", "company": "Įmonė", "countryCode": "<PERSON><PERSON><PERSON> k<PERSON>", "provinceCode": "<PERSON><PERSON><PERSON><PERSON> koda<PERSON>"}, "create": {"header": "Su<PERSON><PERSON><PERSON> ad<PERSON>", "hint": "Sukurkite naują ad<PERSON> k<PERSON>.", "successToast": "<PERSON><PERSON><PERSON><PERSON> suku<PERSON>."}}}, "customerGroups": {"domain": "Klientų grupės", "subtitle": "Skirstykite klientus į grupes. Grupės gali turėti skirtingas akcijas ir ka<PERSON>.", "list": {"empty": {"heading": "Klientų grupių nėra", "description": "Nėra klientų grupių parodymui."}, "filtered": {"heading": "Rezultatų nėra", "description": "Nei viena klientų grupė neatitinka nustatyto filtro."}}, "create": {"header": "Sukurti klientų grupę", "hint": "Sukurkite naują klientų grupę klientų segmentavimui.", "successToast": "Klientų grupė {{name}} sėkmingai sukurta."}, "edit": {"header": "Keisti klientų grupę", "successToast": "Klientų grupė {{name}} sėkmingai pakeista."}, "delete": {"title": "Pašalinti klientų grupę", "description": "<PERSON><PERSON>te pašalinti klientų grupę {{name}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "successToast": "Klientų grupė {{name}} sėkmingai p<PERSON>šalint<PERSON>."}, "customers": {"alreadyAddedTooltip": "<PERSON><PERSON><PERSON> jau pris<PERSON>rtas prie grupės.", "add": {"successToast_one": "Klientas sėkmingai priskirtas prie grupės.", "successToast_other": "Klientai sėkmingai priskirti prie grupės.", "list": {"noRecordsMessage": "Pirma sukurkite klientą."}}, "remove": {"title_one": "Pa<PERSON>lint<PERSON>", "title_other": "<PERSON><PERSON><PERSON><PERSON>", "description_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} k<PERSON><PERSON> iš klientų grupės. Šis veiksmas neatstatomas.", "description_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} k<PERSON><PERSON> iš klientų grupės. Šis veiksmas neatstatomas."}, "list": {"noRecordsMessage": "Ši grupė neturi klientų."}}}, "orders": {"domain": "Užsakymai", "claim": "Pretenzi<PERSON>", "exchange": "<PERSON><PERSON><PERSON><PERSON>", "return": "Grąžinimai", "cancelWarning": "<PERSON><PERSON><PERSON> atša<PERSON>ti užsakymą {{id}}. <PERSON><PERSON> ve<PERSON>.", "orderCanceled": "Užsakymas <PERSON> atšauktas", "onDateFromSalesChannel": "{{date}} iš {{salesChannel}}", "list": {"noRecordsMessage": "Jūsų užsakymai bus rodomi čia."}, "status": {"not_paid": "Neapmokėtas", "pending": "<PERSON><PERSON><PERSON>", "completed": "Įvykdytas", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archived": "Archyvuota<PERSON>", "canceled": "<PERSON><PERSON>uk<PERSON>", "requires_action": "Reikia veiksmų"}, "summary": {"requestReturn": "Prašyti grąžinimo", "allocateItems": "Priskirti atsargas", "editOrder": "Keisti užsakymą", "editOrderContinue": "Tęsti užsakymo keitimą", "inventoryKit": "<PERSON><PERSON><PERSON> iš {{count}}x atsargų", "itemTotal": "<PERSON><PERSON><PERSON><PERSON> iš viso", "shippingTotal": "Siuntimas iš viso", "discountTotal": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> viso", "taxTotalIncl": "Mokesčiai iš viso (įskaičiuoti)", "itemSubtotal": "<PERSON><PERSON><PERSON><PERSON> viso", "shippingSubtotal": "Siuntimas viso", "discountSubtotal": "<PERSON><PERSON><PERSON><PERSON> viso", "taxTotal": "Mokesčiai viso"}, "transfer": {"title": "Perleisti nuosavybę", "requestSuccess": "Užsaky<PERSON> n<PERSON> per<PERSON>id<PERSON> p<PERSON> i<PERSON> į {{email}}.", "currentOwner": "<PERSON><PERSON><PERSON><PERSON>", "newOwner": "<PERSON><PERSON><PERSON>", "currentOwnerDescription": "Pirkėjas jau susietas su šiuo užsakymu.", "newOwnerDescription": "Pirkėjas kuriam per<PERSON> užsakymmo nuosavybė."}, "payment": {"title": "Apmokėjimas", "isReadyToBeCaptured": "<PERSON><PERSON><PERSON><PERSON><PERSON> <0/> paruoštas nuskaitymui.", "totalPaidByCustomer": "<PERSON><PERSON><PERSON>", "capture": "Nuskaityti mokėjimą", "capture_short": "Nuskaityti", "refund": "Grąžinti", "markAsPaid": "Pažymėti ka<PERSON>", "statusLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "statusTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "status": {"notPaid": "Neapmokėtas", "authorized": "<PERSON><PERSON><PERSON><PERSON>", "partiallyAuthorized": "<PERSON><PERSON><PERSON>", "awaiting": "<PERSON><PERSON><PERSON>", "captured": "Nuskaitytas", "partiallyRefunded": "Dalinai grąžintas", "partiallyCaptured": "Dalinai captured", "refunded": "Gr<PERSON><PERSON><PERSON><PERSON>", "canceled": "<PERSON><PERSON>uk<PERSON>", "requiresAction": "Reikia veiksmų"}, "capturePayment": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurio suma {{amount}}, bus nuskaitytas.", "capturePaymentSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurio suma {{amount}}, sėkmingai nuskaitytas", "markAsPaidPayment": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurio suma {{amount}}, bus pažymėtas kaip at<PERSON>.", "markAsPaidPaymentSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurio suma {{amount}}, s<PERSON>k<PERSON><PERSON> paž<PERSON> kaip <PERSON>", "createRefund": "Sukurti grąžinimą", "refundPaymentSuccess": "return, kurio suma {{amount}}, s<PERSON><PERSON><PERSON><PERSON>", "createRefundWrongQuantity": "<PERSON><PERSON><PERSON> t<PERSON> b<PERSON><PERSON> s<PERSON> tarp 1 ir {{number}}", "refundAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ amount }}", "paymentLink": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>, kurio suma {{ amount }}, nuorodą", "selectPaymentToRefund": "Pasirinkite mokėjimą grąžinimui"}, "edits": {"title": "Keisti užsakymą", "confirm": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "confirmText": "Ketinate patvir<PERSON>ti užsaky<PERSON> keitimą. Šis ve<PERSON>sma<PERSON>.", "cancel": "<PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "currentItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentItemsDescription": "Pakeiskite prekės kiekį ar pa<PERSON>ite.", "addItemsDescription": "Galite pridėti naujų prekių prie užsakymo.", "addItems": "Pridėti prekių", "amountPaid": "Sumokėta suma", "newTotal": "Nauja suma", "differenceDue": "<PERSON><PERSON><PERSON><PERSON>", "create": "Keisti užsakymą", "currentTotal": "<PERSON><PERSON><PERSON><PERSON> suma", "noteHint": "Pridėkite vidinę užsakymo keitimo pastab<PERSON>", "cancelSuccessToast": "Užsakymo keitimas atšauktas", "createSuccessToast": "Užsakymo keitimas sėkmingai sukurtas", "activeChangeError": "Jau yra vykdomas užsakymo keitimas (g<PERSON><PERSON><PERSON><PERSON>mas, pretenzija, keitimas, kt.). Užbaikite arba atšaukite keitimą.", "panel": {"title": "Užsakymo keitimas išsiųstas", "titlePending": "Užsaky<PERSON> keit<PERSON> laukia"}, "toast": {"canceledSuccessfully": "Užsakymo keitimas atšauktas", "confirmedSuccessfully": "Užsakymo keit<PERSON>"}, "validation": {"quantityLowerThanFulfillment": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>, kad kiekis būtų mažesnis arba lygus įvykdytam kiekiui"}}, "edit": {"email": {"title": "Keisti el. paš<PERSON>ą", "requestSuccess": "Užsakymo el. paštas pakeitas į {{email}}."}, "shippingAddress": {"title": "Keisti pristatymo <PERSON>", "requestSuccess": "Užsakymo pristatymo adresas pakeistas."}, "billingAddress": {"title": "Keisti adresą sąskaitai", "requestSuccess": "Užsakymo adresas sąskaitai pakeistas."}}, "returns": {"create": "Sukurti grąžinimą", "confirm": "Patvirtinti grąžinimą", "confirmText": "Ketinate patvirtinti grą<PERSON><PERSON>ą. Šis veiksmas neatstatom<PERSON>.", "inbound": "Įeinantis", "outbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendNotification": "Siųsti pranešimą", "sendNotificationHint": "Praneškite klientui apie grąžinimą.", "returnTotal": "Grąžinama viso", "inboundTotal": "Įeinantis viso", "refundAmount": "Pirkėjui grąžinama suma", "outstandingAmount": "Likučio suma", "reason": "Priežastis", "reasonHint": "Pasirinkite kodėl klientas nori grąžinti prekes.", "note": "Pastabaa", "noInventoryLevel": "Nėra likučio", "noInventoryLevelDesc": "Pasirinktoje vietoje nėra pasirinktų atsargų likučio. Grąžinimas gali būti išsių<PERSON>s, bet negali būti priimtas kol pasirinktoje vietoje nebus sukurtas likutis.", "noteHint": "Laisvos formos pataba, jei norite ką nors nurodyti.", "location": "Sandėliavimo vieta", "locationHint": "Pasirinkite vietą, į kurią norite grąžinti atsargas.", "inboundShipping": "Grąžini<PERSON> si<PERSON>", "inboundShippingHint": "Pasirinkite nor<PERSON> siuntimo būd<PERSON>.", "returnableQuantityLabel": "Grąž<PERSON><PERSON> kiekis", "refundableAmountLabel": "Grąžinama suma", "returnRequestedInfo": "{{requestedItemsCount}}x prekių grąžinimas i<PERSON>s", "returnReceivedInfo": "{{requestedItemsCount}}x prekių grąžinimas gautas", "itemReceived": "<PERSON><PERSON><PERSON>", "returnRequested": "return i<PERSON><PERSON><PERSON><PERSON><PERSON>", "damagedItemReceived": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON>", "damagedItemsReturned": "{{quantity}}x pa<PERSON><PERSON><PERSON><PERSON>", "activeChangeError": "Jau yra vykdomas užsakymo keitimas (g<PERSON><PERSON><PERSON><PERSON>mas, pretenzija, keitimas, kt.). Užbaikite arba atšaukite keitimą.", "cancel": {"title": "Atšaukti grąžinimą", "description": "Tikrai norite atšaukti grąžinimą?"}, "placeholders": {"noReturnShippingOptions": {"title": "Nėra grąžinimo siuntimo variantų", "hint": "Nėra sukurtų siuntimo variantų šiai vietovei. Galite sukurti Galite sukurti jį <LinkComponent>Vietos ir siuntimas</LinkComponent>."}, "outboundShippingOptions": {"title": "Nėra išeinančio siuntimo variantų", "hint": "Nėra sukurtų išeinančio siuntimo variantų šiai vietovei. Galite sukurti jį <LinkComponent>Vietos ir siuntimas</LinkComponent>."}}, "receive": {"action": "<PERSON><PERSON><PERSON><PERSON>", "receiveItems": "{{ returnType }} {{ id }}", "restockAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus liku<PERSON>", "itemsLabel": "<PERSON><PERSON><PERSON>", "title": "<PERSON>ri<PERSON><PERSON> #{{returnId}} prekes", "sendNotificationHint": "Praneškite klientui apie gautą grąžinimą.", "inventoryWarning": "Atkreipkite dėmesį, kad automatiškai pakeisime likučius pagal tai, ką įvesite aukščiau.", "writeOffInputLabel": "Kiek prekių yra pažeistų?", "toast": {"success": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>kmingai gaut<PERSON>.", "errorLargeValue": "<PERSON><PERSON><PERSON> did<PERSON> nei p<PERSON>š<PERSON>.", "errorNegativeValue": "<PERSON><PERSON><PERSON> negali b<PERSON>ti ne<PERSON>.", "errorLargeDamagedValue": "Pažeistų prekių kiekis + nepažeistų gautas kiekis didesnis už bedrą grąžinąmą kiekį. Sumažinkite nepažeistų prekių kiekį."}}, "toast": {"canceledSuccessfully": "return sekmingai atšauktas", "confirmedSuccessfully": "return sek<PERSON><PERSON> pat<PERSON>"}, "panel": {"title": "return inicijuotas", "description": "Yra pradėtas gr<PERSON><PERSON><PERSON>, kuris neuž<PERSON>igtas"}}, "claims": {"create": "Sukurti pretenziją", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "confirmText": "<PERSON><PERSON><PERSON> pat<PERSON><PERSON> preten<PERSON>. <PERSON>is ve<PERSON>sma<PERSON>.", "manage": "Tvar<PERSON><PERSON> pretenziją", "outbound": "Tiekėjui", "outboundItemAdded": "{{itemsCount}}x pridėta prie pretenzijos", "outboundTotal": "Tiekėjui viso", "outboundShipping": "Siuntimas tiėkėjui", "outboundShippingHint": "Pasirinkite nor<PERSON> siuntimo būd<PERSON>.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON>", "activeChangeError": "Jau yra vykdomas užsakymo keitimas (g<PERSON><PERSON><PERSON><PERSON>mas, pretenzija, keitimas, kt.). Užbaikite arba atšaukite keitimą.", "actions": {"cancelClaim": {"successToast": "Pretenzija sėkmingai atšaukta."}}, "cancel": {"title": "<PERSON><PERSON><PERSON><PERSON> pretenzi<PERSON>", "description": "Tikrai norite atšaukti pretenziją?"}, "tooltips": {"onlyReturnShippingOptions": "Šiame sąraše bus tik grąžinimo siuntimo pasirinkimai."}, "toast": {"canceledSuccessfully": "Pretenzija sekmingai atšaukta", "confirmedSuccessfully": "Pretenzija sekmingai patvir<PERSON>"}, "panel": {"title": "Pretenzija inicijuota", "description": "<PERSON><PERSON> yra nagrin<PERSON><PERSON> preten<PERSON>, kuri turi b<PERSON><PERSON>"}}, "exchanges": {"create": "Su<PERSON><PERSON><PERSON> keitim<PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "confirmText": "<PERSON><PERSON><PERSON> pat<PERSON><PERSON><PERSON> keit<PERSON>. <PERSON><PERSON> ve<PERSON>.", "outbound": "Tiekėjui", "outboundItemAdded": "{{itemsCount}}x p<PERSON><PERSON><PERSON> keit<PERSON>", "outboundTotal": "Tiekėjui viso", "outboundShipping": "Siuntimas tie<PERSON>ėjui", "outboundShippingHint": "Pasirinkite nor<PERSON> siuntimo būd<PERSON>.", "refundAmount": "<PERSON><PERSON><PERSON><PERSON>", "activeChangeError": "Jau yra vykdomas užsakymo keitimas (g<PERSON><PERSON><PERSON><PERSON>mas, pretenzija, keitimas, kt.). Užbaikite arba atšaukite keitimą.", "actions": {"cancelExchange": {"successToast": "<PERSON><PERSON><PERSON> atšauktas."}}, "cancel": {"title": "<PERSON><PERSON><PERSON><PERSON> keit<PERSON>", "description": "Tikrai norite atšaukti keitimą?"}, "tooltips": {"onlyReturnShippingOptions": "Šiame sąraše bus tik grąžinimo siuntimo pasirinkimai."}, "toast": {"canceledSuccessfully": "<PERSON><PERSON><PERSON> atšauktas", "confirmedSuccessfully": "<PERSON><PERSON><PERSON> sek<PERSON> pat<PERSON>"}, "panel": {"title": "Keitimas inicijuota<PERSON>", "description": "<PERSON><PERSON> yra nag<PERSON><PERSON><PERSON><PERSON>, kuris turi b<PERSON><PERSON>"}}, "reservations": {"allocatedLabel": "Atidė<PERSON>", "notAllocatedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "allocateItems": {"action": "<PERSON><PERSON><PERSON><PERSON> prekes", "title": "Atidėti uždakymo prekes", "locationDescription": "Pasirinkite vietą, kurioje norite atidėti.", "itemsToAllocate": "Prekių atidėjimui", "itemsToAllocateDesc": "Pasirinkite kiek prekių norite atidėti", "search": "Prekių paieška", "consistsOf": "<PERSON><PERSON><PERSON> iš {{num}}x atsargų", "requires": "Reikalinga {{num}} kiekvienam variantui", "toast": {"created": "<PERSON><PERSON>ė sėkmingai atidėta"}, "error": {"quantityNotAllocated": "Yra neatidėtų prekių."}}, "shipment": {"title": "Žymėti vykdymą išsiųstu", "trackingNumber": "Sekimo numeris", "addTracking": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> numerį", "sendNotification": "Siųsti pranešimą", "sendNotificationHint": "Praneškite klientui apie šį siuntimą.", "toastCreated": "Siuntimas sėkmingai sukurta<PERSON>."}, "fulfillment": {"cancelWarning": "Ketinate atšaukti užsakymo vykdymą. Šis ve<PERSON>.", "markAsDeliveredWarning": "Ketinate pažymėti kaip pristat<PERSON>. Šis ve<PERSON>sma<PERSON>.", "differentOptionSelected": "Pasirinktas siuntimo būdas nesutampa su kliento pasirinktu.", "disabledItemTooltip": "Pasirinktas siuntimo būdas <PERSON>leidžia siųsti šios <PERSON>", "unfulfilledItems": "Neparu<PERSON><PERSON><PERSON>", "statusLabel": "<PERSON><PERSON><PERSON><PERSON>", "statusTitle": "<PERSON><PERSON><PERSON><PERSON>", "fulfillItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "awaitingFulfillmentBadge": "<PERSON><PERSON><PERSON>", "requiresShipping": "Reikalingas siunt<PERSON>", "number": "Vykdymas #{{number}}", "itemsToFulfill": "<PERSON><PERSON><PERSON><PERSON>", "create": "Sukurti vykdymą", "available": "<PERSON><PERSON>", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "markAsShipped": "Žymėti ka<PERSON>", "markAsPickedUp": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>", "markAsDelivered": "Ž<PERSON>ėti ka<PERSON> p<PERSON>", "itemsToFulfillDesc": "Pasirinkite prekes ir kiekius par<PERSON>", "locationDescription": "Pasirinkite vietą iš kurios norite paruošti prekes.", "sendNotificationHint": "Praneškite klientui apie sukurtą prekių paruošimą.", "methodDescription": "Pasirinkite kitą siuntimo būdą nei kliento pasirinkas", "error": {"wrongQuantity": "<PERSON>ra tik viena pre<PERSON>", "wrongQuantity_other": "<PERSON><PERSON><PERSON> t<PERSON> b<PERSON><PERSON> s<PERSON> tarp 1 ir {{number}}", "noItems": "Nėra prekių paruošimui.", "noShippingOption": "<PERSON><PERSON><PERSON> b<PERSON>das yra privalomas", "noLocation": "Vieta yra privaloma"}, "status": {"notFulfilled": "Neįvykdytas", "partiallyFulfilled": "Dalinai įvykdytas", "fulfilled": "Įvykdytas", "partiallyShipped": "Dalinai i<PERSON>", "shipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delivered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partiallyDelivered": "<PERSON><PERSON><PERSON>", "partiallyReturned": "Dalinai grąžintas", "returned": "Grąžinintas", "canceled": "<PERSON><PERSON>uk<PERSON>", "requiresAction": "Reikia veiksmų"}, "toast": {"created": "Sėkmingai paruoštas", "canceled": "Paruoš<PERSON><PERSON>šauktas", "fulfillmentShipped": "Negalima atšaukti jau išsiųsto užsakymo", "fulfillmentDelivered": "Sėkmingai pažymėtas kaip pristatytas", "fulfillmentPickedUp": "Sėkmingai pažymėtas kaip paimtas"}, "trackingLabel": "Se<PERSON><PERSON>", "shippingFromLabel": "<PERSON>un<PERSON><PERSON><PERSON> iš", "itemsLabel": "Prek<PERSON><PERSON>"}, "refund": {"title": "Sukurti pinigų grąžinimą", "sendNotificationHint": "Praneškite klientui apie sukurtą pinigų grąžinimą.", "systemPayment": "<PERSON><PERSON><PERSON><PERSON>", "systemPaymentDesc": "Vienas ar daugiau jūsų mokėjimų yra sisteminiai. Atminkite, kad Medusa netvarko tokių mokėjimų užfiksavimo ir grąžinimo.", "error": {"amountToLarge": "Negalima grąžinti daugiau nei užsakymo suma.", "amountNegative": "Grąžinimo suma turi būti te<PERSON> s<PERSON>.", "reasonRequired": "Pasirinkite grąžinimo priežastį."}}, "customer": {"contactLabel": "Kontaktai", "editEmail": "Keisti el. paš<PERSON>ą", "transferOwnership": "<PERSON><PERSON><PERSON>i nuosavybės te<PERSON>", "editBillingAddress": "Keisti adresą sąskaitai", "editShippingAddress": "Keisti pristatymo <PERSON>"}, "activity": {"header": "Veikla", "showMoreActivities_one": "<PERSON><PERSON><PERSON> dar {{count}} veik<PERSON><PERSON>", "showMoreActivities_other": "<PERSON><PERSON><PERSON> dar {{count}} veiklų", "comment": {"label": "Komentaras", "placeholder": "Palikite komentarą", "addButtonText": "Pridėti komentarą", "deleteButtonText": "Pašalinti komentarą"}, "from": "<PERSON><PERSON>", "to": "Į", "events": {"common": {"toReturn": "Grąžinti", "toSend": "Si<PERSON>sti"}, "placed": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromSalesChannel": "iš {{salesChannel}}"}, "canceled": {"title": "<PERSON>žsa<PERSON><PERSON> atšauktas"}, "payment": {"awaiting": "<PERSON><PERSON><PERSON>", "captured": "Mokėjimas užfiksuotas", "canceled": "Mokėjimas atšauktas", "refunded": "Mokėjimas grąžintas"}, "fulfillment": {"created": "<PERSON><PERSON><PERSON><PERSON>", "canceled": "Paruoš<PERSON><PERSON>šauktas", "shipped": "<PERSON><PERSON><PERSON><PERSON>", "delivered": "Prek<PERSON><PERSON>", "items_one": "{{count}} prekė", "items_other": "{{count}} prek<PERSON>s"}, "return": {"created": "Grąžinimas #{{returnId}} sukurtas", "canceled": "Grąžinimas #{{returnId}} atšauktas", "received": "Grąžinimas #{{returnId}} gautas", "items_one": "{{count}} prek<PERSON> g<PERSON>", "items_other": "{{count}} preki<PERSON> gr<PERSON>ž<PERSON>a"}, "note": {"comment": "Komentaras", "byLine": "sukurtas {{author}}"}, "claim": {"created": "Pretenzija #{{claimId}} i<PERSON><PERSON>ųsta", "canceled": "Pretenzija #{{claimId}} atšaukta", "itemsInbound": "{{count}} prek<PERSON> g<PERSON>", "itemsOutbound": "{{count}} pre<PERSON><PERSON>"}, "exchange": {"created": "Keitimas #{{exchangeId}} i<PERSON><PERSON><PERSON><PERSON><PERSON>", "canceled": "Keitimas #{{exchangeId}} atšauktas", "itemsInbound": "{{count}} prek<PERSON> g<PERSON>", "itemsOutbound": "{{count}} pre<PERSON><PERSON>"}, "edit": {"requested": "<PERSON><PERSON><PERSON><PERSON><PERSON> keit<PERSON> #{{editId}} i<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON> keit<PERSON> #{{editId}} pat<PERSON><PERSON><PERSON>"}, "transfer": {"requested": "Užsaky<PERSON> n<PERSON> te<PERSON>ė<PERSON> perleidima<PERSON> #{{transferId}} iš<PERSON>ų<PERSON><PERSON>", "confirmed": "Užsaky<PERSON> n<PERSON> te<PERSON>ė<PERSON> perleidima<PERSON> #{{transferId}} pat<PERSON><PERSON><PERSON>", "declined": "Užsaky<PERSON> n<PERSON> te<PERSON> perleid<PERSON> #{{transferId}} atmestas"}, "update_order": {"shipping_address": "Pristatymo adresas pakeistas", "billing_address": "Adresas sąskaitai pakeistas", "email": "El. paštas pakeistas"}}}, "fields": {"displayId": "Rodyti ID", "refundableAmount": "Grąžinama suma", "returnableQuantity": "Grąž<PERSON><PERSON> kiekis"}}, "draftOrders": {"domain": "Užsakymų juodraščiai", "deleteWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> užsaky<PERSON> juodraštį {{id}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "paymentLinkLabel": "Apmokėjimo nuoroda", "cartIdLabel": "Krepšelio ID", "markAsPaid": {"label": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "warningTitle": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "warningDescription": "Ketinate pažymėti užsakymo juodraštį kaip apmok<PERSON>tą. Šis veiksmas neatstatomas ir apmokėjimas nebus įmanomas vėliau."}, "status": {"open": "<PERSON><PERSON><PERSON>", "completed": "Atliktas"}, "create": {"createDraftOrder": "Su<PERSON>rti užsakymo juodraštį", "createDraftOrderHint": "Sukurkite naują užsakymo juodraštį užsakymo tvarkymui prieš jo patei<PERSON>.", "chooseRegionHint": "Pasirinkite regioną", "existingItemsLabel": "<PERSON><PERSON><PERSON>", "existingItemsHint": "Pridėkite esamas prekes prie šio užsakymo juo<PERSON>.", "customItemsLabel": "Inividualios <PERSON>", "customItemsHint": "Pridėkite individualias prekes prie šio užsakymo juodra<PERSON>.", "addExistingItemsAction": "<PERSON><PERSON><PERSON><PERSON> pre<PERSON>", "addCustomItemAction": "Pridėti individualias prekes", "noCustomItemsAddedLabel": "Nėra pridėtų individualių prekių", "noExistingItemsAddedLabel": "Nėra pridėtų individualių prekių", "chooseRegionTooltip": "Pasirinkite regioną", "useExistingCustomerLabel": "<PERSON><PERSON><PERSON> k<PERSON>", "addShippingMethodsAction": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> b<PERSON>", "unitPriceOverrideLabel": "Vieneto ka<PERSON>", "shippingOptionLabel": "<PERSON><PERSON><PERSON>", "shippingOptionHint": "Pasirinkite siuntimo būdą šiam užsaky<PERSON> juo<PERSON>.", "shippingPriceOverrideLabel": "<PERSON><PERSON><PERSON>", "shippingPriceOverrideHint": "Perrašykite kitą siuntimo kainą šiam užsakymo juodra<PERSON>i.", "sendNotificationLabel": "Siųsti pranešimą", "sendNotificationHint": "Praneškite klientui apie užsakymo juodrašč<PERSON> su<PERSON>."}, "validation": {"requiredEmailOrCustomer": "El. paštas ar klientas yra privalomas.", "requiredItems": "Prival<PERSON> bent viena prekė.", "invalidEmail": "El. paš<PERSON> turi būti te<PERSON>."}}, "stockLocations": {"domain": "Sandėliavimas ir siuntimas", "list": {"description": "Tvarkykite savo sandėliavimo vietas ir siuntimo bū<PERSON>."}, "create": {"header": "Sukurti sandėliavimo vietą", "hint": "Sandėliavimo vieta yra fizin<PERSON> ad<PERSON>, k<PERSON><PERSON> ir iš kurio si<PERSON> pre<PERSON> k<PERSON>.", "successToast": "Sandėliavimo vieta {{name}} sėkmingai sukurta."}, "edit": {"header": "Ke<PERSON>i sandėliavimo vietą", "viewInventory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "successToast": "Sandėliavimo vieta {{name}} sėkmingai pakeista."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON>i sandėliavimo vietą {{name}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>."}, "fulfillmentProviders": {"header": "Vykdytojai", "shippingOptionsTooltip": "Čia bus tik vykdytojai įjungti šiai sandėliavimo vietai. Pridėkite juos jei šis pasirinkimas uždraustas.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectedTo": "Prijungta {{count}} iš {{total}} v<PERSON><PERSON><PERSON>j<PERSON>", "noProviders": "Ši sandėliavimo vieta neprijungta nei prie vieno vykdytojo.", "action": "Prijun<PERSON><PERSON>", "successToast": "Vykdytojai sėkmingai p<PERSON>isti."}, "fulfillmentSets": {"pickup": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shipping": {"header": "Siuntimas"}, "disable": {"confirmation": "Are you sure that you want to disable {{name}}? This will delete all associated service zones and shipping options, and cannot be undone.", "pickup": "Atsiėmimas sėkmingai išjungtas.", "shipping": "Siuntimas sėkmingai išjungtas."}, "enable": {"pickup": "Atsiėmi<PERSON> įjungtas.", "shipping": "Siuntimas s<PERSON>kming<PERSON> įjungtas."}}, "sidebar": {"header": "<PERSON><PERSON><PERSON> n<PERSON>", "shippingProfiles": {"label": "<PERSON><PERSON><PERSON>", "description": "Grupuokite prekes pagal siuntimo re<PERSON>"}}, "salesChannels": {"header": "Pardavimų kanalai", "hint": "Tvarkykite pardavimo kanalus prijungtus prie šios sandėliavimo vietos.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> pardavimo kanalai", "connectedTo": "Prijungta {{count}} iš {{total}} pardavimo kanalų", "noChannels": "Ši sandėliavimo vieta neprijungta nei prie vieno pardavimo kanalų.", "action": "<PERSON><PERSON><PERSON><PERSON><PERSON> pardavimo kanalus", "successToast": "Pardavimo kanalai sėkmingai pakeisti."}, "pickupOptions": {"edit": {"header": "<PERSON><PERSON><PERSON> b<PERSON>"}}, "shippingOptions": {"create": {"shipping": {"header": "<PERSON><PERSON><PERSON>i siuntimo būdą zonai {{zone}}", "hint": "Sukurkite siuntimo būdą kad nustatyti kaip prek<PERSON>s siun<PERSON> iš <PERSON> sandėliavimo vietos.", "label": "<PERSON><PERSON><PERSON> b<PERSON>", "successToast": "<PERSON><PERSON><PERSON> b<PERSON> {{name}} sėkmingai sukurtas."}, "pickup": {"header": "Su<PERSON><PERSON><PERSON> atsiėmimo būdą zonai {{zone}}", "hint": "Sukurkite atsiėmimo būdą kad nustatyti kaip prekės atsiimamos iš š<PERSON> sandėliavimo vietos.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> bū<PERSON> {{name}} sėkmingai sukurtas."}, "returns": {"header": "Sukurti grąžinimo būdą zonai {{zone}}", "hint": "Sukurkite grąžinimo būdą kad nustatyti kaip prekės grąžinimos į šią sandėliavimo vietą.", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bū<PERSON> {{name}} sėkmingai sukurtas."}, "tabs": {"details": "Nustatymai", "prices": "<PERSON><PERSON>"}, "action": "<PERSON><PERSON><PERSON><PERSON> būd<PERSON>"}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> siuntimo būd<PERSON> {{name}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "<PERSON><PERSON><PERSON> b<PERSON> {{name}} sėk<PERSON><PERSON> p<PERSON>."}, "edit": {"header": "<PERSON><PERSON><PERSON> si<PERSON> b<PERSON>", "action": "<PERSON><PERSON><PERSON>", "successToast": "<PERSON><PERSON><PERSON> b<PERSON> {{name}} sėkmingai pakeistas."}, "pricing": {"action": "<PERSON><PERSON><PERSON>"}, "conditionalPrices": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{name}} ka<PERSON>s", "description": "Nustatykite sąlygines kainas šiam siuntimo būdui priklausomai nuo krepšelio sumos.", "attributes": {"cartItemTotal": "Krepšelio suma"}, "summaries": {"range": "Jei <0>{{attribute}}</0> yra tarp <1>{{gte}}</1> ir <2>{{lte}}</2>", "greaterThan": "Jei <0>{{attribute}}</0> ≥ <1>{{gte}}</1>", "lessThan": "Jei <0>{{attribute}}</0> ≤ <1>{{lte}}</1>"}, "actions": {"addPrice": "<PERSON><PERSON><PERSON><PERSON>", "manageConditionalPrices": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>lygin<PERSON> kainas"}, "rules": {"amount": "<PERSON><PERSON><PERSON> kaina", "gte": "Mažiausia krepšelio suma", "lte": "Didžiausia krepšelio suma"}, "customRules": {"label": "Inivid<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> kaina turi <PERSON>, kurios negali būti pakeist<PERSON> darbastalyje.", "eq": "Krepšelio suma turi būti lygi", "gt": "Krepšelio suma turi būti did<PERSON> už", "lt": "Krepšelio suma turi būti mažesnė už"}, "errors": {"amountRequired": "<PERSON><PERSON><PERSON> kaina privaloma", "minOrMaxRequired": "<PERSON><PERSON> b<PERSON><PERSON> pat<PERSON> bent viena iš mažiausios ar didžiausios krepšelio sumų", "minGreaterThanMax": "Mažiausia krepšelio suma turi būti mažesnė arba lygi didžiausiai krepšelio sumai", "duplicateAmount": "<PERSON>untimo kaina turi būti unikali kiekvienai sąlygai", "overlappingConditions": "Sąlygos turi būti unikaios tarp visų kainų taisyklių"}}, "fields": {"count": {"shipping_one": "{{count}} si<PERSON><PERSON> b<PERSON>", "shipping_other": "{{count}} si<PERSON><PERSON> būdai", "pickup_one": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "pickup_other": "{{count}} at<PERSON><PERSON><PERSON><PERSON> b<PERSON>dai", "returns_one": "{{count}} g<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "returns_other": "{{count}} g<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>dai"}, "priceType": {"label": "Kainos tipas", "options": {"fixed": {"label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON>untimo kaina yra fiksuota ir nekinta p<PERSON>lausomai nuo užsaky<PERSON> turinio."}, "calculated": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "<PERSON>untimo kaina yra apskaič<PERSON>ojama pirkimo metu pagal v<PERSON>dy<PERSON>ją."}}}, "enableInStore": {"label": "Įjungta parduotuvėje", "hint": "Ar k<PERSON>ai gali pasirinkti šį būdą pirkimo metu."}, "provider": "Vykdytojas", "profile": "Pristatymo eiga", "fulfillmentOption": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "serviceZones": {"create": {"headerPickup": "Su<PERSON><PERSON><PERSON> atsiėmimo iš {{location}} aptarnavimo zoną", "headerShipping": "<PERSON><PERSON><PERSON><PERSON> siuntimo iš {{location}} aptarnavimo zoną", "action": "Sukurti aptarnavimo zoną", "successToast": "Aptarnavimo zona {{name}} sėkmingai sukurta."}, "edit": {"header": "Keisti aptarnavimo zoną", "successToast": "Aptarnavimo zona {{name}} sėkmingai pakeista."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON>šalinti aptarnavimo zoną {{name}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "Aptarnavimo zona {{name}} sėkmingai p<PERSON>."}, "manageAreas": {"header": "<PERSON><PERSON><PERSON>ti geografines sritis {{name}}", "action": "T<PERSON><PERSON>ti geografines sritis", "label": "Geografines sritys", "hint": "Pasirinkite geografines sritis, kurias apima aptarnavimo zona.", "successToast": "{{name}} geografines sritys sėkmingai pakeistos."}, "fields": {"noRecords": "Nėra aptarnavimo zonų siuntimo būdų pridėjimui.", "tip": "Aptarnavimo zoną sudaro geografinės sritys. Ji naudojama apriboti siuntimo būdus pagal ad<PERSON> rink<PERSON>."}}}, "shippingProfile": {"domain": "<PERSON><PERSON><PERSON>", "subtitle": "Sugrupuokite prekes su panašiais siuntimo reikalavimais į siuntimo tipus.", "create": {"header": "<PERSON><PERSON><PERSON>i siuntimo tip<PERSON>", "hint": "Sukurkite siuntimo <PERSON>, kad sugrupuoti prekes su panašiais siuntimo re<PERSON>.", "successToast": "<PERSON><PERSON><PERSON> tip<PERSON> {{name}} sėkmingai sukurtas."}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON> siuntimo <PERSON>", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> siuntimo <PERSON> {{name}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "<PERSON><PERSON><PERSON> {{name}} s<PERSON>k<PERSON><PERSON> p<PERSON>."}, "tooltip": {"type": "Įveskite <PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, t.t."}}, "taxRegions": {"domain": "Mokesčių regionai", "list": {"hint": "Nustatykite kiek apmokęstinate klientus kai jie perka iš skirtingų šalių ir regionų."}, "delete": {"confirmation": "Ketinate pašalinti mokesčių regioną. Šis ve<PERSON>.", "successToast": "Mokesčių regionas sėkmingai pašalintas."}, "create": {"header": "Sukurti mokesčių regioną", "hint": "Sukurkite mokesčių regioną, kad nustatyti mokesčių tarifus konkrečiai šaliai.", "errors": {"rateIsRequired": "Tarifas yra privalomas kai kuriamas numatytasis mokesčių tarifas.", "nameIsRequired": "Pavadinimas yra privalomas kai kuriamas numatytasis mokesčių tarifas."}, "successToast": "Mokesčių regionas sėkmingai sukurtas."}, "province": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Sukurti provincijos mokesčių regioną", "hint": "Sukurkite provincijos mokesčių regioną jos mokesčių tarifų nustatymui."}}, "state": {"header": "<PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Sukurti valstijos mokesčių regioną", "hint": "Sukurkite valstijos mokesčių regioną jos mokesčių tarifų nustatymui."}}, "stateOrTerritory": {"header": "Valstijos ar teritorijos", "create": {"header": "Sukurti valstijos/teritorijos mokesčių regioną", "hint": "Sukurkite valstijos/teritorijos mokesčių regioną jos mokesčių tarifų nustatymui."}}, "county": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Sukurti šalies mokesčių regioną", "hint": "Sukurkite šalies mokesčių regioną jos mokesčių tarifų nustatymui."}}, "region": {"header": "Regionai", "create": {"header": "Sukurti regiono mokesčių regioną", "hint": "Sukurkite regiono mokesčių regioną jo mokesčių tarifų nustatymui."}}, "department": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Sukurti skyriaus m<PERSON> regioną", "hint": "Sukurkite skyriaus mokesčių regioną jo mokesčių tarifų nustatymui."}}, "territory": {"header": "Teritorijos", "create": {"header": "Sukurti teritorijos mokesčių regioną", "hint": "Sukurkite teritorijos mokesčių regioną jos mokesčių tarifų nustatymui."}}, "prefecture": {"header": "Prefektūros", "create": {"header": "Sukurti prefektūros mokesčių regioną", "hint": "Sukurkite prefektūros mokesčių regioną jos mokesčių tarifų nustatymui."}}, "district": {"header": "<PERSON><PERSON><PERSON>", "create": {"header": "Sukurti rajono mokesčių regioną", "hint": "Sukurkite rajono mokesčių regioną jo mokesčių tarifų nustatymui."}}, "governorate": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Sukurti gubernijos mokesčių regioną", "hint": "Sukurkite gubernijos mokesčių regioną jos mokesčių tarifų nustatymui."}}, "canton": {"header": "Kantonai", "create": {"header": "Sukurti kantono mokesčių regioną", "hint": "Sukurkite kantono mokesčių regioną jo mokesčių tarifų nustatymui."}}, "emirate": {"header": "Emyratai", "create": {"header": "Sukurti emyrato m<PERSON>ų regioną", "hint": "Sukurkite emyrato m<PERSON> regioną jos mokesčių tarifų nustatymui."}}, "sublevel": {"header": "Polygiai", "create": {"header": "Sukurti polygio mokesčių regioną", "hint": "Sukurkite polygio mokesčių regioną jos mokesčių tarifų nustatymui."}}, "taxOverrides": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": {"header": "Su<PERSON>rti <PERSON>", "hint": "Sukurkite mokesči<PERSON> tarifą, kuris nustatytomis s<PERSON>ly<PERSON> perra<PERSON> numaty<PERSON> moke<PERSON>."}, "edit": {"header": "<PERSON><PERSON><PERSON>", "hint": "Pakeiskite mokesčių tarifą, kuris nustatytomis s<PERSON>ly<PERSON> per<PERSON> numaty<PERSON> moke<PERSON>."}}, "taxRates": {"create": {"header": "Sukurti mokesčių tarifą", "hint": "Sukurkite regiono mokesčio tarifą.", "successToast": "Mokesčių tarifas sėkmingai sukurtas."}, "edit": {"header": "Keisti mokesčių tarifą", "hint": "Pakeiskite regiono mokesčio tarifą.", "successToast": "Mokesčių tarifas sėkmingai pakeistas."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> moke<PERSON>č<PERSON> tari<PERSON>. {{name}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "Mokesčių tarifas sėkmingai p<PERSON>šalintas."}}, "fields": {"isCombinable": {"label": "<PERSON><PERSON><PERSON>", "hint": "<PERSON><PERSON> <PERSON><PERSON> mokesčio tarifas gali būti derinamas su numatytuoju mokesčių regiono tarifu.", "true": "<PERSON><PERSON><PERSON>", "false": "<PERSON><PERSON><PERSON>"}, "defaultTaxRate": {"label": "<PERSON>uma<PERSON><PERSON><PERSON> m<PERSON><PERSON> ta<PERSON>", "tooltip": "Numatytasis mokesčio tarifas šiam regionui. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, standartinis PVM tarifas šaliai ar regionui.", "action": "Sukurti numatytąjį mokesčio tarifą"}, "taxRate": "Mokes<PERSON><PERSON> tari<PERSON>", "taxCode": "Mokesčio kodas", "targets": {"label": "Taikymai", "hint": "Pasirinkite kam šis mokestis bus taikomas.", "options": {"product": "Prek<PERSON><PERSON>", "productCollection": "Preki<PERSON> kolekcijos", "productTag": "Prekių žymės", "productType": "Prekių tipai", "customerGroup": "Klientų grupės"}, "operators": {"in": "į", "on": "ant", "and": "ir"}, "placeholders": {"product": "Prekių paieška", "productCollection": "Prekių kolekcijų paieška", "productTag": "Prekių žymių paieška", "productType": "Prekių tipų paieška", "customerGroup": "Klientų grupių paieška"}, "tags": {"product": "Prekė", "productCollection": "Prekių kolekcija", "productTag": "Prekių žymė", "productType": "Prekių tipas", "customerGroup": "Klientų grupė"}, "modal": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>"}, "values_one": "{{count}} re<PERSON><PERSON><PERSON><PERSON>", "values_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfTargets_one": "{{count}} ta<PERSON><PERSON><PERSON>", "numberOfTargets_other": "{{count}} ta<PERSON><PERSON><PERSON>", "additionalValues_one": "ir dar {{count}} re<PERSON><PERSON><PERSON><PERSON>", "additionalValues_other": "ir dar {{count}} reikšmi<PERSON>", "action": "<PERSON><PERSON><PERSON><PERSON>"}, "sublevels": {"labels": {"province": "Provincija", "state": "Valstija", "region": "Regionas", "stateOrTerritory": "Valstija/Teritorija", "department": "Skyrius", "county": "County", "territory": "Teritorija", "prefecture": "Prefektūra", "district": "<PERSON><PERSON><PERSON>", "governorate": "Gubernija", "emirate": "<PERSON><PERSON><PERSON>", "canton": "<PERSON><PERSON><PERSON>", "sublevel": "Polygio kodas"}, "placeholders": {"province": "Pasirinkite provinciją", "state": "Pasirinkite valstija", "region": "Pasirinkite regioną", "stateOrTerritory": "Pasirinkite valstiją/tertoriją", "department": "Pasirinkite skyrių", "county": "Pasirinkite šalį", "territory": "Pasirinkite tertoriją", "prefecture": "Pasirinkite prefektūrą", "district": "Pasirinkite rajoną", "governorate": "Pasirinkite guberniją", "emirate": "Pasirinkite emyratą", "canton": "Pasirinkite kantoną"}, "tooltips": {"sublevel": "Įveskite ISO 3166-2 kodą polygio mokesčių regionui.", "notPartOfCountry": "{{province}} ne<PERSON><PERSON><PERSON>o {{country}}. Patikrinkite dar kartą ar nurod<PERSON>te te<PERSON>."}, "alert": {"header": "Polygio regionai išjungti šiam mokesčių regionui", "description": "Polygio regionai išjungti šiam mokesčių regionui region pagal nutylėjimą. <PERSON><PERSON><PERSON> juo<PERSON> įungti, kad gal<PERSON> kurti polygius, to<PERSON><PERSON> ka<PERSON>, valstijos ar teritorijos.", "action": "Įjungti polygio regionus"}}, "noDefaultRate": {"label": "Nėra numatytojo tarifo", "tooltip": "Šis mokesčių regionas neturi numatytojo mokesčio tarifo. Jei yra standartinis tarifas, pvz., <PERSON><PERSON><PERSON> PVM, pridėkite jį prie šio regiono."}}}, "promotions": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"details": "<PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"template": "Tipas", "details": "Nustatymai", "campaign": "Kampanija"}, "fields": {"type": "Tipas", "value_type": "Reikš<PERSON><PERSON><PERSON>", "value": "Reikš<PERSON><PERSON>", "campaign": "Kampanija", "method": "<PERSON><PERSON><PERSON>", "allocation": "Paskirstymas", "addCondition": "<PERSON><PERSON><PERSON><PERSON>", "clearAll": "Išvalyti visus", "amount": {"tooltip": "Pasirinkite valiuto<PERSON> kod<PERSON>, kad įjungti sumos nustatymą"}, "conditions": {"rules": {"title": "Kas gali naudotis š<PERSON>o kodu?", "description": "<PERSON><PERSON><PERSON> k<PERSON> le<PERSON>a naudotis šiuo nuolaid<PERSON> kodu? Je<PERSON>, kodu gal<PERSON>s naudotis visi klientai."}, "target-rules": {"title": "Kokioms prekėms bus taikoma akcija?", "description": "Akcija bus taikoma prekėms, kurios atitinka šias sąlygas."}, "buy-rules": {"title": "Kas turi bū<PERSON>, kad gal<PERSON> akcija?", "description": "<PERSON><PERSON>, taiko<PERSON> a<PERSON> nurodytoms prek<PERSON>."}}}, "tooltips": {"campaignType": "Valiutos kodas turi būti pasirinktas prie akcijos, kad nustatyti išlaidų biudžetą."}, "errors": {"requiredField": "<PERSON><PERSON><PERSON> prival<PERSON>", "promotionTabError": "<PERSON><PERSON>š tęsdami ištaisykite klaidas skirt<PERSON>"}, "toasts": {"promotionCreateSuccess": "<PERSON><PERSON><PERSON><PERSON> koda<PERSON> ({{code}}) sėkmingai sukurtas."}, "create": {}, "edit": {"title": "<PERSON><PERSON><PERSON> a<PERSON> nustatymus", "rules": {"title": "<PERSON><PERSON><PERSON> naudo<PERSON>"}, "target-rules": {"title": "Keisti taikym<PERSON> pre<PERSON>"}, "buy-rules": {"title": "<PERSON><PERSON><PERSON> p<PERSON>"}}, "campaign": {"header": "Kampanija", "edit": {"header": "<PERSON><PERSON><PERSON> ka<PERSON>", "successToast": "<PERSON><PERSON><PERSON><PERSON> kampanija sėkmingai atnauinta."}, "actions": {"goToCampaign": "Eiti į kampaniją"}}, "campaign_currency": {"tooltip": "Tai akcijos valiuta. Pakeiskite ją nustatymų skirtuke."}, "form": {"required": "Privaloma", "and": "IR", "selectAttribute": "Pasirink<PERSON> p<PERSON>ž<PERSON>", "campaign": {"existing": {"title": "<PERSON><PERSON><PERSON>", "description": "Pridėkite akciją prie esamos kampanijos.", "placeholder": {"title": "Nėra sukurtų kampanijų", "desc": "<PERSON><PERSON><PERSON> sukurti j<PERSON>, kad sekti kelias a<PERSON> ir nustatyti biudž<PERSON>."}}, "new": {"title": "<PERSON><PERSON><PERSON> kampanija", "description": "Sukurti nauą kampaniją šiai ak<PERSON>jai."}, "none": {"title": "Be kampanijos", "description": "Tęsti be akcijos ir kampanijos susiejimo"}}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "draft": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Klientai kol kas negalės naudotis š<PERSON> kodu"}, "active": {"title": "Taikoma", "description": "Klientai gali naudotis š<PERSON>o kodu"}, "inactive": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Klientai daugiau negali naudotis š<PERSON>o kodu"}}, "method": {"label": "<PERSON><PERSON><PERSON>", "code": {"title": "<PERSON><PERSON><PERSON><PERSON> kodas", "description": "Klientai turi įvesti šį nuolaidos kodą pirkimo metu"}, "automatic": {"title": "Automatiškai", "description": "Klientai mato šią akciją pirkimo metu"}}, "max_quantity": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "description": "Did<PERSON><PERSON><PERSON><PERSON> kiek<PERSON>, kuriam taikoma ši akcija."}, "type": {"standard": {"title": "Standartinė", "description": "Standartinė akcija"}, "buyget": {"title": "Pirk gauk", "description": "Pirk X gauk Y akcija"}}, "allocation": {"each": {"title": "Kiekvienai", "description": "Taikoma kiekvienai prekei"}, "across": {"title": "Visoms", "description": "Taikoma visoms prekėms"}}, "code": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>, kurį klientai įves pirkdami."}, "value": {"title": "Vertė"}, "value_type": {"fixed": {"title": "Vertė", "description": "Nuskaičiuoja<PERSON> suma, pvz., 100"}, "percentage": {"title": "Vertė", "description": "Procentas, nuskaičiuoamas nuo sumos, pvz., 8%"}}}, "deleteWarning": "<PERSON><PERSON><PERSON> p<PERSON>lint<PERSON> n<PERSON>laid<PERSON> kod<PERSON> {{code}}. <PERSON><PERSON> ve<PERSON>.", "createPromotionTitle": "Sukurti a<PERSON>ą", "type": "<PERSON><PERSON><PERSON><PERSON>", "conditions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Pridė<PERSON><PERSON>, kad apriboti kurioms prekėms akcija taikoma."}}}, "campaigns": {"domain": "<PERSON><PERSON><PERSON><PERSON>", "details": "Kampanijos nustatymai", "status": {"active": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "delete": {"title": "<PERSON><PERSON><PERSON> tikri?", "description": "<PERSON><PERSON><PERSON> pa<PERSON><PERSON> kampaniją '{{name}}'. <PERSON><PERSON> ve<PERSON>.", "successToast": "Kampanija '{{name}}' s<PERSON><PERSON><PERSON><PERSON> sukurta."}, "edit": {"header": "<PERSON><PERSON><PERSON> ka<PERSON>", "description": "Keiskite kampanios nustatymus.", "successToast": "Kampanija '{{name}}' s<PERSON><PERSON><PERSON><PERSON> p<PERSON>."}, "configuration": {"header": "Konfigūracija", "edit": {"header": "Keisti kampanijos konfigūraciją", "description": "Keiskite kompanijos konfigūraciją", "successToast": "Kampanijos konfigūracija sėkmingai pakeista."}}, "create": {"title": "Sukurti kampaniją", "description": "Sukurkite reklaminę kampaniją.", "hint": "Sukurkite reklaminę kampaniją.", "header": "Sukurti kampaniją", "successToast": "Kampanija '{{name}}' s<PERSON><PERSON><PERSON><PERSON> sukurta."}, "fields": {"name": "Pavadinimas", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start_date": "Pradžios data", "end_date": "Pabaigos data", "total_spend": "Išleistas biudžetas", "total_used": "Panaudotas bi<PERSON>žetas", "budget_limit": "Biudžeto limitas", "campaign_id": {"hint": "Šiame sąraše rodomos tik reklamos kampanijos, kuri<PERSON> valiutos kodas yra toks pat kaip a<PERSON>."}}, "budget": {"create": {"hint": "Sukurkite kampanijos <PERSON>ą.", "header": "Kampanijos <PERSON>ž<PERSON>"}, "details": "Kampanijos <PERSON>ž<PERSON>", "fields": {"type": "Tipas", "currency": "valiuta", "limit": "Limitas", "used": "Panaudota"}, "type": {"spend": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Nustatykite sumą, už kurią gali būti suteikta akcijos nuolaidų."}, "usage": {"title": "<PERSON>aud<PERSON><PERSON><PERSON>", "description": "Nustatykite kiekį, kiek kartų gali būti pasinaudota šia a<PERSON>ja."}}, "edit": {"header": "Keisti kampanijos <PERSON>"}}, "promotions": {"remove": {"title": "Pašalinti akciją iš kampanijos", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} ak<PERSON><PERSON><PERSON>(-ų) iš <PERSON>(-ų). <PERSON><PERSON> veiksma<PERSON> neat<PERSON>."}, "alreadyAdded": "Ši akcija jau pridėta prie kampanijos.", "alreadyAddedDiffCampaign": "Ši akcija jau pridėta prie kitos kampanijos ({{name}}).", "currencyMismatch": "<PERSON>k<PERSON><PERSON> ir kampanijos valiuta nesutampa", "toast": {"success": "{{count}} ak<PERSON><PERSON><PERSON>(-ų) sėkmingai pridėta prie kampanijos"}, "add": {"list": {"noRecordsMessage": "Pirmiausia sukurkite akciją."}}, "list": {"noRecordsMessage": "Kampanijoje akcijų nėra."}}, "deleteCampaignWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> kampani<PERSON> {{name}}. <PERSON><PERSON> ve<PERSON>.", "totalSpend": "<0>{{amount}}</0> <1>{{currency}}</1>"}, "priceLists": {"domain": "<PERSON>nor<PERSON>", "subtitle": "Sukurkite pardavimo ar perrašymo kainas tam tikroms sąlygoms.", "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> kainoraštį {{title}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} s<PERSON><PERSON><PERSON><PERSON> p<PERSON>."}, "create": {"header": "Sukurti kainoraštį", "subheader": "Sukurkite kainoraštį jūsų prekių kainų valdymui.", "tabs": {"details": "Nustatymai", "products": "Prek<PERSON><PERSON>", "prices": "<PERSON><PERSON>"}, "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} sėkming<PERSON> sukurtas.", "products": {"list": {"noRecordsMessage": "Pirmiausia sukurkite prekę."}}}, "edit": {"header": "<PERSON><PERSON><PERSON> kainoraštį", "successToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{title}} sėkmingai pakeistas."}, "configuration": {"header": "Konfigūracija", "edit": {"header": "Keisti kainoraščio konfigūraciją", "description": "Keiskite kainoraščio konfigūraciją.", "successToast": "Kainor<PERSON><PERSON><PERSON><PERSON> konfigūraciją sėkmingai pakeista."}}, "products": {"header": "Prek<PERSON><PERSON>", "actions": {"addProducts": "<PERSON><PERSON><PERSON><PERSON> prekes", "editPrices": "Keisti prices"}, "delete": {"confirmation_one": "<PERSON><PERSON><PERSON> pa<PERSON><PERSON>i kainas {{count}} prekei kainoraštyje. Šis veiksmas neatstatomas.", "confirmation_other": "<PERSON><PERSON><PERSON> pa<PERSON><PERSON>i kainas {{count}} prekėms kainoraštyje. Šis veiksmas neatstatomas.", "successToast_one": "<PERSON><PERSON> {{count}} prekei <PERSON> p<PERSON>.", "successToast_other": "<PERSON><PERSON> {{count}} prekėms sėkmingai p<PERSON>šalintoss."}, "add": {"successToast": "Kainos sėkmingai pridėtos prie kainorščio."}, "edit": {"successToast": "<PERSON><PERSON> s<PERSON> p<PERSON>ist<PERSON>."}}, "fields": {"priceOverrides": {"label": "Kain<PERSON>", "header": "Kain<PERSON>"}, "status": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"active": "Aktyvus", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Pasibaigęs", "scheduled": "Suplanuotas"}}, "type": {"label": "Tipas", "hint": "Pasirinkite kokio tipo kainoraštį norite sukurti.", "options": {"sale": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pardavimo kainos yra laikini prekių kainų pakeitimai."}, "override": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> dažniausiai naudojamos norint sukurti specialias kainas k<PERSON>ams."}}}, "startsAt": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi pradžios datą?", "hint": "Suplanuokite kainoraščio įjungimą."}, "endsAt": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi pabaigos datą?", "hint": "Suplanuokite kainoraščio išjungimą."}, "customerAvailability": {"header": "Pasirinkite klientų grupes", "label": "<PERSON><PERSON><PERSON><PERSON>", "hint": "Pasirinkite kurioms klientų grupėms turėtų būti taikomas š<PERSON> ka<PERSON>.", "placeholder": "Klientų grupių paieška", "attribute": "Klientų grupės"}}}, "profile": {"domain": "Paskyra", "manageYourProfileDetails": "Tvarkykite savo paskyros nustatymus.", "fields": {"languageLabel": "<PERSON><PERSON><PERSON>", "usageInsightsLabel": "<PERSON><PERSON><PERSON><PERSON> įžvalgos"}, "edit": {"header": "<PERSON><PERSON><PERSON>", "languageHint": "<PERSON><PERSON><PERSON>, k<PERSON>ą norite naudoti administravimo aplinkoje. Tai nekeičia jūsų parduotuvės kalbos.", "languagePlaceholder": "Pasirinkite kalbą", "usageInsightsHint": "Pasidalinkite naudojimo įžvalgomis ir padėkite mums to<PERSON><PERSON><PERSON>. <PERSON>ug<PERSON><PERSON> apie tai, ką renkame ir kaip tai naudojame, galite perska<PERSON>ti mūs<PERSON> <0>dokumentacijoje</0>."}, "toast": {"edit": "Paskyros pakeitimai iš<PERSON>ugoti"}}, "users": {"domain": "Administratoriai", "editUser": "Keisti administratorių", "inviteUser": "Pakviesti administratorių", "inviteUserHint": "Pakvieskite naują parduotuvės administratorių.", "sendInvite": "Siųsti pakvietimą", "pendingInvites": "<PERSON><PERSON><PERSON><PERSON>", "deleteInviteWarning": "<PERSON><PERSON><PERSON> p<PERSON>ša<PERSON>i p<PERSON>timą, i<PERSON><PERSON><PERSON><PERSON><PERSON> {{email}}. <PERSON><PERSON> veiksma<PERSON> neatstatomas.", "resendInvite": "Pakartoti pakvietimą", "copyInviteLink": "Kopijuoti pakvietimo nuorodą", "expiredOnDate": "<PERSON><PERSON><PERSON> i<PERSON> {{date}}", "validFromUntil": "Galioja <0>{{from}}</0> - <1>{{until}}</1>", "acceptedOnDate": "<PERSON><PERSON><PERSON><PERSON> {{date}}", "inviteStatus": {"accepted": "<PERSON><PERSON><PERSON><PERSON>", "pending": "<PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "roles": {"admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "developer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "member": "<PERSON><PERSON><PERSON>"}, "list": {"empty": {"heading": "Administratorių nėra", "description": "<PERSON>, jie bus rod<PERSON>."}, "filtered": {"heading": "Rezultatų nėra", "description": "Nėra administratorių, atitinkačių filtro kriterijus."}}, "deleteUserWarning": "Ketinate pašalinti administratorių {{name}}. <PERSON><PERSON> ve<PERSON>.", "deleteUserSuccess": "<PERSON>ius {{name}} s<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "invite": "<PERSON><PERSON><PERSON><PERSON>"}, "store": {"domain": "Parduotuvė", "manageYourStoresDetails": "Tvarkykite savo parduotuvės nustatymus", "editStore": "<PERSON><PERSON><PERSON> parduotuv<PERSON>", "defaultCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON> valiuta", "defaultRegion": "Numatytasis regionas", "defaultSalesChannel": "Numa<PERSON><PERSON><PERSON> pardavimo kanalas", "defaultLocation": "Numaty<PERSON>ji sand<PERSON>liavimo vieta", "swapLinkTemplate": "Keitimo nuorodos š<PERSON>as", "paymentLinkTemplate": "Apmokėjimo nuorodos <PERSON>", "inviteLinkTemplate": "Pakvietimo nuorodos šab<PERSON>as", "currencies": "<PERSON><PERSON><PERSON><PERSON>", "addCurrencies": "<PERSON><PERSON><PERSON><PERSON> vali<PERSON>", "enableTaxInclusivePricing": "Įjungti mokesčių įskaičiavimą kainodaroje", "disableTaxInclusivePricing": "Išjungti mokesčių įskaičiavimą kainodaroje", "removeCurrencyWarning_one": "<PERSON><PERSON><PERSON> pašalinti {{count}} parduotuv<PERSON>s valiut<PERSON>. Įsitikinkite, kad pašalinote visas kainas šia valiuta preš tęsiant.", "removeCurrencyWarning_other": "Ke<PERSON><PERSON> pašalinti {{count}} parduotuv<PERSON>s valiutų. Įsitikinkite, kad pašalinote visas kainas šiomis valiutomis preš tę<PERSON>t.", "currencyAlreadyAdded": "Valiuta parduotuv<PERSON><PERSON> j<PERSON> p<PERSON>.", "edit": {"header": "<PERSON><PERSON><PERSON> parduotuv<PERSON>"}, "toast": {"update": "Parduotuvė sėkming<PERSON> pakeista", "currenciesUpdated": "Valiutos <PERSON> p<PERSON>", "currenciesRemoved": "Valiu<PERSON> p<PERSON> i<PERSON> parduotu<PERSON>s", "updatedTaxInclusivitySuccessfully": "Mokesčių įskaičiavimas kainodaroje sėkmingai pakeistas"}}, "regions": {"domain": "Regionai", "subtitle": "Regionas yra sritis, kurioje parduodate produktus. Jis gali apimti kelias <PERSON> ir turi skirtingus mokesčių tarifus, vykdytojus ir valiutą", "createRegion": "Sukurti regioną", "createRegionHint": "Tvarkykite mokesčių tarifus ir vykdytojus šalių rinkiniams.", "addCountries": "<PERSON><PERSON><PERSON><PERSON>", "editRegion": "Keisti regioną", "countriesHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "deleteRegionWarning": "Ketinate p<PERSON>šalinti regioną {{name}}. <PERSON><PERSON> ve<PERSON>.", "removeCountriesWarning_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} šalį iš regiono. <PERSON><PERSON> ve<PERSON> neat<PERSON>tom<PERSON>.", "removeCountriesWarning_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> {{count}} š<PERSON> iš regiono. <PERSON><PERSON> ve<PERSON>.", "removeCountryWarning": "Ke<PERSON><PERSON> p<PERSON>linti šalį {{name}} iš regiono. Š<PERSON> ve<PERSON> neatsta<PERSON>.", "automaticTaxesHint": "Kai įjungta, mokesčiai bus paskaičiuoti pagal pristatymo adresą pirkimo metu.", "taxInclusiveHint": "Kai įjungta, kainos regione bus rodomos su mokesčiais.", "providersHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie mokėjimo paslaugų teikėjai galimi šiame regione.", "shippingOptions": "<PERSON><PERSON><PERSON> b<PERSON>", "deleteShippingOptionWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> siuntimo būd<PERSON> {{name}}. <PERSON><PERSON> ve<PERSON>.", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outbound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "priceType": "Kainos tipas", "flatRate": "<PERSON><PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Sukurkite regioną vietovėms, kuriose prekiaujate."}, "toast": {"delete": "Regionas sėkmingai p<PERSON>šalintas", "edit": "Regiono pakeitimai išsaugoti", "create": "Regionas sėkmingai sukurtas", "countries": "Regiono šalys s<PERSON> p<PERSON>"}, "shippingOption": {"createShippingOption": "<PERSON><PERSON><PERSON><PERSON> siuntimo būd<PERSON>", "createShippingOptionHint": "Sukurkite siuntimo būdą regionui.", "editShippingOption": "<PERSON><PERSON><PERSON> si<PERSON> b<PERSON>", "fulfillmentMethod": "<PERSON><PERSON><PERSON> tipas", "type": {"outbound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outboundHint": "Naudokite šį pasirinkimą, jei kuriate siuntimo būdą prekių pristatymui k<PERSON>ui.", "return": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnHint": "Naudokite šį pasirinkimą, jei kuriate siuntimo būdą prekių grąžinimui iš k<PERSON>o."}, "priceType": {"label": "Kainos tipas", "flatRate": "<PERSON><PERSON><PERSON><PERSON>", "calculated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "availability": {"adminOnly": "Tik administravimo aplinkoje", "adminOnlyHint": "<PERSON>, si<PERSON><PERSON> b<PERSON> bus prieinamas tik administravimo a<PERSON>, o ne parduotuvėje."}, "taxInclusiveHint": "<PERSON>, siuntimo kaina bus su įskaičiuotais moke<PERSON>.", "requirements": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hint": "Nurodykite šio siuntimo būdo <PERSON>."}}}, "taxes": {"domain": "Mokesčių regionai", "domainDescription": "Tvarkykite savo mokesčių regionus", "countries": {"taxCountriesHint": "Mokesčių nustatymai galioja šalims sąraše."}, "settings": {"editTaxSettings": "Keisti mokesčių nustatymus", "taxProviderLabel": "Mokes<PERSON><PERSON><PERSON> skaič<PERSON>uvas", "systemTaxProviderLabel": "Sistemos mokesčių skaičiuotuvas", "calculateTaxesAutomaticallyLabel": "Apskaičiuoti mokesčius automatiškai", "calculateTaxesAutomaticallyHint": "<PERSON> įjungta, mokesčiai bus apskaičiuoti ir pritaikyti pirkinių krepšeliams automatiškai. <PERSON> išjun<PERSON>, mokesčiai turi būti skaičiuojami rankiniu būdu atsiskaitant. Neautomatinius mokesčius rekomenduojama naudoti su trečiųjų šalių mokesčių skaičiuotuvais.", "applyTaxesOnGiftCardsLabel": "Taikyti m<PERSON><PERSON> do<PERSON> kortelėms", "applyTaxesOnGiftCardsHint": "Kai įjungta, mokesčiai bus pritaikyti dovanų kortelėms pirkimo metu. Kai kuriose šalyse mokesčių teisės aktai reikalauja apmokestinti dovanų korteles perkant.", "defaultTaxRateLabel": "<PERSON>uma<PERSON><PERSON><PERSON> m<PERSON><PERSON> ta<PERSON>", "defaultTaxCodeLabel": "Numa<PERSON><PERSON><PERSON> m<PERSON><PERSON> k<PERSON>"}, "defaultRate": {"sectionTitle": "Numatytasis mokesčių tarifas"}, "taxRate": {"sectionTitle": "Mokesčių tarifai", "createTaxRate": "Sukurti mokesčių tarifą", "createTaxRateHint": "Sukurkite mokesčių tarifą regionui.", "deleteRateDescription": "<PERSON><PERSON><PERSON> pašalinti mokesčių tarifą {{name}}. <PERSON><PERSON> ve<PERSON>.", "editTaxRate": "Keisti mokesčių tarifą", "editRateAction": "<PERSON><PERSON><PERSON> tari<PERSON>", "editOverridesAction": "<PERSON><PERSON><PERSON>", "editOverridesTitle": "Keisti mokesčių tarifo <PERSON>", "editOverridesHint": "Nurodykite mokesčių tarifo <PERSON>.", "deleteTaxRateWarning": "<PERSON><PERSON><PERSON> pašalinti mokesčių tarifą {{name}}. <PERSON><PERSON> ve<PERSON>.", "productOverridesLabel": "Prekių perrašymai", "productOverridesHint": "Nurodykite prekių perrašymus šiam mokesčių tarifui.", "addProductOverridesAction": "Pridėti prekių perrašymus", "productTypeOverridesLabel": "Prekių tipo perrašymai", "productTypeOverridesHint": "Nurodykite prekių tipo perrašymus šiam mokesčių tarifui.", "addProductTypeOverridesAction": "Pridėti prekių tipo perrašymus", "shippingOptionOverridesLabel": "<PERSON><PERSON><PERSON> b<PERSON>", "shippingOptionOverridesHint": "Nurodykite siuntimo bū<PERSON> š<PERSON> mokesčių tarifui.", "addShippingOptionOverridesAction": "<PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> b<PERSON><PERSON>", "productOverridesHeader": "Prek<PERSON><PERSON>", "productTypeOverridesHeader": "Prekių tipai", "shippingOptionOverridesHeader": "<PERSON><PERSON><PERSON> b<PERSON>"}}, "locations": {"domain": "Sandėiavimo vietos", "editLocation": "Ke<PERSON>i sandėliavimo vietą", "addSalesChannels": "Pridė<PERSON> pardavimo kanal<PERSON>", "noLocationsFound": "Sandėliavimo vietų nėra", "selectLocations": "Pasirinkite prekės sandėliavimo vietas.", "deleteLocationWarning": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON>i sandėliavimo vietą {{name}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "removeSalesChannelsWarning_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> {{count}} pardavimo kanalą iš sand<PERSON>liavimo viet<PERSON>.", "removeSalesChannelsWarning_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> {{count}} pardavi<PERSON> kanalus iš <PERSON> viet<PERSON>.", "toast": {"create": "Sandėliavimo vieta sėkmingai sukurta", "update": "Sandėliavimo vieta sėkmingai pakeista", "removeChannel": "Pardavimų kanalas sėkmingai p<PERSON>šalintas"}}, "reservations": {"domain": "Rezervacijos", "subtitle": "Tvarkykite rezervuotų atsargų kiekius.", "deleteWarning": "Ketinate pašalinti rezervaciją. Šis ve<PERSON>."}, "salesChannels": {"domain": "<PERSON><PERSON><PERSON><PERSON> kanalai", "subtitle": "Tvarkykite interneto ir fizinius pardavi<PERSON> kanal<PERSON>, kuri<PERSON><PERSON> preki<PERSON>.", "list": {"empty": {"heading": "Pardavimo kanalų nėra", "description": "Kai sukursite parda<PERSON><PERSON> kanal<PERSON>, jis bus rod<PERSON><PERSON>."}, "filtered": {"heading": "Rezultatų nėra", "description": "Nėra pardavimo kanalų, atitinkančių filtro kriterijus."}}, "createSalesChannel": "Su<PERSON>rti pardavimo kanalą", "createSalesChannelHint": "Sukurkite pardavi<PERSON> kanal<PERSON>, kuria<PERSON> pre<PERSON>.", "enabledHint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ar pardavimo kanalas įjungtas.", "removeProductsWarning_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> {{count}} prek<PERSON> iš {{sales_channel}}.", "removeProductsWarning_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> {{count}} prekes iš {{sales_channel}}.", "addProducts": "<PERSON><PERSON><PERSON><PERSON> prekes", "editSalesChannel": "<PERSON><PERSON>i pardavimo kanal<PERSON>", "productAlreadyAdded": "Prekė jau pridėta prekių kanale.", "deleteSalesChannelWarning": "<PERSON><PERSON>te pašalinti prekių kanalą {{name}}. <PERSON><PERSON> ve<PERSON>.", "toast": {"create": "Pardavimo kanalas sėkmingai sukurtas", "update": "Pardavimo kanalas sėkmingai pakeistas", "delete": "Pardavimo kanalas sėkmingai p<PERSON>ša<PERSON>"}, "tooltip": {"cannotDeleteDefault": "Negalima pašalinti numatytojo pardavimo kanalo"}, "products": {"list": {"noRecordsMessage": "Prekių pardavimo kanale nėra."}, "add": {"list": {"noRecordsMessage": "Pirmiausia sukurkite prekę."}}}}, "apiKeyManagement": {"domain": {"publishable": "Vieši API raktai", "secret": "Slapti API raktai"}, "subtitle": {"publishable": "Tvarkykite API raktus, naudo<PERSON>mus pardu<PERSON>v<PERSON>se siekiant apriboti užklausų apimtį iki konkrečių pardavimo kanalų.", "secret": "Tvarkykite API raktus, naudojamus au<PERSON>i administratorius administravimo programose."}, "status": {"active": "Aktyvus", "revoked": "<PERSON><PERSON>uk<PERSON>"}, "type": {"publishable": "<PERSON><PERSON><PERSON><PERSON>", "secret": "<PERSON><PERSON><PERSON>"}, "create": {"createPublishableHeader": "Sukurti viešą API raktą", "createPublishableHint": "Sukurkite viešą API raktą, kad apriboti užklausų apimtį iki konkrečių pardavimo kanalų.", "createSecretHeader": "Sukurti slaptą API raktą", "createSecretHint": "Sukurkite slaptą API raktą, kad pasiekti Medusa API kaip autentifikuotas administratorius.", "secretKeyCreatedHeader": "Slaptas raktas sukurtas", "secretKeyCreatedHint": "Jūsų slaptas raktas sugeneruotas. Nusikopijuokite jį dabar ir saugiai išsisaugokite. Tai vienintelis kartas, kai jis bus rodomas.", "copySecretTokenSuccess": "Slaptas raktas nukopijuotas į atmintį.", "copySecretTokenFailure": "Nepavyko nukopi<PERSON>oti slapto rakto į atmintį.", "successToast": "API raktas sėkmingai sukurtas."}, "edit": {"header": "Keisti API raktą", "description": "Keiskite API rakto pavadinimą.", "successToast": "API raktas {{title}} sėkmingai pakeistas."}, "salesChannels": {"title": "<PERSON><PERSON><PERSON><PERSON> parda<PERSON> kanalus", "description": "Pridėkite pardavimo kanalus, kurie bus prieinami su šiuo API raktu.", "successToast_one": "{{count}} pardavimo kanalas sėkmingai pridėtas prie API rakto.", "successToast_other": "{{count}} pardavi<PERSON> kanalai sėkmingai pridėti prie API rakto.", "alreadyAddedTooltip": "Pardavimo kanalas jau pridėtas prie API rakto.", "list": {"noRecordsMessage": "API raktas neturi priskirtų pardavimo kanalų."}}, "delete": {"warning": "Ketinate pašalinti API raktą {{title}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "successToast": "API raktas {{title}} sėkmingai p<PERSON>šalintas."}, "revoke": {"warning": "<PERSON><PERSON><PERSON> atšaukti API raktą {{title}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "successToast": "API raktas {{title}} sėkmingai atšauktas."}, "addSalesChannels": {"list": {"noRecordsMessage": "Pirmiausia sukurkite pardavimo kanalą."}}, "removeSalesChannel": {"warning": "Ketinate pašalinti pardavimo kanalą {{name}}, priskirtą API raktui. Šis veiksmas neatstatomas.", "warningBatch_one": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} parda<PERSON><PERSON> ka<PERSON>, priskirtą API raktui. Šis veiksmas neatstatomas.", "warningBatch_other": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>i {{count}} parda<PERSON><PERSON> kanal<PERSON>, priskirtus API raktui. Šis veiksmas neatstatomas.", "successToast": "Pardavimo kanalas sėkmingai p<PERSON>šalintas nuo API rakto.", "successToastBatch_one": "{{count}} pardavimo kanalas sėkmingai p<PERSON>šalintas nuo API rakto.", "successToastBatch_other": "{{count}} parda<PERSON><PERSON> kanalass sėkmingai p<PERSON>šalintas nuo API rakto."}, "actions": {"revoke": "Atšaukti API raktą", "copy": "Kopijuoti API raktą", "copySuccessToast": "API raktas nukopijuotas į atmintį."}, "table": {"lastUsedAtHeader": "Paskutinį kartą naudotas", "createdAtHeader": "<PERSON><PERSON>uk<PERSON>"}, "fields": {"lastUsedAtLabel": "Paskutinį kartą naudotas", "revokedByLabel": "Atšaukė", "revokedAtLabel": "<PERSON><PERSON>uk<PERSON>", "createdByLabel": "Sukūrė"}}, "returnReasons": {"domain": "Grąžinimo priežastys", "subtitle": "Tvarkykite prekių grąžinimo priežastis.", "calloutHint": "Tvarkykite prekių grą<PERSON><PERSON><PERSON> priežastis, kad suskirtyti grąžinimus į kategorijas.", "editReason": "Keisti grąžinimo priežastį", "create": {"header": "Pridėti grąžinimo priežastį", "subtitle": "Nurodykite dažniausias grąžinimo priežastis.", "hint": "Sukurkite prekių grąžinimo priežastį, kad suskirtyti grąžinimus į kategorijas.", "successToast": "Grąžinimo priežastis {{label}} sėkmingai sukurta."}, "edit": {"header": "Keisti grąžinimo priežastį", "subtitle": "Keiskite grąžinimo priežasties reikšmę.", "successToast": "Grąžinimo priežastis {{label}} sėkmingai pakeista."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> p<PERSON>šalinti grąžinimo priežastį {{label}}. <PERSON><PERSON> veiksma<PERSON> neatstatomas.", "successToast": "Grąžinimo priežastis {{label}} sėkmingai p<PERSON>šalinta."}, "fields": {"value": {"label": "Reikš<PERSON><PERSON>", "placeholder": "klaid<PERSON>s_dydis", "tooltip": "<PERSON><PERSON><PERSON> t<PERSON>tų būti unikalus grąžinimo priežasties identifikatorius."}, "label": {"label": "Antraštė", "placeholder": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>"}, "description": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> gavo netinkamą dydį"}}}, "login": {"forgotPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį? - <0>Atstatyti</0>", "title": "Sveiki atvykę į Medusa", "hint": "Prisijunkite, kad pasiektumėte paskyrą"}, "invite": {"title": "Sveiki atvykę į Medusa", "hint": "Sukurkite savo paskyrą žemiau", "backToLogin": "Atgal į prisijungimą", "createAccount": "Sukurti paskyrą", "alreadyHaveAccount": "Jau turite paskyrą? - <0>Prisijunkite</0>", "emailTooltip": "Jūsų el. pašto adresas negali būti pakeistas. Jei norite naudoti kitą el. pašto adres<PERSON>, turite gauti naują kvietimą.", "invalidInvite": "Kvietimas neteisingas arba pasibaigęs.", "successTitle": "Jūsų paskyra sukurta", "successHint": "Pradėkite naudotis Medusa administravimo aplinka nedel<PERSON>.", "successAction": "Paleisti Medusa administravimo aplinką", "invalidTokenTitle": "Jūsų kvietimo prieigos raktas neteisingas", "invalidTokenHint": "Pabandykite paprašyti naujos kvietimo nuorodos.", "passwordMismatch": "Slptažodžiai nesutampa", "toast": {"accepted": "Kvietimas sėkmingai p<PERSON>imtas"}}, "resetPassword": {"title": "Slap<PERSON>žod<PERSON><PERSON> atstatymas", "hint": "Įveskite savo el. pa<PERSON><PERSON> ad<PERSON> ir atsiųsime instrukcijas, kaip iš naujo nustatyti slaptažodį.", "email": "El. <PERSON>", "sendResetInstructions": "Siųsti atstatymo instrukcijas", "backToLogin": "<0>Atgal į prisijungimą</0>", "newPasswordHint": "Pasirinkite naują slaptažodį.", "invalidTokenTitle": "Jūsų atkūrimo prieigos raktas neteisingas", "invalidTokenHint": "Pabandykite užsakyti naują slaptažodžio atstatymo nuorodą.", "expiredTokenTitle": "Jūsų atkūrimo prieigos rakto gal<PERSON> bai<PERSON>", "goToResetPassword": "Eiti į slaptažodžio atstatymą", "resetPassword": "Atstatyti slaptažodį", "newPassword": "<PERSON><PERSON><PERSON>", "repeatNewPassword": "Pakartokite naują slaptažodį", "tokenExpiresIn": "Prieigos rakto gal<PERSON> baigsis už <0>{{time}}</0> minučių", "successfulRequestTitle": "Sėkmingai išsiuntėme jums el. laiš<PERSON>ą", "successfulRequest": "Išsiuntėme jums el. <PERSON>, kurį galite naudoti naujo slaptažodžio nustatymui. Patikrinkite šlamš<PERSON> a<PERSON>, jei po kelių minučių jo negavote.", "successfulResetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>s", "successfulReset": "Prašome prisijungti prisijungimo pusla<PERSON>.", "passwordMismatch": "Slaptažodžiai nesutampa", "invalidLinkTitle": "Jū<PERSON>ų slaptažodžio keitimo nuoroda neteisinga", "invalidLinkHint": "Pabandykite iš naujo atstatyti slaptažodį."}, "workflowExecutions": {"domain": "<PERSON><PERSON> e<PERSON>s", "subtitle": "Peržiūrėkite ir sekite darbo eigų vykdymą savo Medusa programoje.", "transactionIdLabel": "Transakcijos ID", "workflowIdLabel": "Darbo eigos ID", "progressLabel": "Progresas", "stepsCompletedLabel_one": "{{completed}} iš {{count}} <PERSON><PERSON><PERSON>", "stepsCompletedLabel_other": "{{completed}} iš {{count}} <PERSON><PERSON><PERSON><PERSON>", "list": {"noRecordsMessage": "Jokia darbo eiga dar nebuvo atlikta."}, "history": {"sectionTitle": "Istorija", "runningState": "Vykdoma...", "awaitingState": "<PERSON><PERSON><PERSON>", "failedState": "Nepavyko", "skippedState": "P<PERSON>ei<PERSON>", "skippedFailureState": "Praleista (nepavyko)", "definitionLabel": "Apibrėžimas", "outputLabel": "<PERSON><PERSON><PERSON><PERSON>", "compensateInputLabel": "Kompensuo<PERSON> įvestį", "revertedLabel": "Atstatyta", "errorLabel": "<PERSON><PERSON><PERSON>"}, "state": {"done": "Atlikta", "failed": "Nepavyko", "reverted": "Atstatyta", "invoking": "<PERSON><PERSON><PERSON><PERSON>", "compensating": "Kompensuojama", "notStarted": "Nepradėta"}, "transaction": {"state": {"waitingToCompensate": "<PERSON><PERSON><PERSON> kompen<PERSON>"}}, "step": {"state": {"skipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skippedFailure": "<PERSON><PERSON><PERSON><PERSON><PERSON> (nepavyko)", "dormant": "Neveikiantis", "timeout": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "productTypes": {"domain": "Prekių tipai", "subtitle": "Suskirstykite savo prekes į tipus.", "create": {"header": "Sukurti prekių tipą", "hint": "Sukurkite prekių tipą prekių skirstymui.", "successToast": "Prekių tipas {{value}} sėkmingai sukurtas."}, "edit": {"header": "Keisti prekių tipą", "successToast": "Prekių tipas {{value}} sėkmingai pakeistas."}, "delete": {"confirmation": "<PERSON><PERSON><PERSON> pašalinti prekių tipą {{value}}. <PERSON><PERSON> ve<PERSON> neat<PERSON>.", "successToast": "Prekių tipas {{value}} sėkmingai p<PERSON>šalintas."}, "fields": {"value": "Reikš<PERSON><PERSON>"}}, "productTags": {"domain": "Prekių žymos", "create": {"header": "Sukurti prekių žymą", "subtitle": "Sukurkite prekių žymą prekių skirstymui.", "successToast": "Prekių žyma {{value}} sėkmingai sukurta."}, "edit": {"header": "Keisti prekių žymą", "subtitle": "Keiskite prekių žymos re<PERSON>ę.", "successToast": "Prekių žyma {{value}} sėkmingai pakeista."}, "delete": {"confirmation": "Ke<PERSON>te pašalinti prekių žymą {{value}}. <PERSON><PERSON> ve<PERSON>.", "successToast": "Preki<PERSON> žyma {{value}} sėkmingai p<PERSON>šalinta."}, "fields": {"value": "Reikš<PERSON><PERSON>"}}, "notifications": {"domain": "Pranešimai", "emptyState": {"title": "Pranešimų nėra", "description": "Šiuo metu neturite jokių p<PERSON>, bet kai tik gausite, jie bus <PERSON>."}, "accessibility": {"description": "Čia bus pateikti pranešimai apie Medusa veiklą"}}, "errors": {"serverError": "Serverio klaida - bandykite dar kartą vėliau.", "invalidCredentials": "Klaidingas el. pašto adresas arba slap<PERSON>žodis"}, "statuses": {"scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "expired": "Negalioja", "active": "Aktyvus", "inactive": "Neaktyvus", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": "Įjungta", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "labels": {"productVariant": "<PERSON><PERSON><PERSON><PERSON> variantas", "prices": "<PERSON><PERSON>", "available": "<PERSON><PERSON>", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "added": "<PERSON><PERSON><PERSON><PERSON>", "removed": "<PERSON><PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON>", "to": "<PERSON><PERSON>", "beaware": "Žinokite, kad", "loading": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"amount": "<PERSON><PERSON>", "refundAmount": "Grąžinama suma", "name": "Pavadinimas", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "Pa<PERSON><PERSON>", "firstName": "Vardas", "title": "Pavadinimas", "customTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pavadinimas", "manageInventory": "<PERSON><PERSON><PERSON><PERSON>", "inventoryKit": "<PERSON><PERSON> rinkinį", "inventoryItems": "Atsargos", "inventoryItem": "Atsargos", "requiredQuantity": "Re<PERSON>lingas kiekis", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "El. <PERSON>", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repeatPassword": "Pakartokite slaptažodį", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "newPassword": "<PERSON><PERSON><PERSON>", "repeatNewPassword": "Pakartokite naują slaptažodį", "categories": "<PERSON><PERSON><PERSON><PERSON>", "shippingMethod": "<PERSON><PERSON><PERSON>", "configurations": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "conditions": "<PERSON>ąly<PERSON>", "category": "Kategorija", "collection": "Kolekcija", "discountable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handle": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Paantraštė", "by": "", "item": "Prekė", "qty": "kiekis.", "limit": "Limitas", "tags": "<PERSON><PERSON><PERSON>", "type": "Tipas", "reason": "Priežastis", "none": "jokie", "all": "visi", "search": "Pa<PERSON>š<PERSON>", "percentage": "Procentas", "sales_channels": "Pardavimų kanalai", "customer_groups": "Klientų grupės", "product_tags": "Prekių žymos", "product_types": "Prekių tipai", "product_collections": "Preki<PERSON> kolekcijos", "status": "<PERSON><PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON>", "value": "Reikš<PERSON><PERSON>", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamic": "<PERSON><PERSON><PERSON><PERSON>", "normal": "Normalus", "years": "Metai", "months": "Mėnesiai", "days": "<PERSON><PERSON>", "hours": "Valandos", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "totalRedemptions": "Bendras išpirkimas", "countries": "<PERSON><PERSON><PERSON>", "paymentProviders": "Mokėjimo paslaugų teikėjai", "refundReason": "Pinigų grąžinimo priežastis", "fulfillmentProviders": "Užsakymo vykdytojai", "fulfillmentProvider": "Užsakymo vykdytojas", "providers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "availability": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": "Atsargos", "optional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "note": "Pastaba", "automaticTaxes": "Automatiniai mokesčiai", "taxInclusivePricing": "Kainodara su įskaičiuotais mokes<PERSON>is", "currency": "Valiuta", "address": "<PERSON><PERSON><PERSON>", "address2": "Butas, apartamentai, kt.", "city": "Miestas", "postalCode": "<PERSON><PERSON><PERSON> k<PERSON>", "country": "<PERSON><PERSON>", "state": "Valstija", "province": "Provincija", "company": "Įmonė", "phone": "Telefonas", "metadata": "Met<PERSON><PERSON><PERSON><PERSON>", "selectCountry": "Pasirinkite šalį", "products": "Prek<PERSON><PERSON>", "variants": "<PERSON><PERSON><PERSON>", "orders": "Užsakymai", "account": "Paskyra", "total": "Užsakymo viso", "paidTotal": "<PERSON><PERSON><PERSON>", "totalExclTax": "Viso be m<PERSON>čių", "subtotal": "Ta<PERSON><PERSON><PERSON> suma", "shipping": "Siuntimas", "outboundShipping": "Pristat<PERSON><PERSON>", "returnShipping": "Grąžini<PERSON> si<PERSON>", "tax": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON>", "date": "Data", "order": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fulfillment": "<PERSON><PERSON><PERSON><PERSON>", "provider": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Mokė<PERSON><PERSON>", "items": "Prek<PERSON><PERSON>", "salesChannel": "<PERSON><PERSON><PERSON><PERSON> kanalai", "region": "Regionas", "discount": "<PERSON><PERSON><PERSON><PERSON>", "role": "Rolė", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "salesChannels": "<PERSON><PERSON><PERSON><PERSON> kanalai", "product": "Prekė", "createdAt": "Sukurta", "updatedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revokedAt": "<PERSON><PERSON><PERSON><PERSON>", "true": "<PERSON><PERSON>", "false": "Ne", "giftCard": "Dovan<PERSON> kortelė", "tag": "<PERSON><PERSON><PERSON>", "dateIssued": "Išdavimo data", "issuedDate": "Išdavimo data", "expiryDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "price": "<PERSON><PERSON>", "priceTemplate": "<PERSON><PERSON> {{regionOrCurrency}}", "height": "<PERSON><PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "length": "<PERSON><PERSON>", "weight": "<PERSON><PERSON><PERSON>", "midCode": "MID kodas", "hsCode": "HS kodas", "ean": "EAN", "upc": "UPC", "inventoryQuantity": "Atsargų likutis", "barcode": "<PERSON><PERSON>ūk<PERSON><PERSON><PERSON> k<PERSON>", "countryOfOrigin": "<PERSON><PERSON><PERSON><PERSON>", "material": "Medžiaga", "thumbnail": "Miniatūra", "sku": "SKU", "managedInventory": "Valdomos atsargos", "allowBackorder": "Leisti užsakyti kai n<PERSON>ra", "inStock": "<PERSON><PERSON><PERSON><PERSON>", "location": "Sandėliavimo vieta", "quantity": "<PERSON><PERSON><PERSON>", "variant": "<PERSON><PERSON><PERSON>", "id": "ID", "parent": "<PERSON><PERSON><PERSON>", "minSubtotal": "Mažiausia tarpinė suma", "maxSubtotal": "Didžiausia tarpinė suma", "shippingProfile": "<PERSON><PERSON><PERSON>", "summary": "<PERSON><PERSON><PERSON>", "details": "Nustatymai", "label": "Antraštė", "rate": "<PERSON><PERSON><PERSON><PERSON>", "requiresShipping": "Reikalingas siunt<PERSON>", "unitPrice": "<PERSON><PERSON><PERSON> kaina", "startDate": "Pradžios data", "endDate": "Pabaigos data", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dateTime": {"years_one": "Metai", "years_other": "Metai", "months_one": "M<PERSON><PERSON><PERSON>", "months_other": "Mėnesiai", "weeks_one": "Savaitė", "weeks_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "days_one": "<PERSON><PERSON>", "days_other": "<PERSON><PERSON>", "hours_one": "<PERSON><PERSON>", "hours_other": "Valandos", "minutes_one": "Minutė", "minutes_other": "<PERSON><PERSON><PERSON><PERSON>", "seconds_one": "Se<PERSON>nd<PERSON>", "seconds_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}